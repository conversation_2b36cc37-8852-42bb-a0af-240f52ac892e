import React, { useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  Switch,
  TextField,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material/styles';
import { v4 as uuidv4 } from 'uuid';
import StarIcon from '@mui/icons-material/Star';
import StarOutlineIcon from '@mui/icons-material/StarOutline';
import AddIcon from '@mui/icons-material/Add';
import Close from '@mui/icons-material/Close';
import { ContactField, CambianTooltip } from '@cambianrepo/ui';
import { MaskTextField } from './MaskTextField';

const useStyles = (theme) => ({
  icons: {
    color: theme.palette.primary.main,
    fontSize: '20px',
  },
  closeIcon: {
    fontSize: '20px',
  },
  starButton: {
    paddingRight: 0,
  },
});

const EMAIL_REGEX_PATTERN = new RegExp('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$');
const MOBILE_REGEX_PATTERN = new RegExp(/^\+\d{1,3}\s+[\d\s()-]*(\d[\d\s()-]*){10}$/);

function ContactPanel(props) {
  const { t } = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);

  const { userDetail, updateProfileDataCallback, handleVerifyOtp, singleContactInfo = false } = props;
  const [userEmailList, setUserEmailList] = React.useState(
    singleContactInfo ? userDetail.emailAddress || '' : userDetail?.emailAddresses || [],
  );

  const [userPhoneList, setUserPhoneList] = React.useState(
    singleContactInfo ? userDetail.phoneNumber || '' : userDetail?.phoneNumbers || [],
  );

  const [emailErrorMessages, setEmailErrorMessages] = React.useState([]);
  const [phoneErrorMessages, setPhoneErrorMessages] = React.useState([]);
  const [subscribeToNotifications, setSubscribeToNotifications] = React.useState(userDetail?.subscribeToNotifications);
  const [preferredContactMechanism, setPreferredContactMechanism] = React.useState(
    userDetail?.preferredContactMechanism ?? '',
  );
  const [notificationFrequency, setNotificationFrequency] = React.useState(
    userDetail?.communicationPreferences?.notificationFrequency ?? '',
  );

  useEffect(() => {
    if (!singleContactInfo) {
      if (userEmailList.length === 0) {
        handleAddNewEmail(); // Adds one email entry on mount
      }
      if (userPhoneList.length === 0) {
        handleAddNewPhone(); // Adds one phone entry on mount
      }
    }
  }, []);

  const handleEmailChange = (index, event, key) => {
    const value = event.target.value.trim();
    let errorMessage = '';

    if (singleContactInfo) {
      setUserEmailList(value);
      updateProfileDataCallback('property', 'update', 'emailAddress', value);

      if (!value) {
        errorMessage = 'Email is a required field.';
      } else if (!EMAIL_REGEX_PATTERN.test(value)) {
        errorMessage = 'Please enter a valid email address.';
      }
    } else {
      const updatedEmailList = [...userEmailList];
      updatedEmailList[index][key] = value;
      setUserEmailList(updatedEmailList);
      updateProfileDataCallback('emailList', 'update', 'email', updatedEmailList);

      if (value && !EMAIL_REGEX_PATTERN.test(value)) {
        errorMessage = 'Please enter a valid email address.';
      }
    }

    const updatedErrorMessages = [...emailErrorMessages];
    updatedErrorMessages[index] = errorMessage;
    setEmailErrorMessages(updatedErrorMessages);
  };

  const handlePhoneChange = (index, event, key) => {
    const value = event.target.value.trim();
    let errorMessage = '';

    const phoneRegex = singleContactInfo ? /^\+[1-9][0-9]{9,12}$/ : MOBILE_REGEX_PATTERN;

    if (singleContactInfo) {
      setUserPhoneList(value);
      updateProfileDataCallback('property', 'update', 'phoneNumber', value);
    } else {
      const updatedPhoneList = [...userPhoneList];
      updatedPhoneList[index][key] = value;
      setUserPhoneList(updatedPhoneList);
      updateProfileDataCallback('phoneList', 'update', 'phone', updatedPhoneList);
    }

    // Validate phone number
    if (value && !phoneRegex.test(value)) {
      errorMessage = 'Please enter a valid phone number.';
    }

    const updatedErrorMessages = [...phoneErrorMessages];
    updatedErrorMessages[index] = errorMessage;
    setPhoneErrorMessages(updatedErrorMessages);
  };

  const handleTogglePrimaryEmail = (index) => {
    if (singleContactInfo) return;
    const updatedEmailList = [...userEmailList];
    updatedEmailList.forEach((email, i) => {
      email.primary = i === index; // Mark only the clicked email as primary
    });

    setUserEmailList(updatedEmailList);
    updateProfileDataCallback('emailList', 'update', 'email', updatedEmailList);
  };

  const handleTogglePrimaryPhone = (index) => {
    if (singleContactInfo) return;
    const updatedPhoneList = [...userPhoneList];
    updatedPhoneList.forEach((phone, i) => {
      phone.primary = i === index; // Mark only the clicked phone as primary
    });

    setUserPhoneList(updatedPhoneList);
    updateProfileDataCallback('phoneList', 'update', 'phone', updatedPhoneList);
  };

  const handleRemoveEmail = (index) => {
    if (singleContactInfo) return;
    const updatedEmailList = userEmailList.filter((_, i) => i !== index); // Remove the email at the given index
    setUserEmailList(updatedEmailList);
    updateProfileDataCallback('emailList', 'delete', 'email', index);

    if (userEmailList[index].primary && updatedEmailList.length > 0) {
      updatedEmailList[0].primary = true;
    }
    updateProfileDataCallback('emailList', 'update', 'email', updatedEmailList);
  };

  const handleRemovePhone = (index) => {
    if (singleContactInfo) return;
    const updatedPhoneList = userPhoneList.filter((_, i) => i !== index); // Remove the phone at the given index
    setUserPhoneList(updatedPhoneList);
    updateProfileDataCallback('phoneList', 'delete', 'phone', index);

    if (userPhoneList[index].primary && updatedPhoneList.length > 0) {
      updatedPhoneList[0].primary = true;
    }
    updateProfileDataCallback('phoneList', 'update', 'phone', updatedPhoneList);
  };

  const handleAddNewEmail = () => {
    if (singleContactInfo) return;
    const newEmail = {
      id: uuidv4(),
      emailAddress: '',
      note: null,
      primary: userEmailList.length === 0, // First email is primary by default
      verified: false,
    };

    const updatedEmailList = [...userEmailList, newEmail];

    // Ensure only one email is primary
    if (newEmail.primary) {
      updatedEmailList.forEach((email) => {
        if (email.id !== newEmail.id) {
          email.primary = false;
        }
      });
    }

    setUserEmailList(updatedEmailList);
  };

  const handleAddNewPhone = () => {
    if (singleContactInfo) return;
    const newPhone = {
      id: uuidv4(),
      phoneNumber: '',
      note: null,
      primary: userPhoneList.length === 0, // First phone is primary by default
      verified: false,
    };

    const updatedPhoneList = [...userPhoneList, newPhone];

    // Ensure only one phone is primary
    if (newPhone.primary) {
      updatedPhoneList.forEach((phone) => {
        if (phone.id !== newPhone.id) {
          phone.primary = false;
        }
      });
    }

    setUserPhoneList(updatedPhoneList);
  };

  const handleSubscribeToNotificationsChange = (event) => {
    updateProfileDataCallback('property', 'change', 'subscribeToNotifications', event.target.checked);
    setSubscribeToNotifications(event.target.checked);
  };

  const handlePreferredContactMechanismChange = (event) => {
    const selectedValue = event.target.value;
    setPreferredContactMechanism(selectedValue);
    updateProfileDataCallback('property', 'change', 'preferredContactMechanism', selectedValue);
  };

  const handleNotificationFrequencyChange = (event) => {
    const selectedValue = event.target.value;
    setNotificationFrequency(selectedValue);
    updateProfileDataCallback('property', 'change', 'notificationFrequency', selectedValue);
  };

  const handleNoneClick = (type) => {
    if (type === 'preferredContactMechanism') {
      setPreferredContactMechanism('');
      updateProfileDataCallback('property', 'change', 'preferredContactMechanism', '');
    } else if (type === 'notificationFrequency') {
      setNotificationFrequency('');
      updateProfileDataCallback('property', 'change', 'notificationFrequency', '');
    }
  };

  const handleVerification = (index, type, value, onSuccess) => {
    let updatedVerifiedList;

    if (type === 'Email') {
      updateProfileDataCallback('property', 'change', 'sendEmailAddressOTP', value);
    } else if (type === 'Phone') {
      updateProfileDataCallback('property', 'change', 'sendPhoneNumberOTP', value);
    }
    onSuccess();
  };

  const handleVerify = (index, type, value, otp, onSuccess) => {
    const verificationType = type === 'Email' ? 'emailAddress' : 'phoneNumber';
    const payload = { [verificationType]: value, otp };

    props.handleVerifyOtp(payload, (otpResult) => {
      console.log(`${type} OTP Verification Result:`, otpResult);

      if (otpResult?.result == 'VERIFIED') {
        const updatedList = type === 'Email' ? [...userEmailList] : [...userPhoneList];
        updatedList[index].verified = true;

        if (type === 'Email') {
          setUserEmailList(updatedList);
          updateProfileDataCallback('emailList', 'update', 'email', updatedList);
        } else if (type === 'Phone') {
          setUserPhoneList(updatedList);
          updateProfileDataCallback('phoneList', 'update', 'phone', updatedList);
        }
      }

      onSuccess(otpResult);
    });
  };

  const hasValidEmail = () => {
    return userEmailList.some((email) => email.emailAddress && EMAIL_REGEX_PATTERN.test(email.emailAddress));
  };

  const hasValidPhone = () => {
    return userPhoneList.some((phone) => phone.phoneNumber && MOBILE_REGEX_PATTERN.test(phone.phoneNumber));
  };

  return (
    <Grid item xs={12} sm={10} md={6} lg={5} sx={{ marginTop: !singleContactInfo ? '-16px' : '0px' }}>
      {/* Email List */}
      {singleContactInfo ? (
        <TextField
          label="Email"
          value={userEmailList}
          onChange={(e) => handleEmailChange(0, e, 'emailAddress')}
          error={!!emailErrorMessages[0]}
          helperText={emailErrorMessages[0]}
          disabled
          required
          fullWidth
        />
      ) : (
        userEmailList.map((email, index) => (
          <ContactField
            key={`email-${index}`}
            contact={userEmailList}
            index={index}
            label="Email"
            type="email"
            value={email.emailAddress}
            allowMultiple={true}
            onChange={(e) => handleEmailChange(index, e, 'emailAddress')}
            isReadOnly={false}
            otpVerificationEnabled={true}
            handleVerificationClick={({ type, value, onSuccess }) => handleVerification(index, type, value, onSuccess)}
            error={!!emailErrorMessages[index]}
            helperText={emailErrorMessages[index]}
            isPrimary={email.primary}
            onSetPrimary={() => handleTogglePrimaryEmail(index)}
            onDelete={() => handleRemoveEmail(index)}
            isVerified={email.verified}
            CambianTooltip={CambianTooltip}
            handleVerify={({ type, value, otp, onSuccess }) => handleVerify(index, type, value, otp, onSuccess)}
          />
        ))
      )}

      {!singleContactInfo && (
        <Button
          size="medium"
          color="primary"
          onClick={handleAddNewEmail}
          startIcon={<AddIcon />}
          fullWidth
          sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
        >
          Add Email
        </Button>
      )}

      {/* Phone List */}
      {singleContactInfo ? (
        <Stack spacing={1} sx={{ marginTop: 2 }}>
          <TextField
            label="Phone"
            value={userPhoneList}
            onChange={(e) => handlePhoneChange(0, e, 'phoneNumber')}
            error={!!phoneErrorMessages[0]}
            helperText={phoneErrorMessages[0]}
            disabled
            fullWidth
          />
        </Stack>
      ) : (
        userPhoneList.map((phone, index) => (
          <ContactField
            key={`phone-${index}`}
            contact={userPhoneList}
            index={index}
            label="Phone"
            type="phone"
            value={phone.phoneNumber}
            allowMultiple={true}
            onChange={(e) => handlePhoneChange(index, e, 'phoneNumber')}
            isReadOnly={false}
            otpVerificationEnabled={true}
            handleVerificationClick={({ type, value, onSuccess }) => handleVerification(index, type, value, onSuccess)}
            error={!!phoneErrorMessages[index]}
            helperText={phoneErrorMessages[index]}
            isPrimary={phone.primary}
            onSetPrimary={() => handleTogglePrimaryPhone(index)}
            onDelete={() => handleRemovePhone(index)}
            isVerified={phone.verified}
            CambianTooltip={CambianTooltip}
            handleVerify={({ type, value, otp, onSuccess }) => handleVerify(index, type, value, otp, onSuccess)}
            InputProps={{
              inputComponent: MaskTextField,
              inputProps: {
                mask: '(###) ###-####',
                definitions: {
                  '#': /[0-9]/,
                },
              },
            }}
          />
        ))
      )}

      {!singleContactInfo && (
        <Button
          size="medium"
          color="primary"
          onClick={handleAddNewPhone}
          startIcon={<AddIcon />}
          fullWidth
          sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
        >
          Add Phone
        </Button>
      )}

      {/* Notifications when toggled have Preferred Contact Mechanism, and Notification Frequency */}
      {!singleContactInfo && (
        <Box display="flex" flexDirection="column" style={{ padding: 0 }}>
          <FormControlLabel
            control={<Switch checked={subscribeToNotifications} onChange={handleSubscribeToNotificationsChange} />}
            label={t('Notifications')}
            labelPlacement="start"
            sx={{
              marginTop: 2,
              display: 'flex',
              justifyContent: 'start',
              alignItems: 'center',
              width: 'auto',
            }}
          />
          {subscribeToNotifications && (
            <Box>
              <FormControl size="small" variant="outlined" sx={{ marginTop: 2, width: '100%' }}>
                <InputLabel id="preferred-contact-label" htmlFor="preferred-contact-select">
                  {t('Preferred Contact Method')}
                </InputLabel>
                <Select
                  labelId="preferred-contact-label"
                  value={preferredContactMechanism || ''}
                  onChange={handlePreferredContactMechanismChange}
                  label={t('Preferred Contact Method')}
                >
                  {preferredContactMechanism !== '' && (
                    <MenuItem value="" onClick={() => handleNoneClick('preferredContactMechanism')}>
                      <em>None</em>
                    </MenuItem>
                  )}
                  {hasValidEmail() && <MenuItem value="Email">{t('Email')}</MenuItem>}
                  {hasValidPhone() && <MenuItem value="Phone">{t('Phone')}</MenuItem>}
                </Select>
              </FormControl>
              <FormControl size="small" variant="outlined" sx={{ marginTop: 2, width: '100%' }}>
                <InputLabel id="notification-frequency-label" htmlFor="notification-frequency-select">
                  {t('Notification Frequency')}
                </InputLabel>
                <Select
                  labelId="notification-frequency-label"
                  value={notificationFrequency || ''}
                  onChange={handleNotificationFrequencyChange}
                  label={t('Notification Frequency')}
                >
                  {notificationFrequency !== '' && (
                    <MenuItem value="" onClick={() => handleNoneClick('notificationFrequency')}>
                      <em>None</em>
                    </MenuItem>
                  )}
                  <MenuItem value="INSTANTLY">{t('Instantly')}</MenuItem>
                  <MenuItem value="WEEKLY">{t('Weekly')}</MenuItem>
                </Select>
              </FormControl>
            </Box>
          )}
        </Box>
      )}
    </Grid>
  );
}

export default ContactPanel;
