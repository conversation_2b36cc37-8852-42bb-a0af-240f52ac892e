import React from 'react';
import { Avatar, Tooltip } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useMediaQuery } from '@mui/material';

function AccountSettings(props) {
  const { firstName, lastName, photoImageUrl, photoImage, email } = props;
  const theme = useTheme();
  const greaterThanMid = useMediaQuery(theme.breakpoints.up('md'));

  const userInitials = () => {
    let initials;
    if (firstName != null && lastName != null) {
      initials = firstName.charAt(0).toUpperCase() + lastName.charAt(0).toUpperCase();
    } else if (lastName != null) {
      initials = lastName.charAt(0).toUpperCase();
    } else if (firstName != null) {
      initials = firstName.charAt(0).toUpperCase();
    } else {
      initials = '?';
    }
    return initials;
  };

  const fullName = `${firstName || ''} ${lastName || ''}`.trim();
  const tooltipText = (
    <div>
      {fullName}
      <br />
      {email}
    </div>
  );

  const avatar = () => {
    if (photoImageUrl == null && photoImage == null) {
      return <Avatar alt={`${firstName} ${lastName}`}>{userInitials()}</Avatar>;
    }

    return <Avatar alt={`${firstName} ${lastName}`} src={photoImage ? photoImage : photoImageUrl} />;
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginTop: '8px' }}>
      <Tooltip title={tooltipText}>{avatar()}</Tooltip>
    </div>
  );
}

export { AccountSettings };
