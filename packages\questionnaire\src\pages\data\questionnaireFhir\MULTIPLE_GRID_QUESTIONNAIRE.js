export const MULTIPLE_GRID_QUESTIONNAIRE = {
  resourceType: 'Questionnaire',
  date: '2025-06-26T12:04:10.216Z',
  name: 'Test data grid Updated ',
  title: 'Test data grid Updated ',
  description: 'Test data grid Updated ',
  subjectType: 'Patient',
  extension: [
    {
      url: 'display-dial',
      valueBoolean: false,
    },
    {
      url: 'display-description',
      valueBoolean: true,
    },
    {
      url: 'display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'display-score',
      valueBoolean: false,
    },
    {
      url: 'display-score-category',
      valueBoolean: false,
    },
    {
      url: 'display-title',
      valueBoolean: true,
    },
    {
      url: 'questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'trendable',
      valueBoolean: false,
    },
    {
      url: 'pdftemplate-id',
      valueString: '',
    },
    {
      url: 'question-identifier-prefix',
      valueString: 'Item',
    },
    {
      url: 'codeBookHtmlData',
      valueString:
        '\n<h2 style="text-align: center;"><strong>General Data Format</strong></h2>\n<p>The data export file for questionnaire responses has the following characteristics:</p>\n<ol>\n<li data-list-text="1.">\n<p>The file format is &ldquo;csv&rdquo;(machine readable)</p>\n</li>\n<li data-list-text="2.">\n<p>One row for each questionnaire response (Long Format)</p>\n</li>\n<li data-list-text="3.">\n<p>Each row contains the following:</p>\n<ol style="list-style-type: lower-alpha;">\n<li data-list-text="a.">\n<p>An identifier for the questionnaire</p>\n</li>\n<li data-list-text="b.">\n<p>The group id of the group the participant is a member of</p>\n</li>\n<li data-list-text="c.">\n<p>The participant id of the user filling in the response</p>\n</li>\n<li data-list-text="d.">\n<p>The start date (YYYY-MM-DD hh:mm:ss) when the participant begins to work on the response</p>\n</li>\n<li data-list-text="e.">\n<p>The completion date (YYYY-MM-DD hh:mm:ss)</p>\n</li>\n<li data-list-text="f.">\n<p>Time spent in completing questionnaire (in seconds)</p>\n</li>\n<li data-list-text="g.">\n<p>The response for each item (Note: For multiple choice questions that allow multiple responses, each allowable response is turned into a item with the possible response of Yes or No. Free form text responses are put in double quotes (e.g.,”I experienced some pain”) so commas can be used inside the response value. Double quotes are escaped with another double quote (e.g.,”I experienced some “”phantom”” pain”). A skipped item will have the corresponding item response set to -99.)</p>\n</li>\n<li data-list-text="h.">\n<p>The computed scores if applicable (Note: if a score cannot be computed because of missing data, the corresponding field will be set to -99)</p>\n</li>\n</ol>\n</li>\n</ol><h1 style="text-align: center;">{Questionnaire.title}</h1>\n<h3>Questionnaire and Item mapping</h3>\n<p>Each questionnaire is given a unique numerical identifier and each item within a questionnaire is given a name that is used to define the columns used in the data export file.</p>\n<table style="border-collapse: collapse; width: 900px;">\n<tbody>\n<tr>\n<td style="width: 714px; border-style: solid; padding-left: 10px;">\n<p>{Questionnaire.title}</p>\n</td>\n<td style="width: 178px; border-style: solid; padding-left: 10px;">\n<p>{Questionnaire.id}</p>\n</td>\n</tr>\n</tbody>\n</table>\n<p>&nbsp;</p>\n{Questionnaire.mappedQuestionsList}\n<p>&nbsp;</p>\n<p>Each computed score within a questionnaire is given a name that is used to define the columns used in the data export file.</p>\n<table style="border-collapse: collapse; width: 900.359px;">\n<tbody>\n<tr>\n<td style="width: 714px; border-style: solid; padding-left: 10px;">\n<p>score</p>\n</td>\n<td style="width: 178px; border-style: solid; padding-left: 10px;">\n<p>S1</p>\n</td>\n</tr>\n</tbody>\n</table>\n<p>&nbsp;</p>\n<p>With the information above, columns that will be presented in an entry for {Questionnaire.title} are defined as:</p>\n<p>{Questionnaire.questionnaireColumns}</p>\n<p>&nbsp;</p>\n<h3>Item response mapping</h3>\n<p>Allowed responses for each item are shown below:</p>\n<p>&nbsp;</p>\n{Questionnaire.mappedResponseList}\n<p>&nbsp;</p>\n<h3>Sample Data</h3>\n<p>k45e7b06-1295-47f2-9577-d8e4d43c5333,Item1, Item2, Item3, S1</p>\n<p>m5187b06-8321-88i2-2342-h456w234l231,Item1, Item2, Item3, Item4, Item5, Item6, S1</p>\n',
    },
    {
      url: 'question-identifier-next-sequence',
      valueInteger: 6,
    },
    {
      url: 'htmltemplate-base64',
      valueString:
        '<h1>{Questionnaire.title}</h1>\n<p>Date: {QuestionnaireResponse.completionDate:format(YYYY-MM-DD HH:mm:ss)}</p>\n<p>Here is a list of items and responses:</p>\n{QuestionnaireResponse.itemsAndResponses}\n<p style="color:gray; font-size:11px;">{Questionnaire.description}</p>',
    },
    {
      url: 'pdftemplate-name',
      valueString: '',
    },
    {
      url: 'list-of-score-definitions',
      extension: [
        {
          url: 'score-id',
          valueCode: 'e5fb8e88-f979-4054-ad82-077fb6506d50',
        },
        {
          url: 'score-sequence',
          valueInteger: 0,
        },
        {
          url: 'score-name',
          valueString: 'NewVariable',
        },
        {
          url: 'list-of-formula-definitions',
          extension: [
            {
              extension: [
                {
                  url: 'formula-name',
                  valueString: 'NewVariable-F1',
                },
                {
                  url: 'mathematical-expression',
                  valueString: '',
                },
                {
                  url: 'selection-rule',
                  valueString: 'Select Rule',
                },
              ],
              url: 'set-of-api-formula',
            },
          ],
        },
      ],
    },
  ],
  identifier: [
    {
      use: 'old',
      system: 'questionnaire/identifier',
      value: 'd3ea065c-2d4f-4d38-b4c7-fc4b28d21f78',
      period: {
        start: '2023-12-13T14:06:06+00:00',
        end: '2023-12-13T14:15:33+00:00',
      },
    },
    {
      use: 'usual',
      system: 'urn:uuid',
      value: 'f6356947-59d8-495b-abf7-47973d3a1968',
      period: {
        start: '2023-12-13T14:06:06+00:00',
      },
    },
  ],
  item: [
    {
      linkId: 'Group1',
      item: [
        {
          id: '32iHjDSMkiASxRtA6DF4Tk',
          linkId: 'Item1',
          type: 'choice',
          text: 'RB',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          answerOption: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: 'YES',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: 'NO',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
          ],
        },
        {
          id: 'complex-oW6qEMs64ZkF9HzUfEKaMs',
          linkId: 'Item2',
          type: 'group',
          text: 'DG 1',
          item: [
            {
              id: '3rENDPLi6PuqGcrWv5NgdY',
              type: 'group',
              item: [
                {
                  id: '9We7R2baFuJHAFdZ3reAum',
                  linkId: '6GJh8SqyMcMiq4y8EvmhSa',
                  text: 'TEXT',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                },
                {
                  id: 'jFgsN6xwgPwRt4NG3QZoF1',
                  linkId: 'eLqJf8rjkTdtEQejEs8Kbk',
                  text: 'TEXT',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                  ],
                },
                {
                  id: '6oxm1pgTc5XiLF6UVhJwzf',
                  linkId: 'upZK9YBroGoEyTjtM49Rvv',
                  text: 'TEXT',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                  ],
                },
                {
                  id: 'p4TxC7p9offJe36j22fqUX',
                  linkId: '9nN1nrRt2R75Y3kNThiZTe',
                  text: 'NUM',
                  type: 'decimal',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 4,
                    },
                  ],
                },
                {
                  id: 'hVC7a8xyX2Spd2zk3BiH2W',
                  linkId: 'o73jxFb95MEmrhUSZEd4gK',
                  text: 'NUM',
                  type: 'decimal',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 5,
                    },
                  ],
                },
              ],
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          enableWhen: [
            {
              operator: '=',
              answerString: '1',
              question: 'Item1',
            },
          ],
        },
      ],
      id: 'group-8566ny7CLZQUM8xWADpGMS',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
    },
    {
      linkId: 'Group2',
      item: [
        {
          id: 'upy1DqqLw18RS1uJpC9kZs',
          linkId: 'Item4',
          type: 'choice',
          text: 'CB',
          answerOption: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: '1',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: '2',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
        },
        {
          id: 'complex-96hjwNFSHD5BnUtJdP1LcV',
          linkId: 'Item5',
          type: 'group',
          text: 'DG2',
          item: [
            {
              id: '7caN6mhL9pEQC5cPxRK6ne',
              type: 'group',
              item: [
                {
                  id: 'veFRTvb66TZsebyextk8oo',
                  linkId: '17N4e32AkN2qdixB2LrGJP',
                  text: 'Date',
                  type: 'dateTime',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                },
                {
                  id: 'bFr3mm4wVSbnCshdMZKP9y',
                  linkId: '13vKpPXTUv6ubjsnmsQGD1',
                  text: 'Date',
                  type: 'dateTime',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                  ],
                },
                {
                  id: 'pZfYXfckQ73c3LGeZW9us4',
                  linkId: 'a9ByjdiCrD6aFBPiP721Hc',
                  text: 'Num',
                  type: 'decimal',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                  ],
                },
                {
                  id: 'uBnnyYYvmBU7sdVMrLopG1',
                  linkId: '41TvSaQBuBLBZipuZpnhMW',
                  text: 'Text',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 4,
                    },
                  ],
                },
              ],
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          enableWhen: [
            {
              operator: '=',
              answerString: '1',
              question: 'Item4',
            },
          ],
        },
      ],
      id: 'group-nvvFC4vMF1vfThamCBE1mt',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 2,
        },
      ],
    },
  ],
  publisher: 'App-Scoop',
  status: 'draft',
  id: 'b654a960-9467-482f-9ecb-6c83c8b3cc7b',
  url: 'Questionnaire/b654a960-9467-482f-9ecb-6c83c8b3cc7b',
};
