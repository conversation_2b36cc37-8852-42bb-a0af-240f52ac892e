import React, { useState, useEffect } from 'react';
import {
  Button,
  Grid,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  IconButton,
  Box,
} from '@mui/material';
import { v4 as uuidv4 } from 'uuid';
import { useTheme } from '@mui/material/styles';
import AddIcon from '@mui/icons-material/Add';
import Close from '@mui/icons-material/Close';
import StarIcon from '@mui/icons-material/Star';
import StarOutlineIcon from '@mui/icons-material/StarOutline';

const useStyles = (theme) => ({
  icons: {
    color: theme.palette.primary.main,
    fontSize: '20px',
  },
  closeIcon: {
    fontSize: '20px',
  },
  starButton: {
    paddingRight: 0,
  },
});

function IdentificationPanel(props) {
  const { idTypesAndIssuers } = props;
  const theme = useTheme();
  const styles = useStyles(theme);
  const [userIdentificationList, setUserIdentificationList] = useState(props.userDetail.healthCareIds || []);

  useEffect(() => {
    if (userIdentificationList.length === 0) {
      handleAddIdentification(); // Adds one identification entry on mount
    }
  }, []);

  const handleTextFieldChange = (index, event, key) => {
    const updatedIdentificationList = [...userIdentificationList];
    updatedIdentificationList[index][key] = event.target.value;
    setUserIdentificationList(updatedIdentificationList);
    props.updateProfileDataCallback('property', 'update', 'healthCareIds', updatedIdentificationList);
  };

  const handleAddIdentification = () => {
    const newIdentification = {
      id: uuidv4(),
      type: '',
      issuer: '',
      value: '',
      primary: userIdentificationList.length === 0, // First identification is primary by default
    };

    const updatedIdentificationList = [...userIdentificationList, newIdentification];

    // Ensure only one identification is primary
    if (newIdentification.primary) {
      updatedIdentificationList.forEach((identification) => {
        if (identification.id !== newIdentification.id) {
          identification.primary = false;
        }
      });
    }

    setUserIdentificationList(updatedIdentificationList);
  };

  const handleTogglePrimary = (index) => {
    const updatedIdentificationList = [...userIdentificationList];
    updatedIdentificationList.forEach((identification, i) => {
      identification.primary = i === index; // Mark only the clicked identification as primary
    });

    setUserIdentificationList(updatedIdentificationList);
    props.updateProfileDataCallback('property', 'update', 'healthCareIds', updatedIdentificationList);
  };

  const handleRemoveIdentification = (index) => {
    const updatedIdentificationList = userIdentificationList.filter((_, i) => i !== index);
    setUserIdentificationList(updatedIdentificationList);
    props.updateProfileDataCallback('property', 'delete', 'healthCareIds', index);

    // Check if the item being removed is the primary one
    if (userIdentificationList[index].primary) {
      if (updatedIdentificationList.length > 0) {
        updatedIdentificationList[0].primary = true; // Set the first item to primary
      }
    }
    props.updateProfileDataCallback('property', 'update', 'healthCareIds', updatedIdentificationList);
  };

  const handleNoneClick = (index, field) => {
    const updatedData = {
      ...userIdentificationList,
      [index]: {
        ...userIdentificationList[index],
        [field]: '',
      },
    };
  };

  return (
    <Grid item xs={12} sm={10} md={6} lg={5} style={{ padding: 0 }}>
      {/* Identification List */}
      {userIdentificationList.map((identification, index) => (
        <Stack spacing={1} key={index}>
          <Box display="flex" alignItems="center" style={{ padding: 0 }}>
            {/* Identification Issuer */}
            <FormControl size="small" sx={{ width: '161px', mt: index !== 0 ? '32px' : 0 }}>
              <InputLabel id={`issuer-label-${index}`}>Issuer</InputLabel>
              <Select
                labelId={`issuer-label-${index}`}
                id={`issuer-select-${index}`}
                value={identification.issuer}
                onChange={(e) => handleTextFieldChange(index, e, 'issuer')}
                label="Issuer"
              >
                {identification.issuer !== '' && (
                  <MenuItem value="" onClick={handleNoneClick(index, 'issuer')}>
                    <em>None</em>
                  </MenuItem>
                )}
                {Object.keys(idTypesAndIssuers).map((issuer, idx) => (
                  <MenuItem key={idx} value={issuer}>
                    {issuer}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Identification Type */}
            <FormControl size="small" sx={{ ml: 1, width: '161px', mt: index !== 0 ? '32px' : 0 }}>
              <InputLabel id={`id-type-label-${index}`}>Type</InputLabel>

              {identification.issuer !== '' ? (
                <Select
                  labelId={`id-type-label-${index}`}
                  id={`id-type-select-${index}`}
                  value={identification.type}
                  onChange={(e) => handleTextFieldChange(index, e, 'type')}
                  label="Type"
                >
                  {identification.type !== '' && (
                    <MenuItem value="" onClick={handleNoneClick(index, 'type')}>
                      <em>None</em>
                    </MenuItem>
                  )}
                  {identification.issuer && idTypesAndIssuers[identification.issuer] ? (
                    <MenuItem value={idTypesAndIssuers[identification.issuer]}>
                      {idTypesAndIssuers[identification.issuer]}
                    </MenuItem>
                  ) : (
                    <MenuItem disabled />
                  )}
                </Select>
              ) : (
                <Select
                  labelId={`id-type-label-${index}`}
                  id={`id-type-select-${index}`}
                  value={''}
                  onChange={(e) => handleTextFieldChange(index, e, 'type')}
                  label="Type"
                ></Select>
              )}
            </FormControl>

            {/* ID Value Input */}
            <TextField
              label="Value"
              value={identification.value}
              onChange={(e) => handleTextFieldChange(index, e, 'value')}
              sx={{ ml: 1, width: '162px', mt: index !== 0 ? '32px' : 0 }}
            />

            {/* Primary Star and Close Icon */}
            <Grid sx={{ mt: index !== 0 ? '32px' : 0 }}>
              <IconButton onClick={() => handleTogglePrimary(index)} sx={styles.starButton}>
                {identification.primary ? <StarIcon sx={styles.icons} /> : <StarOutlineIcon sx={styles.icons} />}
              </IconButton>
              <IconButton onClick={() => handleRemoveIdentification(index)} sx={styles.starButton}>
                <Close sx={styles.closeIcon} />
              </IconButton>
            </Grid>
          </Box>
        </Stack>
      ))}

      {/* Button to Add Additional Identification */}
      <Button
        style={{ marginTop: 0 }}
        size="medium"
        color="primary"
        onClick={handleAddIdentification}
        startIcon={<AddIcon />}
        fullWidth
        sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
      >
        Add ID
      </Button>
    </Grid>
  );
}

export default IdentificationPanel;
