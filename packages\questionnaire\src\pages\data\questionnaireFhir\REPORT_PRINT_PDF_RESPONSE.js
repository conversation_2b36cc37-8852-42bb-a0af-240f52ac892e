export const REPORT_PRINT_PDF_RESPONSE = {
  resourceType: 'QuestionnaireResponse',
  identifier: [],
  questionnaire: 'Questionnaire/3e32b86f-4eb5-4740-88e0-ebc851954003',
  status: 'completed',
  authored: '2024-07-08T18:56:53.838Z',
  extension: [
    {
      url: 'questionnaire-response-type',
      valueCode: 'instrument-response',
    },
    {
      url: 'questionnaire-name',
      valueString: 'RegressionTesting',
    },
    {
      url: 'questionnaire-title',
      valueString: 'Regression Test',
    },
    {
      url: 'calculated-scores',
      extension: [],
    },
  ],
  item: [
    {
      id: 'group-3QaowpQamQjwQp53yt6snz',
      linkId: 'Group1',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
        {
          url: 'Item/question-type',
          valueString: 'group',
        },
      ],
      item: [
        {
          id: 'hmQqq7UwLZNgAkmJ9STFYy',
          linkId: 'Item2',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/integer-only',
              valueBoolean: true,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/min-value',
              valueDecimal: '10',
            },
            {
              url: 'Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'integer',
            },
          ],
          text: 'Int',
          answer: [
            {
              valueInteger: '25',
            },
          ],
          type: 'integer',
        },
        {
          id: 'gvDcFxY1shE8qZJP2XgDkv',
          linkId: 'Item4',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Item/min-length',
              valueInteger: 0,
            },
            {
              url: 'Item/max-length',
              valueInteger: 1024,
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Item/description',
              valueString: 'description ',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'text',
            },
          ],
          text: 'para',
          answer: [
            {
              valueString:
                '12fdjkf \nda\nfjakhf\nafa<gfh hfkdhsafiu dk jdhfkladh f89435ui jkd jfiua 98faukfdkaf98ea fak f9eu fu',
            },
          ],
          type: 'text',
        },
      ],
      type: 'group',
    },
    {
      id: 'group-pQZUiv4gYwD3u6eC2ddG3p',
      linkId: 'Group2',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 2,
        },
        {
          url: 'Item/question-type',
          valueString: 'group',
        },
      ],
      item: [
        {
          id: 'p6oKoXTqqcAef2EoX1qsVH',
          linkId: 'Item6',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'checkbox',
          answer: [
            {
              valueCoding: {
                id: 2,
                sequence: 3,
                display: '30',
                code: 3,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
              },
            },
          ],
          type: 'choice',
        },
        {
          id: 'ikNiDEkbZGj7R7wX5kT41s',
          linkId: 'Item7',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5543,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'dropdown menu',
          answer: [
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: '2',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
          ],
          type: 'choice',
        },
        {
          id: 'a9L1J1KhRJ6PoZnWRqDUkB',
          linkId: 'Item8',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/display-large-buttons',
              valueBoolean: true,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'ERROR',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'LB',
          answer: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: '1',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
          ],
          type: 'choice',
        },
      ],
      type: 'group',
    },
    {
      id: 'group-jq5BL9kjjMckYKSQGnH4Ue',
      linkId: 'Group3',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 3,
        },
        {
          url: 'Item/question-type',
          valueString: 'group',
        },
      ],
      item: [
        {
          id: 'aQnnak8fDXECizv35bEkuP',
          linkId: 'Item9',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/description',
              valueString: 'date and time',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Item/question-type',
              valueString: 'date',
            },
          ],
          text: 'date ',
          answer: [
            {
              valueDate: '2024-07-10',
            },
          ],
          type: 'date',
        },
      ],
      type: 'group',
    },
    {
      id: 'group-1e9Yagfcs3ejzsXYERWips',
      linkId: 'Group4',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 4,
        },
        {
          url: 'Item/question-type',
          valueString: 'group',
        },
      ],
      item: [
        {
          id: 'bTL7bL5sAuANwiRkujqbTQ',
          linkId: 'Item10',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/display-type',
              valueString: 'choice-bar',
            },
            {
              url: 'Item/bar-start-label',
              valueString: '-',
            },
            {
              url: 'Item/bar-end-label',
              valueString: '+',
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'ERROR',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'linear scale ',
          answer: [
            {
              valueCoding: {
                id: 'sCxXAxLNgaZH4kobDkKdjh',
                code: 0,
                display: 0,
              },
            },
          ],
          type: 'choice',
        },
        {
          id: 'th9qCfHuU3BXo3xZ7fJpWq',
          linkId: 'Item11',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5013,
            },
            {
              url: 'Item/display-type',
              valueString: 'numeric-slider',
            },
            {
              url: 'Item/slider-min-value',
              valueDecimal: 5,
            },
            {
              url: 'Item/slider-max-value',
              valueDecimal: 6,
            },
            {
              url: 'Item/slider-min-label',
              valueString: 'min',
            },
            {
              url: 'Item/slider-max-label',
              valueString: 'max',
            },
            {
              url: 'Item/slider-steps',
              valueDecimal: 10,
            },
            {
              url: 'Item/slider-min-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/slider-max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-type',
              valueString: 'integer',
            },
          ],
          text: 'numeric slider',
          answer: [
            {
              valueInteger: 5,
            },
          ],
          type: 'integer',
        },
        {
          id: 'uaqWSjNeXg1WrV5nT2BjbV',
          linkId: 'Item12',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Item/display-type',
              valueString: 'choice-image',
            },
            {
              url: 'Item/image-path',
              valueString: '/api/media/body_diagram.jpg',
            },
            {
              url: 'Item/image-width',
              valueInteger: 795,
            },
            {
              url: 'Item/image-height',
              valueInteger: 698,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'body dig',
          answer: [
            {
              valueCoding: {
                id: '574401',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '168,49,168,43,168,39,169,34,171,29,173,26,181,20,193,15,200,14,206,14,214,15,222,18,233,27,237,36,239,41,239,47,239,49,236,51,231,52,226,52,222,53,214,53,207,53,200,53,189,53,182,53,174,52,170,50|594,10,592,10,584,11,580,12,575,14,571,17,567,21,565,23,562,27,560,31,559,34,559,39,558,43,558,49,558,53,558,56,555,57,552,59,552,61,551,65,552,68,553,71,554,73,556,75,557,80,557,82,558,84,561,84,563,85,563,89,565,94,566,97,567,100,567,102,568,105,571,107,575,107,583,107,586,107,593,107,599,107,607,107,616,106,618,101,620,97,622,94,623,89,624,86,625,85,629,83,632,77,633,74,635,70,635,65,636,63,636,60,635,58,633,57,632,57,630,57,630,54,630,38,629,33,628,29,626,26,625,23,622,19,616,17,608,13,604,12',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '1',
                display: 'Head',
              },
            },
          ],
          type: 'choice',
        },
        {
          id: 'complex-mTzp9th8oLzpyVfAaRr6ta',
          linkId: 'Item13',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/description',
              valueString: 'decription',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-type',
              valueString: 'group',
            },
          ],
          text: 'data grid',
          item: [
            {
              item: [
                {
                  id: 0,
                  linkId: '3CK2SDMmUiKGqGjxGrgxTD',
                  text: 'name',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'text',
                    },
                  ],
                  answer: [
                    {
                      valueString: 'fdafa',
                    },
                  ],
                },
              ],
            },
          ],
          type: 'group',
        },
      ],
      type: 'group',
    },
  ],
  text: {
    status: 'generated',
    div: 'root <html xmlns="http://www.w3.org/1999/xhtml"><head><style>[data-visible=\'false\'] {  display: none;}</style></head><body><h1>Regression Test</h1><p>Date: 2024-07-09 00:26:53</p><p>Here is a list of items and responses:</p>{QuestionnaireResponse.itemsAndResponses}<p style="color:gray; font-size:11px;">Regression Testing for Questionnaire and Questionnaire Editor</p></body></html>',
  },
  id: 'e302dbd2-80a3-48cf-b8d4-118c5ff41dcc',
};
