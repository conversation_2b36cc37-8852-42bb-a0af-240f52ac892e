const AnswerQuestionTypeMap = {
  SINGLE_ANSWER: '',
  MULTIPLE_ANSWER: 'choice',
  DISPLAY_ONLY: 'display',
  NUMERIC: 'decimal',
  INTEGER: 'integer',
};

/*
setupVariableForEval helps in storing the questions data and QR data and returns the key.
*/
const setupVariableForEval = (scoringResponse, questionnaire, mEvaluator, expressionEngine) => {
  try {
    const AnswerQuestionTypes = Object.values(AnswerQuestionTypeMap);
    const ExcludedAnswerQuestionTypes = ['display'];
    let questionGroupData = questionnaire.item || [];
    let questionAnswerUnits = scoringResponse.item || [];

    // get all questions data
    const allQuestionsData = questionGroupData.reduce((r, c) => {
      c &&
        c.item &&
        c.item.forEach((item) => {
          r.push(item);
        });
      return r;
    }, []);

    let questionAnsweredMap = {};
    let questionIdentifiers = [];

    for (let i = 0; i < questionGroupData.length; i++) {
      let questionUnit = questionGroupData[i];

      if (questionUnit.type === 'group') {
        const questions = questionUnit.item || [];

        for (let j = 0; j < questions.length; j++) {
          let questionInGroupFhirLinkId = questions[j].linkId;
          const questionType = questions[j].type;
          // exclude display question types
          if (ExcludedAnswerQuestionTypes.indexOf(questionType) === -1) {
            questionAnsweredMap[questionInGroupFhirLinkId] = '0';
            questionIdentifiers.push(questionInGroupFhirLinkId);

            mEvaluator[questionInGroupFhirLinkId] = '0';
          }
        }
      }
    }

    for (let i = 0; i < questionAnswerUnits.length; i++) {
      let questionUnit = questionAnswerUnits[i];

      const questionResponse = questionUnit.item || [];

      for (let j = 0; j < questionResponse.length; j++) {
        let questionInGroupFhirLinkId = questionResponse[j].linkId;
        const quesId = questionResponse[j].id;
        let answers = questionResponse[j].answer;

        if (answers && answers.length === 0) {
          continue;
        }

        let questionIdentifier = '';
        const reqQuestion = allQuestionsData.filter((ques) => {
          return quesId === ques.id;
        })[0];

        if (reqQuestion) {
          questionIdentifier = reqQuestion.linkId;
          const answerQuestionTypeEnum = reqQuestion.type;

          // process non-display question types
          if (answerQuestionTypeEnum !== AnswerQuestionTypeMap.DISPLAY_ONLY) {
            if (answers && answers.length === 0) {
              console.error('Error while processing answers');
            } else {
              const idToMCResponseValueMap = {};
              let scoreValueForResponse = 0;
              const answerOptions = reqQuestion && reqQuestion.answerOption;
              answerOptions &&
                answerOptions.forEach((option) => {
                  const optionId = option.valueCoding.id;
                  idToMCResponseValueMap[optionId] = option.valueCoding;
                });

              answers &&
                answers.forEach((ans) => {
                  const ansId = ans && ans.valueCoding && ans.valueCoding.id;
                  if (ansId !== null && ansId !== undefined) {
                    if (reqQuestion.type === AnswerQuestionTypeMap.MULTIPLE_ANSWER) {
                      if (idToMCResponseValueMap[ansId]) {
                        let questionMultipleChoiceResponseValue = idToMCResponseValueMap[ansId];
                        const scoreValue = questionMultipleChoiceResponseValue.code;
                        scoreValueForResponse += parseInt(scoreValue);
                      }
                    } else if (reqQuestion.type === AnswerQuestionTypeMap.NUMERIC) {
                      if (ans.valueCoding.code) {
                        scoreValueForResponse = ans.valueCoding.code;
                      } else if (ans.valueDecimal !== undefined) {
                        scoreValueForResponse = ans.valueDecimal;
                      }
                    } else if (reqQuestion.type === AnswerQuestionTypeMap.INTEGER) {
                      if (ans.valueInteger !== undefined) {
                        scoreValueForResponse = ans.valueInteger;
                      }
                    }
                  } else if (ans.valueDecimal !== undefined) {
                    scoreValueForResponse = ans.valueDecimal;
                  } else if (ans.valueInteger !== undefined) {
                    scoreValueForResponse = ans.valueInteger;
                  }
                });
              scoreValueForResponse = isNaN(scoreValueForResponse) ? 0 : scoreValueForResponse;
              mEvaluator[questionIdentifier] = scoreValueForResponse?.toString();

              questionAnsweredMap[questionIdentifier] = '1';
            }
          }
        } else {
          console.log('Null question: question not found via for ', questionInGroupFhirLinkId);
        }
      }
    }

    let key = '';
    questionIdentifiers.forEach((identifier) => {
      key += questionAnsweredMap[identifier] || '0';
    });
    return { key, mEvaluator };
  } catch (err) {
    console.error('Error in setupVariableForEval: ', err);
  }
};

const getFormulas = (scoringResponse, questionnaire) => {
  const listOfScoreDefs = questionnaire.extension.filter((ext) => ext.url.includes('list-of-score-definitions'));

  const listOfFormulaDefs = listOfScoreDefs.map((scoreDef) => {
    const scoreName = scoreDef.extension.find((formulaDef) => {
      return formulaDef.url.includes('score-name');
    }).valueString;

    let f = scoreDef.extension.find((formulaDef) => {
      return formulaDef.url.includes('list-of-formula-definitions');
    }).extension;

    f =
      Array.isArray(f) &&
      f.reduce((r, c) => {
        c['scoreName'] = scoreName;
        r.push(c);
        return r;
      }, []);

    return f;
  });

  const listOfApiFormula = listOfFormulaDefs.reduce((r, c) => {
    const formulae =
      Array.isArray(c) &&
      c
        .filter((f) => f.url.includes('set-of-api-formula'))
        .map((f) => {
          const formula = {};
          f.extension.forEach((ext) => {
            if (ext.url.includes('formula-name')) {
              formula['name'] = ext.valueString;
            } else if (ext.url.includes('mathematical-expression')) {
              formula['expression'] = ext.valueString;
            } else if (ext.url.includes('selection-rule')) {
              formula['rule'] = ext.valueString;
            }
            formula['scoreName'] = f.scoreName;
          });
          return formula;
        });

    try {
      r.push(...formulae);
    } catch (e) {
      console.log(e);
    }
    return r;
  }, []);

  return { listOfFormulaDefs, listOfApiFormula };
};

const evaluateExp = (selectionRule, mEvaluator) => {
  let res = Object.keys(mEvaluator).reduce((r, c) => {
    const tStr = r.replaceAll(`#{${c}}`, mEvaluator[c]);
    r = tStr;
    return r;
  }, selectionRule);

  res = res.replaceAll(/(\n | \s+)/g, '');
  return res;
};

const decorateWithScores = (questionnaire, scoringResponse) => {
  let questionAnswerUnits = scoringResponse.item || [];
  let breakCalculation = false;
  let mEvaluator = {};
  const expressionEngine = {};

  let missedScoreCount = 0;

  let { key: selectionKey, mEvaluator: updatedEvaluator } = setupVariableForEval(
    scoringResponse,
    questionnaire,
    mEvaluator,
    expressionEngine,
  );

  mEvaluator = updatedEvaluator;

  let scoreDefinitionIterator = getFormulas(scoringResponse, questionnaire)?.listOfFormulaDefs;

  if (questionAnswerUnits.length === 0) {
    const tmpformulae = [];
    for (let k = 0; k < scoreDefinitionIterator.length; k++) {
      const scoredefs = scoreDefinitionIterator[k];

      Array.isArray(scoredefs) &&
        scoredefs.forEach((def) => {
          let tmpformula = {};
          def.extension.forEach((ext) => {
            if (ext.url.includes('formula-name')) {
              tmpformula['name'] = ext.valueString;
            } else if (ext.url.includes('mathematical-expression')) {
              tmpformula['expression'] = ext.valueString;
            } else if (ext.url.includes('selection-rule')) {
              tmpformula['rule'] = ext.valueString;
            }
            tmpformula['scoreName'] = def.scoreName;
          });
          tmpformulae.push(tmpformula);
        });
    }
    const defaultFormaula = tmpformulae.find((f) => f.scoreName === 'default');
    const finalScoreJson = {
      url: 'calculated-scores',
      extension: [],
    };
    if (defaultFormaula) {
      finalScoreJson.extension.push({
        url: 'score',
        valueString: `${defaultFormaula.scoreName}:${defaultFormaula.expression}`,
      });
    }
    const scoringResponseCopy = JSON.parse(JSON.stringify(scoringResponse));
    scoringResponseCopy.extension.push(finalScoreJson);
    return scoringResponseCopy;
  }

  for (let k = 0; k < scoreDefinitionIterator.length; k++) {
    if (breakCalculation) {
      break;
    }
    const scoredefs = scoreDefinitionIterator[k];
    const tmpformulae = [];
    Array.isArray(scoredefs) &&
      scoredefs.forEach((def) => {
        let tmpformula = {};
        def.extension.forEach((ext) => {
          if (ext.url.includes('formula-name')) {
            tmpformula['name'] = ext.valueString;
          } else if (ext.url.includes('mathematical-expression')) {
            tmpformula['expression'] = ext.valueString;
          } else if (ext.url.includes('selection-rule')) {
            tmpformula['rule'] = ext.valueString;
          }
          tmpformula['scoreName'] = def.scoreName;
        });

        tmpformulae.push(tmpformula);
      });

    let formulaIterator = tmpformulae;

    let formula = null;
    let found = false;

    for (let i = 0; i < formulaIterator.length; i++) {
      const formulaData = formulaIterator[i];

      formula = formulaData;

      let sbSelectionKey = selectionKey;
      let rule = formulaData.rule;
      let ruleComponents = rule != null ? rule.split('$') : null;

      if (ruleComponents != null) {
        if (ruleComponents[0] === 'VAR_COMPARE') {
          if (ruleComponents.length == 1) {
            found = true;
          } else if (ruleComponents.length == 2) {
            let selectionRule = ruleComponents[1];
            try {
              selectionRule = selectionRule
                .replaceAll('pow(', 'Math.pow(')
                .replaceAll('log(', 'Math.log(')
                .replaceAll('exp(', 'Math.exp(')
                .replaceAll('sqrt(', 'Math.sqrt(')
                .replaceAll('sin(', 'Math.sin(')
                .replaceAll('cos(', 'Math.cos(')
                .replaceAll('tan(', 'Math.tan(')
                .replaceAll('ceil(', 'Math.ceil(')
                .replaceAll('equals(', 'Math.equals(')
                .replaceAll('random(', 'Math.random(')
                .replaceAll('replace(', 'Math.replace(')
                .replaceAll('floor(', 'Math.floor(');
              let ruleMatchingResult = evaluateExp(selectionRule, mEvaluator, expressionEngine);
              ruleMatchingResult = eval(ruleMatchingResult);

              if (ruleMatchingResult) {
                found = true;
              } else {
                found = false;
              }
            } catch (err) {
              console.error(err);
            }
          } else {
            found = false;
          }
        } else if (ruleComponents[0] === 'NUM_Q_EQ') {
          if (ruleComponents.length == 3) {
            let numberOfQuestionAnsweredShouldBe = parseInt(ruleComponents[1]);
            let sbFormulaSelectionRule = ruleComponents[2];
            let index = sbFormulaSelectionRule.indexOf('X');
            while (index >= 0) {
              sbSelectionKey = sbSelectionKey.slice(0, index) + 'X' + sbSelectionKey.slice(index + 1);
              index = sbFormulaSelectionRule.indexOf('X', index + 1);
            }

            let key = sbSelectionKey.replaceAll('X', '');
            key = key.replaceAll('0', '');

            if (key.length == numberOfQuestionAnsweredShouldBe) {
              found = true;
            } else {
              found = false;
            }
          } else {
            console.error('Wrong format');
          }
        } else {
          let sbFormulaSelectionRule = formulaData.rule;
          let index = sbFormulaSelectionRule.indexOf('X');
          while (index >= 0) {
            sbSelectionKey = sbSelectionKey.slice(0, index) + 'X' + sbSelectionKey.slice(index + 1);
            index = sbFormulaSelectionRule.indexOf('X', index + 1);
          }
          if (sbSelectionKey === sbFormulaSelectionRule) {
            found = true;
          } else {
            found = false;
          }
        }
      }

      if (found) {
        break;
      }
    }

    if (!found) {
      console.trace('Selection rule is not satisfied, skipping evaluation.');
      continue;
    }

    let formulaMathExpr = formula ? formula.expression : '';

    formulaMathExpr = formulaMathExpr
      .replaceAll('pow(', 'Math.pow(')
      .replaceAll('log(', 'Math.log(')
      .replaceAll('exp(', 'Math.exp(')
      .replaceAll('sqrt(', 'Math.sqrt(')
      .replaceAll('sin(', 'Math.sin(')
      .replaceAll('cos(', 'Math.cos(')
      .replaceAll('tan(', 'Math.tan(')
      .replaceAll('ceil(', 'Math.ceil(')
      .replaceAll('equals(', 'Math.equals(')
      .replaceAll('random(', 'Math.random(')
      .replaceAll('replace(', 'Math.replace(')
      .replaceAll('floor(', 'Math.floor(');

    if (typeof formulaMathExpr === 'string' && formulaMathExpr && formulaMathExpr.length > 0) {
      let result = evaluateExp(formulaMathExpr, mEvaluator, expressionEngine);
      try {
        result = eval(result);
        if (isNaN(result)) {
          console.error('Selection rule not satisfied', formula.scoreName);
        }
        if (result || result !== undefined || !isNaN(result)) {
          expressionEngine[formula.scoreName] = result?.toFixed(2).toString();
          mEvaluator[formula.scoreName] = Number(result);
        }
      } catch (err) {
        console.log(err);
      }
    } else {
      let result = evaluateExp(formulaMathExpr, mEvaluator, expressionEngine);
      try {
        result = eval(result);
        let score = null;
        if (typeof result === 'number') {
          expressionEngine[formula.scoreName] = result?.toFixed(2).toString();
        } else {
          expressionEngine[formula.scoreName] = result?.toFixed(2);
        }
        mEvaluator[formula.scoreName] = Number(result);
      } catch (err) {
        console.error(err);
      }
    }
  }

  const finalScoreJson = {
    url: 'calculated-scores',
    extension: [],
  };

  !breakCalculation &&
    Object.keys(expressionEngine).forEach((key) => {
      if (expressionEngine[key] && !isNaN(expressionEngine[key])) {
        finalScoreJson.extension.push({
          url: 'score',
          valueString: `${key}:${expressionEngine[key]}`,
        });
      }
    });

  const scoringResponseCopy = JSON.parse(JSON.stringify(scoringResponse));

  scoringResponseCopy.extension.push(finalScoreJson);
  return scoringResponseCopy;
};

export { decorateWithScores };
