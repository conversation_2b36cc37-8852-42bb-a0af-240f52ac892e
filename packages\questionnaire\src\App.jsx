import { CssB<PERSON><PERSON>, ThemeProvider } from '@mui/material';
import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import ComponentPlayground from './ComponentPlayground';
import { CambianTheme } from './assets/theme/CambianTheme';

function App() {
  return (
    <BrowserRouter>
      <ThemeProvider theme={CambianTheme}>
        <CssBaseline />
        <ComponentPlayground />
      </ThemeProvider>
    </BrowserRouter>
  );
}

export default App;
