import './App.css';
import { BrowserRouter } from 'react-router-dom';
import ComponentPlayground from './ComponentPlayground';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { CambianTheme } from './lib/components';

//
// This is the playground app.  Each component should have a page for testing
//

function App() {
  return (
    <BrowserRouter basename="/">
      <ThemeProvider theme={CambianTheme}>
        <CssBaseline />
        <ComponentPlayground />
      </ThemeProvider>
    </BrowserRouter>
  );
}

export default App;
