export const updateUserDetails = (updatedData) => {
  console.log('Updating user details:', updatedData);
};

export const deleteAddress = (addressId) => {
  console.log('Deleting address with id:', addressId);
};

export const deleteHealthCareId = (healthCareId) => {
  console.log('Deleting health care ID with id:', healthCareId);
};

export const updateIcon = (fileName) => {
  if (!fileName) {
    try {
      console.log('Deleting Icon');
    } catch (error) {}
  } else {
    try {
      console.log('Updating Icon:', fileName);
    } catch (error) {}
  }
};

export const icon = {
  iconUrl:
    'https://cambian-devindividual-data.s3.ca-central-1.amazonaws.com/individual/9c1baa55-6611-46d8-9494-cac92c9d4f7e/icon?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=ASIA6GBMDDGOOLTWW5LV%2F20241216%2Fca-central-1%2Fs3%2Faws4_request&X-Amz-Date=20241216T191853Z&X-Amz-Expires=86400&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEGsaDGNhLWNlbnRyYWwtMSJHMEUCIQD8mUwrOtJtK80gv3GS5fIM882gUmhw8uUWH7uU%2FxNZYAIgRVT3ffTvJJjp5EYF%2F73JqcOcy3ChABe5CtvBLLrGO%2B0qmgMINBAAGgw5NzUwNTAwNTQwNDQiDMved06LNKo7OdCkNCr3AkZU8XgHO5yeDdlyi4pNOvtkSPL%2Fj1YI3er0Lkqkkgj5QUkQH76nDOxZHtlVs00aI3%2FOGkcOWxuDxBnWDQOvnE7zE9ZvEVMq%2BXVDN0dvQQoIZsAUZ11eDHF4S36zaXlnTo1hlhJtagaJrvZuFlEde5I%2Fb53dbgc73P67eKJxMgZhH8AH4wOJQ%2FqMeKScPCREfT2S7iq9%2FhhF9PKQnTGULBs1vIfK4GYOgRF274GNMO9Ew5FshUGlZHWL3EhQuygTknN0i8gVUzYqFs%2FugnUyE0A1VJd85%2F%2Fr3q%2FlqHWDOLJDN1Br0CEWZcBU0pXTCtQUWLaazqxyYWHiVPjrgLLTI7KujWb57tSidP206dDvCD1S75FXyXcbImlQX1Ll79M1jpByJU2%2Fnuy9cL%2BCXvtnppPY%2BLKSKNcHJRlcRHG8m4jRINGm2sHlV55Zi73c9wQN7vBeGe2tGf9K9eIhfR9uGviAkChRTTvXFSTF5YDv59wP7LGckIObzzCI%2BoG7BjqdAWHyUciMv1Qt77aTxFBg1XjrFv7%2Bgb81uAcr2zAxwvFaaaK46o%2B5B5qEZbGYKqut%2B9UTM35USGL3sbt%2FgIqa9WmrYidi%2BCQXRFVJ5ZGRvTAmdHRDqeci900eUXs5tBmHhToeGllwKjJzhk5BQoNpPsiSrPa0aJLy9xqoQ2yZg3LbTRUPTBUGLZ6UrLicd9hg5OYcuN9ymwRpu4hbj64%3D&X-Amz-Signature=d3e43ffc3260b78eb04a0b0a676485655dec1cf4998be3f0fe35c728fbe44b38&X-Amz-SignedHeaders=host&x-id=GetObject',
};

export const sendOtp = (contactMechanism) => {
  console.log('Sending OTP code to user: ', contactMechanism);
};

export const verifyOtp = async (contactMechanism, onSuccess) => {
  console.log('Verifying OTP code by user: ', contactMechanism);
  const otpResult = { result: 'VERIFIED' };
  if (otpResult?.result) {
    onSuccess(otpResult);
  }
  return otpResult;
};

export const countriesAndProvinces = {
  Canada: ['AB', 'BC', 'MB', 'NB', 'NL', 'NT', 'NS', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT'],
  USA: [
    'AL',
    'AK',
    'AZ',
    'AR',
    'CA',
    'CO',
    'CT',
    'DE',
    'FL',
    'GA',
    'HI',
    'ID',
    'IL',
    'IN',
    'IA',
    'KS',
    'KY',
    'LA',
    'ME',
    'MD',
    'MA',
    'MI',
    'MN',
    'MS',
    'MO',
    'MT',
    'NE',
    'NV',
    'NH',
    'NJ',
    'NM',
    'NY',
    'NC',
    'ND',
    'OH',
    'OK',
    'OR',
    'PA',
    'RI',
    'SC',
    'SD',
    'TN',
    'TX',
    'UT',
    'VT',
    'VA',
    'WA',
    'WV',
    'WI',
    'WY',
  ],
};

export const idTypesAndIssuers = {
  AB: 'PHN',
  BC: 'PHN',
  MB: 'PHN',
  NB: 'PHN',
  NL: 'PHN',
  NT: 'PHN',
  NS: 'PHN',
  NU: 'PHN',
  ON: 'PHN',
  PE: 'PHN',
  QC: 'PHN',
  SK: 'PHN',
  YT: 'PHN',
  Canada: 'IFH',
};

export const userDetails = {
  individualId: '9c1baa55-6611-46d8-9494-cac92c9d4f7e',
  dateOfBirth: '2025-02-04',
  firstName: 'Bob',
  middleName: null,
  lastName: 'Cambian',
  gender: null,
  preferredContactMechanism: null,
  subscribeToNotifications: true,
  emailAddress: '<EMAIL>',
  phoneNumber: null,
  emailAddresses: [
    {
      id: '0224e604-c8b4-46b3-9f28-5e69d0367e6e',
      emailAddress: '<EMAIL>',
      primary: true,
      note: null,
      verified: false,
    },
    {
      id: '79fda016-5f03-4dd6-8c87-98ff101be02e',
      emailAddress: '<EMAIL>',
      primary: false,
      note: null,
      verified: false,
    },
    {
      id: '40096888-a1f4-4268-9893-a61df760992a',
      emailAddress: '<EMAIL>',
      primary: false,
      note: null,
      verified: false,
    },
  ],
  phoneNumbers: [
    {
      id: '58ff8a3b-e6fe-4be3-9066-11790b09755f',
      phoneNumber: '+****************',
      primary: true,
      note: null,
      verified: false,
    },
    {
      id: '5c56beae-fcd0-4f29-a93e-122002719c7e',
      phoneNumber: '+****************',
      primary: false,
      note: null,
      verified: false,
    },
    {
      id: 'c7096c56-183f-4b82-b749-794225a7fa38',
      phoneNumber: '+****************',
      primary: false,
      note: null,
      verified: false,
    },
  ],
  addresses: [
    {
      id: 'dbc6ba26-541e-435d-9c54-fcc664063656',
      purpose: null,
      address1: '1690-13450 102 Ave',
      address2: null,
      city: 'Surrey',
      postalCode: 'V3T 5X3',
      primary: true,
      province: 'BC',
      country: 'Canada',
    },
  ],
  healthCareIds: [
    {
      id: '470ae7ee-6759-4f96-922e-12609566c530',
      type: 'PHN',
      value: '**********',
      issuer: 'AB',
      primary: true,
    },
  ],
  communicationPreferences: {
    sendEmail: false,
    sendSms: false,
    notificationFrequency: 'INSTANTLY',
  },
};
