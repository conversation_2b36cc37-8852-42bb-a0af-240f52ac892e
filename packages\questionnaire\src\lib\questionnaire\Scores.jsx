import React from 'react';
import { Grid, Typography } from '@mui/material';
import * as QuestionnaireUtility from './utility/questionnaireUtility';

function Scores(props) {
  const { instrumentScores, section } = props;

  const getScoreInDecimal = (_score, format) => {
    let score = parseFloat(_score);
    return score.toFixed(format.formatValue);
  };

  const getVariableFormatType = (variable, score) => {
    let prefixedScore = score;
    if (variable.formats && variable.formats.length) {
      variable.formats.forEach((format) => {
        if (format && format.formatName === 'decimalPlace' && format.showInReport === true) {
          prefixedScore = getScoreInDecimal(score, format);
        }
      });
      variable.formats.forEach((format) => {
        if (format && format.formatName !== 'decimalPlace' && format.showInReport === true) {
          if (format.formatName === 'currency') {
            return (prefixedScore = format.formatValue + prefixedScore);
          } else if (format.formatName === 'percent') {
            return (prefixedScore = prefixedScore + format.formatValue);
          }
        }
      });
    }
    return prefixedScore;
  };

  const getScoreValue = (scoreItem) => {
    let scoreValue = scoreItem.score;
    if (section && scoreItem) {
      if (section.variables && section.variables.length) {
        section.variables.forEach((variable) => {
          if (variable.variable === scoreItem.scoreDefinitionName) {
            scoreValue = getVariableFormatType(variable, scoreValue);
          }
        });
      }
    }

    return scoreValue;
  };

  return (
    <Grid container>
      {instrumentScores.map((item, index) => {
        if (section) {
          let scoreDefinitionName = QuestionnaireUtility.extractResultPageVariableName(
            section,
            item.scoreDefinitionName,
          );
          if (scoreDefinitionName) {
            return (
              <React.Fragment key={'ScoreRow' + index}>
                <Grid item key={'1stColumnOf' + index} xs={6} sm={6} md={6} sx={{ border: 1, borderColor: 'divider' }}>
                  <Typography sx={{ py: 1, px: 2 }}>{scoreDefinitionName}</Typography>
                </Grid>
                <Grid item key={'2ndColumnOf' + index} xs={6} sm={6} md={6} sx={{ border: 1, borderColor: 'divider' }}>
                  <Typography sx={{ py: 1, px: 2 }}>{getScoreValue(item)}</Typography>
                </Grid>
              </React.Fragment>
            );
          }
        } else {
          return (
            <React.Fragment key={'ScoreRow' + index}>
              <Grid item key={'1stColumnOf' + index} xs={6} sm={6} md={6} sx={{ border: 1, borderColor: 'divider' }}>
                <Typography sx={{ py: 1, px: 2 }}>{item.scoreDefinitionName}</Typography>
              </Grid>
              <Grid item key={'2ndColumnOf' + index} xs={6} sm={6} md={6} sx={{ border: 1, borderColor: 'divider' }}>
                <Typography sx={{ py: 1, px: 2 }}>{item.score}</Typography>
              </Grid>
            </React.Fragment>
          );
        }
      })}
    </Grid>
  );
}

export default Scores;
