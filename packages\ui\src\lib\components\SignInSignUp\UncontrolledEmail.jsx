import React, { useState, useEffect } from 'react';
import { FormControl, FormHelperText, InputLabel, OutlinedInput, useMediaQuery, Box } from '@mui/material';

const UncontrolledEmail = React.forwardRef((props, ref) => {
  const {
    id,
    labelName,
    autoFocus,
    placeholder,
    matchingEmail,
    setWidth,
    isRequired,
    isDisabled,
    labelFontSize = 16,
    inputFontSize = 17,
    inputBoxSize = 'medium',
    validSchema,
    defaultValue = '',
    onKeyDown,
  } = props;

  const [value, setValue] = useState(defaultValue);
  const [isAutofilled, setIsAutofilled] = useState(false);
  const [focused, setFocused] = useState(false);
  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const isInvalid = Object.values(validSchema).some((v) => v === false);

  // Autofill Detection
  useEffect(() => {
    const inputElement = document.getElementById(id);
    if (!inputElement) return;

    const checkAutofill = () => {
      setIsAutofilled(inputElement.matches(':-webkit-autofill'));
    };

    const observer = new MutationObserver(checkAutofill);
    observer.observe(inputElement, { attributes: true, attributeFilter: ['value'] });

    checkAutofill();
    return () => observer.disconnect();
  }, [id]);

  const handleFocus = () => setFocused(true);
  const handleBlur = () => setFocused(false);

  const shrinkLabel = focused || isAutofilled || value;

  return (
    <>
      <FormControl
        onKeyDown={onKeyDown}
        sx={{ m: 0, width: setWidth }}
        size={inputBoxSize}
        variant="outlined"
        required={isRequired}
        disabled={isDisabled}
      >
        <InputLabel
          htmlFor={id}
          color={isInvalid ? 'error' : 'primary'}
          shrink={shrinkLabel}
          sx={{
            fontSize: labelFontSize,
            marginTop: isXs ? '2px' : undefined,
            color: isInvalid ? 'error.main' : undefined,
            backgroundColor: shrinkLabel ? 'white' : 'transparent',
            padding: shrinkLabel ? '0 10px' : undefined,
            transform: shrinkLabel ? 'translate(8px, -6px) scale(0.75)' : undefined,
          }}
        >
          {labelName}
        </InputLabel>
        <OutlinedInput
          id={id}
          error={isInvalid}
          autoFocus={autoFocus === 'true'}
          placeholder={placeholder || ''}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onFocus={handleFocus}
          onBlur={handleBlur}
          inputProps={{
            style: {
              fontSize: isXs ? inputFontSize.xs : inputFontSize.sm,
            },
          }}
          sx={{
            '& input:-webkit-autofill, & input:-webkit-autofill:focus, & input:-webkit-autofill:hover, & input:-webkit-autofill:first-line':
              {
                WebkitBoxShadow: '0 0 0 1000px white inset',
                WebkitTextFillColor: 'black',
                fontSize: inputFontSize,
              },
          }}
          inputRef={ref}
          defaultValue={defaultValue}
          label={isXs ? labelName.slice(0, labelName.length - 1) : labelName}
        />
      </FormControl>
      <EmailInvalidMessage validSchema={validSchema} matchingEmail={matchingEmail} />
    </>
  );
});

function EmailInvalidMessage(props) {
  const { validSchema, matchingEmail } = props;

  // validSchema's keys are optional and thus we need to specifically check if they are false.
  // If we check all falsy value, the keys that are undefined will also show error message which we do not want.
  if (validSchema.isMinLengthValid === false) {
    return (
      <Box sx={{ marginTop: '0.2px !important' }}>
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '0.2px' }}>
          Please enter an email address
        </FormHelperText>
      </Box>
    );
  }

  return (
    <Box sx={{ marginTop: '0px !important' }}>
      {validSchema.isEmailFormatValid === false && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '0.2px !important' }}>
          Please enter a valid email address
        </FormHelperText>
      )}
      {matchingEmail === false && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 } }}>
          Email addresses must match
        </FormHelperText>
      )}
    </Box>
  );
}

export { UncontrolledEmail };
