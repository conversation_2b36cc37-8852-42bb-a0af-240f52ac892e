import React from 'react';
import { Button } from '@mui/material';

function CambianSelectionButton(props) {
  const { variant, onClick, buttonText, isButtonSelected } = props;

  const getSlotButtonsStyle = (isButtonActive) => {
    if (isButtonActive) {
      const styleObject = {
        py: 1,
        px: { sm: 1.8, md: 5.5, lg: 8 },
        my: 0.5,
        backgroundColor: 'cambianCommon.lightGray',
        border: '1px solid',
        borderColor: '#000',
        '&:hover': {
          border: '1px solid',
          borderColor: '#000',
          backgroundColor: 'cambianCommon.lightGray',
        },
        color: 'cambianCommon.black',
        fontWeight: 'normal',
      };
      return styleObject;
    } else {
      const styleObject = {
        py: 1,
        px: { sm: 1.8, md: 5.5, lg: 8 },
        my: 0.5,
        backgroundColor: 'background.default',
        border: '1px solid',
        borderColor: 'cambianCommon.mediumGray1',
        color: 'text.primary',
        fontWeight: 'normal',
        '&:hover': {
          border: '1px solid',
          borderColor: 'cambianCommon.lightGray',
          backgroundColor: 'cambianCommon.lightGray',
        },
      };
      return styleObject;
    }
  };

  return (
    <Button variant={variant} sx={getSlotButtonsStyle(isButtonSelected)} onClick={onClick}>
      {buttonText}
    </Button>
  );
}

export { CambianSelectionButton };
