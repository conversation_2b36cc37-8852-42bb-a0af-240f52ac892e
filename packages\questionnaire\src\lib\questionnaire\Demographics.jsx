import React from 'react';
import { Grid, Typography } from '@mui/material';

function Demographics(props) {
  const { demographics } = props;

  const getFields = () => {
    let rows = [];
    for (var i = 0; i < demographics.length; i += 2) {
      rows.push(
        <React.Fragment key={'DemographicRow' + i}>
          <>
            <Grid
              item
              key={i}
              xs={12}
              sm={6}
              md={6}
              sx={{ border: 1, borderColor: 'divider', display: 'block', displayPrint: 'none' }}
            >
              <Typography sx={{ py: 1, px: 2 }}>
                {demographics[i].label}
                {demographics[i].value}
              </Typography>
            </Grid>
            <Grid
              item
              key={i + 1}
              xs={12}
              sm={6}
              md={6}
              sx={{ border: 1, borderColor: 'divider', display: 'block', displayPrint: 'none' }}
            >
              {demographics[i + 1] && (
                <Typography sx={{ py: 1, px: 2 }}>
                  {demographics[i + 1].label}
                  {demographics[i + 1].value}
                </Typography>
              )}
            </Grid>
            <Grid
              item
              key={`print_${i}`}
              xs={6}
              sm={6}
              md={6}
              sx={{ border: 1, borderColor: 'divider', display: 'none', displayPrint: 'block' }}
            >
              <Typography sx={{ py: 1, px: 2 }}>
                {demographics[i].label}
                {demographics[i].value}
              </Typography>
            </Grid>
            <Grid
              item
              key={`print_${i + 1}`}
              xs={6}
              sm={6}
              md={6}
              sx={{ border: 1, borderColor: 'divider', display: 'none', displayPrint: 'block' }}
            >
              {demographics[i + 1] && (
                <Typography sx={{ py: 1, px: 2 }}>
                  {demographics[i + 1].label}
                  {demographics[i + 1].value}
                </Typography>
              )}
            </Grid>
          </>
        </React.Fragment>,
      );
    }
    return rows;
  };

  return <Grid container>{demographics && getFields()}</Grid>;
}

export default Demographics;
