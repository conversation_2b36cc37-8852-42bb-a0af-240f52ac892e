import React from 'react';
import { FormControl, TextField, Grid, FormHelperText } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import * as QuestionUtility from '../../utility/questionUtility';

function IntegerQuestion(props) {
  const { question, handleQuestionResponse } = props;

  const [isReadOnly, setIsReadOnly] = React.useState(() => handleQuestionResponse === undefined);
  const [isValid, setIsValid] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState('');

  const extractExistingAnswer = () => {
    let answer = '';
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      // explicit nullcheck or else it will dismiss the value 0
      answer = question.answer.valueInteger !== null ? question.answer.valueInteger : '';
    }
    return answer;
  };

  const [value, setValue] = React.useState(() => extractExistingAnswer());

  let integerNumberRegex = '^[-+]?[0-9]*$';
  let regex = new RegExp(integerNumberRegex);

  const handleChange = (event) => {
    let isValidInteger = regex.test(event.target.value);
    let characterLimit = 255;
    if (!isReadOnly && isValidInteger) {
      if (event.target.value !== '') {
        let [isValidContent, message] = QuestionUtility.isDecimalContentValid(event.target.value, question);
        if (isValidContent != null) {
          setIsValid(isValidContent);
          setErrorMessage(message);
        }
      } else {
        setIsValid(true);
        setErrorMessage('');
      }
      if (event.target.value.length <= characterLimit) {
        setValue(event.target.value);
        question.answer = {
          valueInteger: event.target.value,
        };
        handleQuestionResponse(question);
      }
    }
  };

  const renderHelperText = (text) => {
    if (isValid) {
      return <></>;
    }
    return (
      <FormHelperText error id="component-error-text">
        {text}
      </FormHelperText>
    );
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <Grid item xs={12} sm={7}>
        <FormControl variant="outlined" fullWidth sx={{ m: 1, ml: 0, mb: 0 }}>
          <TextField
            id="outlined-adornment-integer"
            multiline={isReadOnly}
            value={value}
            autoComplete="off"
            onChange={(event) => handleChange(event)}
            placeholder={!isReadOnly ? 'Please enter an integer' : ''}
            size="small"
          />
          {!isValid ? renderHelperText(errorMessage) : <></>}
        </FormControl>
      </Grid>
      <Explanation question={question} />
    </>
  );
}

export default IntegerQuestion;
