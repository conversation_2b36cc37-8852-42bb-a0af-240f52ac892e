import React from 'react';
import { Box, Button, Grid, Stack } from '@mui/material';
import {
  userDetails,
  updateUserDetails,
  deleteAddress,
  deleteHealthCareId,
  updateIcon,
  icon,
  sendOtp,
  verifyOtp,
  countriesAndProvinces,
  idTypesAndIssuers,
} from '../data/USER_DETAILS';
import { EditUserProfile } from '../lib/EditUserProfile';

function Empty(props) {
  return <div>This component is empty</div>;
}

function GeneralTestPage(props) {
  const [component, setComponent] = React.useState(<Empty />);

  const updateComponent = (component) => {
    setComponent(component);
  };

  return (
    <>
      <Button
        variant="contained"
        onClick={() => {
          updateComponent(
            <EditUserProfile
              userDetails={userDetails}
              updateUserDetails={updateUserDetails}
              deleteAddress={deleteAddress}
              deleteHealthCareId={deleteHealthCareId}
              updateIcon={updateIcon}
              icon={icon}
              sendOtp={sendOtp}
              verifyOtp={(contactMechanism, onSuccess) => verifyOtp(contactMechanism, onSuccess)}
              countriesAndProvinces={countriesAndProvinces}
              idTypesAndIssuers={idTypesAndIssuers}
              //               visibleTabs={['name', 'contact']}
              //               nameTabConfig={['firstName', 'lastName']}
              //               singleContactInfo={true}
            />,
          );
        }}
      >
        UserProfile
      </Button>

      <Box component={Grid} item xs={9} display={{ xs: 'block' }}>
        <div
          style={{
            margin: '25px',
            outline: 'dashed 1px black',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          {component}
        </div>
      </Box>
    </>
  );
}

export default GeneralTestPage;
