import React from 'react';
import { <PERSON>, Button } from '@mui/material';
import { extractExtension } from '../../utility/questionnaireUtility';
import {
  DataGrid,
  GridActionsCellItem,
  useGridApiContext,
  GRID_DATETIME_COL_DEF,
  GRID_NUMERIC_COL_DEF,
  GRID_STRING_COL_DEF,
} from '@mui/x-data-grid';
import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import InputBase from '@mui/material/InputBase';
import { styled } from '@mui/material/styles';
import { QuestionText } from '../QuestionText';
import { format } from 'date-fns';
import { Clear } from '@mui/icons-material';
import { Explanation } from '../Explanation';

function useWindowSize(gridRef) {
  const [size, setSize] = React.useState();
  React.useLayoutEffect(() => {
    function updateSize() {
      setSize(gridRef.current.offsetWidth);
    }
    window.addEventListener('resize', updateSize);
    updateSize();
    return () => window.removeEventListener('resize', updateSize);
  }, []);
  return size;
}

function GridQuestion(props) {
  const { question, handleQuestionResponse } = props;
  const [isReadOnly, setIsReadOnly] = React.useState(() => handleQuestionResponse === undefined);
  const [dateInvalid, setDateInvalid] = React.useState({ error: false });

  const gridRef = React.useRef(null);
  const subQuestions = question && question.item;
  const defaultRowExtension = extractExtension(question.extension, 'Item/default-number-rows');
  const allowedNumberOfRow = defaultRowExtension ? defaultRowExtension.valueInteger : 5;

  const [rows, setRows] = React.useState([]);
  const [columns, setColumns] = React.useState([]);
  const gridWidth = useWindowSize(gridRef);

  const getAnsweredNumberOfRows = () => {
    let noOfRows = 1;
    for (let question of subQuestions) {
      let rowsAnswered = question && question.answer ? question.answer.length : 0;
      if (noOfRows < rowsAnswered) {
        noOfRows = rowsAnswered;
      }
    }
    return noOfRows;
  };

  const handleRowRemove = (rowIndex) => {
    setRows((oldRows) => {
      const newRows = oldRows.filter((item) => item.id !== rowIndex);
      return newRows;
    });
    question.item.forEach((subQuestion, index) => {
      const newAnswer = subQuestion.answer.filter((item, index) => index != rowIndex);
      question.item[index].answer = newAnswer;
    });
  };

  const createAnswerObject = (questionType, answer) => {
    let answerObject = {};
    if (questionType === 'text') {
      answerObject.valueString = answer || '';
    } else if (questionType === 'decimal') {
      answerObject.valueInteger = answer || '';
    } else if (questionType === 'dateTime') {
      if (answer && answer instanceof Date) {
        answerObject.valueDate = format(answer, 'yyyy-MM-dd') || '';
      } else if (typeof answer === 'string') {
        answerObject.valueDate = answer;
      }
    }

    return answerObject;
  };

  const getAnswerValue = (questionType, answer) => {
    if (answer) {
      if (questionType === 'text') {
        return answer && answer.valueString;
      } else if (questionType === 'decimal') {
        return answer && answer.valueInteger;
      } else if (questionType === 'dateTime') {
        if (!answer?.valueDate) return;

        const [year, month, day] = answer?.valueDate?.split('-').map(Number) || ['', '', ''];
        if (!year || !month || !day) return;

        const transformedDate = new Date(year, month - 1, day);
        return transformedDate;
      }
    }
    return '';
  };

  const createRows = (totalNumberOfRows) => {
    let rows = [];
    if (subQuestions) {
      for (let rowIndex = 0; rowIndex < totalNumberOfRows; rowIndex++) {
        let rowObj = {
          id: rowIndex,
        };
        for (let index = 0; index < subQuestions.length; index++) {
          if (subQuestions[index] && subQuestions[index].answer && subQuestions[index].answer[rowIndex]) {
            if (subQuestions[index].type === 'dateTime') {
              let answerValue = getAnswerValue(subQuestions[index].type, subQuestions[index].answer[rowIndex]);
              if (answerValue) {
                answerValue = format(new Date(answerValue), 'yyyy-MM-dd');
              }
              rowObj[subQuestions[index].text] = answerValue;
            } else {
              rowObj[subQuestions[index].text] = getAnswerValue(
                subQuestions[index].type,
                subQuestions[index].answer[rowIndex],
              );
            }
          }
        }
        rows.push(rowObj);
      }
    }

    return rows;
  };

  const getCellWidth = () => {
    let cellWidth = 0;
    let containerWidth = gridWidth - 60;
    if (gridWidth > 500) {
      cellWidth = subQuestions.length > 6 ? containerWidth / 6 : containerWidth / subQuestions.length;
    } else {
      cellWidth = subQuestions.length > 4 ? containerWidth / 4 : containerWidth / subQuestions.length;
    }
    return cellWidth;
  };

  React.useEffect(() => {
    let newColumn = [];

    for (let index = 0; index < subQuestions.length; index++) {
      let columnObject = {
        field: subQuestions[index].text,
        headerName: subQuestions[index].text,
        width: getCellWidth(),
        editable: isReadOnly ? false : true,
        headerAlign: 'left',
        align: 'left',
        sortable: false,
        disableColumnMenu: true,
        disableColumnSelector: true,
      };

      if (subQuestions[index].type === 'dateTime') {
        let dateTimeColumn = {
          ...dateTimeColumnType,
          ...columnObject,
          cellClassName: (params) => `Custom-date-cell__${params.id}`,
        };

        newColumn.push(dateTimeColumn);
      } else if (subQuestions[index].type === 'decimal') {
        let numericColumn = {
          ...numberColumnType,
          ...columnObject,
        };

        newColumn.push(numericColumn);
      } else {
        let textColumn = {
          ...textColumnType,
          ...columnObject,
        };

        newColumn.push(textColumn);
      }
    }

    const actionColumn = {
      field: 'action',
      headerName: '',
      type: 'actions',
      cellClassName: 'actions',
      width: 40,
      sortable: false,
      getActions: (params) => {
        let component = (
          <GridActionsCellItem
            icon={<Clear sx={{ display: params.id == 0 ? 'none' : 'auto' }} />}
            label="Remove"
            className="textPrimary"
            onClick={() => !params.id == 0 && handleRowRemove(params.id)}
            color="inherit"
          />
        );

        return [component];
      },
    };

    if (!isReadOnly) {
      setColumns([...newColumn, actionColumn]);
    } else {
      setColumns([...newColumn]);
    }
    setRows(createRows(getAnsweredNumberOfRows()));
  }, [gridWidth]);

  const addRows = (rowIndex) => {
    let rowObj = {};
    let subQuestions = question.item;
    rowObj['id'] = `${rowIndex}`;
    for (let index = 0; index < question.item.length; index++) {
      rowObj[subQuestions[index].text] = getAnswerValue(subQuestions[index].type, subQuestions[index].answer[rowIndex]);
    }
    setRows((prevRow) => [...prevRow, rowObj]);
  };

  const addLine = (rowIndex) => {
    if (rows.length < allowedNumberOfRow) {
      addRows(rowIndex);
    }
  };

  const handleCellEdit = (cellValue) => {
    if (!isReadOnly) {
      setRows((oldRows) => {
        const newRow = [...oldRows];
        if (newRow && newRow[cellValue.id]) {
          newRow[cellValue.id][cellValue.field] = cellValue.value;
        }
        return newRow;
      });
      question.item.forEach((question) => {
        if (question.text === cellValue.field) {
          question.answer[cellValue.id] = createAnswerObject(question.type, cellValue.value);
        }
      });
      handleQuestionResponse(question);
    }
  };

  const GridEditDateInput = styled(InputBase)({
    fontSize: 'inherit',
    padding: '0 9px',
  });

  const CustomDateInput = ({ inputRef, inputProps, InputProps, disabled, error, onChange, ...rest }) => (
    <GridEditDateInput
      {...rest}
      fullWidth
      autoFocus
      ref={inputRef}
      {...InputProps}
      disabled={disabled}
      error={error}
      inputProps={inputProps}
      onChange={(...args) => {
        rest?.handleDateValidation(error);
        onChange(...args);
      }}
    />
  );

  function GridEditDateCell({ id, field, value, colDef }) {
    const apiRef = useGridApiContext();

    const [date, setDate] = React.useState(value);

    const handleChange = (newValue) => {
      setDate(newValue);
      if (newValue instanceof Date && !isNaN(newValue)) {
        apiRef.current.setEditCellValue({ id, field, value: newValue });
        handleCellEdit({ id, field, value: format(newValue, 'yyyy-MM-dd'), colDef });
      } else {
        apiRef.current.setEditCellValue({ id, field, value: null });
        handleCellEdit({ id, field, value: null, colDef });
      }
    };

    const handleDateValidation = (error) => {
      if (error) {
        setDateInvalid({ error: error, rowId: id });
      } else {
        setDateInvalid({ error: error });
      }
    };

    return (
      <DatePicker
        value={date || null}
        format="yyyy-MM-dd"
        slots={{
          textField: CustomDateInput,
        }}
        slotProps={{
          textField: {
            size: 'small',
            fullWidth: true,
            autoFocus: true,
            handleDateValidation: handleDateValidation,
          },
        }}
        onError={(error) => handleDateValidation(error)}
        onChange={handleChange}
      />
    );
  }

  const dateTimeColumnType = {
    ...GRID_DATETIME_COL_DEF,
    resizable: false,
    renderEditCell: (params) => {
      return <GridEditDateCell {...params} />;
    },
    valueFormatter: (params) => {
      if (typeof params === 'string') {
        return params;
      }
      if (params instanceof Date) {
        return format(params, 'yyyy-MM-dd');
      }
      return '';
    },
  };

  const GridEditNumberInput = styled(InputBase)({
    fontSize: 'inherit',
    padding: '0 9px',
  });

  const GridEditNumberCell = ({ id, field, value }) => {
    const numericAPIRef = useGridApiContext();
    let numericValue = '';
    if (value) {
      if (!isNaN(value)) {
        numericValue = value;
      }
    }

    const handleChange = (event) => {
      if (!isNaN(event.target.value)) {
        numericAPIRef.current.setEditCellValue({ id, field, value: event.target.value });
        handleCellEdit({ id, field, value: event.target.value });
      }
    };

    return <GridEditNumberInput size="small" fullWidth autoFocus value={numericValue} onChange={handleChange} />;
  };

  const numberColumnType = {
    ...GRID_NUMERIC_COL_DEF,
    resizable: false,
    renderEditCell: (params) => {
      return <GridEditNumberCell {...params} />;
    },
    valueFormatter: (params) => {
      return params || '';
    },
  };

  const GridEditTextInput = styled(InputBase)({
    fontSize: 'inherit',
    padding: '0 9px',
  });

  const GridEditTextCell = ({ id, field, value }) => {
    const numericAPIRef = useGridApiContext();

    const handleChange = (event) => {
      numericAPIRef.current.setEditCellValue({ id, field, value: event.target.value });
      handleCellEdit({ id, field, value: event.target.value });
    };

    return <GridEditTextInput size="small" fullWidth autoFocus value={value || ''} onChange={handleChange} />;
  };

  const textColumnType = {
    ...GRID_STRING_COL_DEF,
    resizable: false,
    renderEditCell: (params) => {
      return <GridEditTextCell {...params} />;
    },
    valueFormatter: (params) => {
      return params || '';
    },
  };

  const getDateCellErrorStyle = () => {
    if (dateInvalid.error) {
      return {
        [`.Custom-date-cell__${dateInvalid.rowId}`]: {
          border: '1px solid red',
          borderBottom: '1px solid red !important ',
        },
        [`.Custom-date-cell__${dateInvalid.rowId}.MuiDataGrid-cell--editing`]: {
          outline: false ? 'auto' : 'solid red 1px !important',
        },
      };
    } else {
      return {};
    }
  };

  return (
    <Box sx={getDateCellErrorStyle()}>
      <QuestionText isRequired={question.required} question={question.text} extension={question.extension} />

      <div ref={gridRef} style={{ width: '100%', marginTop: 4 }}>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DataGrid
            density="compact"
            autoHeight
            rows={rows}
            columns={columns}
            disableColumnResize
            sx={{
              '& .MuiDataGrid-columnHeader:last-child .MuiDataGrid-columnSeparator': {
                display: 'none',
              },
            }}
            hideFooter={true}
            disableSelectionOnClick
            onCellEditCommit={(props) => {
              handleCellEdit(props);
            }}
          />
        </LocalizationProvider>
      </div>

      {!isReadOnly ? (
        <Button
          disabled={rows.length >= allowedNumberOfRow}
          onClick={() => addLine(rows.length)}
          sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
        >
          Add another line
        </Button>
      ) : (
        <></>
      )}
      <Explanation question={{ question: question }} />
    </Box>
  );
}

export { GridQuestion };
