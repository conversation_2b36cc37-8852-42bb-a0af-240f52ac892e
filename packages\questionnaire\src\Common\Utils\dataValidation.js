const isNull = (item) => {
  return item === undefined || item === null;
};

const isStringEmpty = (str) => {
  return str.constructor === String && str.trim().length === 0;
};

const isDataEmpty = (data) => {
  if (isNull(data)) return true;
  if (isStringEmpty(data)) return true;
  if (
    (data.constructor === Object && Object.keys(data).length === 0) ||
    (data.constructor === Array && data.length === 0)
  ) {
    return true;
  }
  return false;
};

const formValid = (state, arrayOfkey) => {
  let valid = true;
  // validate the form was filled out
  for (let i = 0; i < arrayOfkey.length; i++) {
    if (isDataEmpty(state[arrayOfkey[i]])) {
      return false;
    }
  }
  return valid;
};

const isValidData = (state) => {
  let valid = false;
  Object.keys(state).forEach((key) => {
    if (!isDataEmpty(state[key])) {
      valid = true;
    }
  });
  return valid;
};

export const isDataValid = (data) => {
  let isValid = false;
  if (data !== null && data !== undefined) isValid = true;
  return isValid;
};

export function isObject(data) {
  let isValid = false;
  if (isDataValid(data)) {
    if (typeof data === 'object') {
      isValid = true;
    }
  }
  return isValid;
}

export default {
  isNull,
  isStringEmpty,
  isDataEmpty,
  formValid,
  isValidData,
};
