import React, { useState, useMemo } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  TextField,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { format } from 'date-fns';
import { EnhancedTableHead } from './TableHead';
import { dateFormats, statuses, publishedStatuses } from '../../CommonConstants';
import { CustomModal } from '../../../components/CustomModal';
import LaunchIcon from '@mui/icons-material/Launch';
import FileCopyIcon from '@mui/icons-material/FileCopy';
import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';
import { Visibility, PublishedWithChanges, Close } from '@mui/icons-material';
import { MenuList, Loader } from '@/components';
import { strings } from '../../../utility/strings';

const createData = ({ title, updateDate, publishStatus, Action }) => {
  return {
    title,
    updateDate,
    publishStatus: publishStatus.toUpperCase(),
    action: Action,
  };
};
const descendingComparator = (a, b, orderBy) => {
  const varA = a[orderBy]?.toUpperCase();
  const varB = b[orderBy]?.toUpperCase();
  if (varB < varA) {
    return -1;
  }
  if (varB > varA) {
    return 1;
  }
  return 0;
};

const getComparator = (order, orderBy) => {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
};

const stableSort = (array, comparator) => {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) {
      return order;
    }
    return a[1] - b[1];
  });
  return stabilizedThis.map((el) => el[0]);
};

const headCells = [
  {
    id: 'title',
    disablePadding: true,
    label: 'Name',
    minWidth: 300,
  },
  {
    id: 'updateDate',
    disablePadding: false,
    label: 'Modified',
    minWidth: 150,
  },
  {
    id: 'publishStatus',
    disablePadding: false,
    label: 'Published',
    minWidth: 100,
  },
  {
    id: 'action',
    disablePadding: false,
    label: '',
    minWidth: 30,
  },
];

export const ArticleTable = (props) => {
  const {
    articleList = [],
    handleEditArticle,
    handleDuplicateArticle,
    handleDeleteArticle,
    handleExportArticle,
    handlePublishArticle,
  } = props;

  const [order, setOrder] = useState(getInitialOrder());
  const [orderBy, setOrderBy] = useState(getInitialOrderBy());
  const [rows, setRows] = useState([]);
  const [anchorEl, setAnchorEl] = useState(null);
  const [searchValue, setSearchValue] = useState('');
  const [openPublishModal, setOpenPublishModal] = useState(false);
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [publishStatus, setPublishStatus] = useState({ isPrivate: false, isPublic: false });
  const [selectedArticleId, setSelectedArticleId] = useState('');
  const [publishedStatus, setPublishedStatus] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  function getInitialOrder() {
    return typeof window !== 'undefined' ? localStorage?.getItem('articleOrder') || 'asc' : 'asc';
  }

  function getInitialOrderBy() {
    return typeof window !== 'undefined' ? localStorage?.getItem('articleOrderBy') || '' : '';
  }

  function updateOrder(newOrder) {
    localStorage?.setItem('articleOrder', newOrder);
    setOrder(newOrder);
  }

  function updateOrderBy(newOrderBy) {
    localStorage?.setItem('articleOrderBy', newOrderBy);
    setOrderBy(newOrderBy);
  }
  const handleSearch = (event) => {
    const value = event?.target?.value?.toLowerCase() || '';
    setSearchValue(event?.target?.value || '');

    const filteredList = articleList.filter(
      (article) => article.title.toLowerCase().includes(value) || article.name.toLowerCase().includes(value),
    );

    createTableRows(filteredList);
  };

  const handleClearSearch = () => {
    setSearchValue('');
    createTableRows(articleList);
  };

  const createTableRows = (articles) => {
    const rows = articles.map((article) => {
      const { title, artifactId, modifiedDate, publishStatus } = article;
      const Action = (
        <MoreVertIcon
          onClick={(e) => handleClick(e, artifactId, publishStatus)}
          sx={{ cursor: 'pointer', display: 'block' }}
        />
      );

      return createData({
        title,
        updateDate: modifiedDate,
        publishStatus,
        artifactId,
        Action,
      });
    });
    setRows(rows);
  };

  useMemo(() => {
    createTableRows(articleList);
  }, [articleList]);

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === 'asc';
    const newOrder = isAsc ? 'desc' : 'asc';
    updateOrder(newOrder);
    updateOrderBy(property);
  };

  const handlePublishModal = (articleId, publishRepository) => {
    setOpenPublishModal(true);
    setSelectedArticleId(articleId);
    if (publishRepository === 'private') {
      setPublishStatus({ isPrivate: true, isPublic: false });
    } else if (publishRepository === 'public') {
      setPublishStatus({ isPrivate: false, isPublic: true });
    } else if (publishRepository === 'both') {
      setPublishStatus({ isPrivate: true, isPublic: true });
    } else {
      setPublishStatus({ isPrivate: false, isPublic: false });
    }
  };

  const handleDeleteModal = (articleId, publishStatus) => {
    setOpenDeleteConfirmation(true);
    setSelectedArticleId(articleId);
    setPublishedStatus(publishStatus);
  };

  const handlePublishConfirm = async () => {
    setIsLoading(true);
    try {
      let status = publishedStatuses.no;
      if (publishStatus.isPrivate && !publishStatus.isPublic) {
        status = publishedStatuses.private;
      } else if (!publishStatus.isPrivate && publishStatus.isPublic) {
        status = publishedStatuses.public;
      } else if (publishStatus.isPrivate && publishStatus.isPublic) {
        status = publishedStatuses.both;
      } else if (!publishStatus.isPrivate && !publishStatus.isPublic) {
        status = publishedStatuses.no;
      }
      await handlePublishArticle(selectedArticleId, status);
      setOpenPublishModal(false);
    } catch (error) {
      console.error('Error publishing article:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    setIsLoading(true);
    try {
      await handleDeleteArticle(selectedArticleId, publishedStatus);
      setOpenDeleteConfirmation(false);
    } catch (error) {
      console.error('Error deleting article:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getMenuItems = (articleId, publishStatus) => [
    {
      id: 'edit',
      label: strings.edit,
      icon: <LaunchIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: async () => {
        setIsLoading(true);
        try {
          await handleEditArticle(articleId, publishStatus);
        } catch (error) {
          console.error('Error editing article:', error);
        } finally {
          setIsLoading(false);
        }
      },
      show: true,
    },
    {
      id: 'duplicate',
      label: strings.duplicate,
      icon: <FileCopyIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: async () => {
        setIsLoading(true);
        try {
          await handleDuplicateArticle(articleId, publishStatus);
        } catch (error) {
          console.error('Error duplicating article:', error);
        } finally {
          setIsLoading(false);
        }
      },
      show: true,
    },
    {
      id: 'export',
      label: strings.export,
      icon: <SaveIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: async () => {
        setIsLoading(true);
        try {
          await handleExportArticle(articleId, publishStatus);
        } catch (error) {
          console.error('Error exporting article:', error);
        } finally {
          setIsLoading(false);
        }
      },
      show: true,
    },
    {
      id: 'publish',
      label: strings.publish,
      icon: <PublishedWithChanges sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () => handlePublishModal(articleId, publishedStatus),
      show: true,
    },
    {
      id: 'delete',
      label: strings.delete,
      icon: <DeleteIcon sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () => handleDeleteModal(articleId, publishStatus),
      show: true,
    },
  ];

  const handleClick = async (event, articleId, publishStatus) => {
    setAnchorEl(event.currentTarget);
    setSelectedArticleId(articleId);
    setPublishedStatus(publishStatus);
  };
  const publishModalContent = (
    <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
      <FormControlLabel
        control={
          <Checkbox
            checked={publishStatus.isPrivate}
            onChange={(e) => setPublishStatus({ ...publishStatus, isPrivate: e.target.checked })}
          />
        }
        label={strings.private}
        sx={{ mr: 4 }}
      />
      <FormControlLabel
        control={
          <Checkbox
            checked={publishStatus.isPublic}
            onChange={(e) => setPublishStatus({ ...publishStatus, isPublic: e.target.checked })}
          />
        }
        label={strings.public}
      />
    </Box>
  );

  return (
    <>
      <Loader active={isLoading} />
      <Box sx={{ mb: -2 }}>
        <Box sx={{ mb: 2 }}>
          <TextField
            size="small"
            placeholder={strings.search}
            sx={{ width: { md: 250 } }}
            value={searchValue}
            onChange={handleSearch}
            InputProps={{
              endAdornment: searchValue && (
                <Close onClick={handleClearSearch} sx={{ color: 'grey', cursor: 'pointer' }} />
              ),
            }}
          />
        </Box>
        {rows?.length ? (
          <Box sx={{ width: '100%' }}>
            <Paper sx={{ width: '100%' }}>
              <TableContainer>
                <Table aria-labelledby="article List">
                  <EnhancedTableHead
                    order={order}
                    orderBy={orderBy}
                    onRequestSort={handleRequestSort}
                    headCells={headCells}
                  />
                  <TableBody>
                    {stableSort(rows, getComparator(order, orderBy)).map((row, index) => (
                      <TableRow key={index}>
                        {headCells.map(
                          (headCell) =>
                            headCell.id !== 'action' && (
                              <TableCell key={headCell.id} align="left" sx={{ py: '0.8rem', px: 2 }}>
                                {['updateDate'].includes(headCell.id)
                                  ? format(new Date(row[headCell.id]), dateFormats.year_month_day)
                                  : row[headCell.id]}
                              </TableCell>
                            ),
                        )}
                        <TableCell align="left" sx={{ py: '0.8rem', px: 2 }}>
                          {row.action}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Paper>
            <MenuList
              menuItems={getMenuItems(selectedArticleId, publishedStatus)}
              anchorEl={anchorEl}
              setAnchorEl={setAnchorEl}
            />
            <CustomModal
              open={openPublishModal}
              onClose={() => setOpenPublishModal(false)}
              onConfirm={handlePublishConfirm}
              title={strings.publishConfirmation}
              subTitle={strings.selectRepositoryYouWantToPublishIn}
              content={publishModalContent}
              saveButtonText={strings.publish}
              closeButtonText={strings.cancel}
            />
            <CustomModal
              open={openDeleteConfirmation}
              onClose={() => setOpenDeleteConfirmation(false)}
              onConfirm={handleDeleteConfirm}
              title={strings.deleteArticle}
              subTitle={strings.thisActionCanNotBeUndone}
              saveButtonText={strings.delete}
              closeButtonText={strings.cancel}
            />
          </Box>
        ) : (
          <>No articles found</>
        )}
      </Box>
    </>
  );
};
