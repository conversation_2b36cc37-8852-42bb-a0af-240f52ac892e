/**
 * Utility functions to transform message data between different formats
 */

/**
 * Transforms API response messages to the format expected by the UI components
 * @param {Array} messages - Messages from the API
 * @param {string} type - Message type ('inbox' or 'sent')
 * @returns {Array} Transformed messages ready for UI components
 */
export const transformApiMessagesToUI = (messages = [], type = 'inbox') => {
  if (!Array.isArray(messages)) return [];

  return messages.map((message) => ({
    id: message.MessageId,
    subject: message.Subject || '',
    content: message.Content?.messageBody || message.Content?.narrative || '',
    sender: message.SenderName || '',
    recipientId: message.RecipientId || '',
    senderId: message.SenderId || '',
    recipient: message.RecipientName || '',
    timestamp: message.MessageTimestamp,
    status: message.IsRead ? 'read' : 'unread',
    type: type,
    deliveryMechanism: message.DeliveryMechanism,
    // Store original message for reference if needed
    originalMessage: message,
  }));
};

/**
 * Transforms sort model from UI to API format
 * @param {Array} sortModel - Sort model from DataGrid
 * @param {string} type - Message type ('inbox' or 'sent')
 * @returns {Object} API sort parameters
 */
export const transformSortModelToApi = (sortModel = [], type = 'inbox') => {
  // Map the DataGrid field names to the API's sortBy values
  const fieldToSortByMap = {
    received: 'date',
    from: 'name',
    sent: 'date',
    to: 'name',
  };

  // Get the current sort field and direction
  const currentSortField = sortModel[0]?.field || (type === 'sent' ? 'sent' : 'received');
  const currentSortDirection = sortModel[0]?.sort?.toUpperCase() || 'DESC';

  // Map the DataGrid field to the API's sortBy parameter
  const sortBy = fieldToSortByMap[currentSortField] || 'date';

  return {
    sortBy,
    sortOrder: currentSortDirection,
  };
};

/**
 * Builds API query parameters from filter options
 * @param {Object} options - Filter options
 * @param {string} options.type - Message type ('inbox' or 'sent')
 * @param {Object} options.startDate - Start date (dayjs object)
 * @param {Object} options.endDate - End date (dayjs object)
 * @param {Array} options.sortModel - Sort model from DataGrid
 * @param {string} options.searchText - Search text
 * @returns {Object} API query parameters
 */
export const buildApiQueryParams = (options = {}) => {
  const { type = 'inbox', startDate, endDate, sortModel = [], searchText = '' } = options;

  const { sortBy, sortOrder } = transformSortModelToApi(sortModel, type);

  // Build params object
  const params = {
    type, // 'inbox' or 'sent'
    startDate: startDate?.format?.('YYYY-MM-DD'),
    endDate: endDate?.format?.('YYYY-MM-DD'),
    sortOrder,
    sortBy,
  };

  // Add search text if provided
  if (searchText) {
    params.searchText = searchText;
  }

  return params;
};
