import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Link, CircularProgress, useMediaQuery, Box } from '@mui/material';
import cambianLogo from './cambian-logo.png';
import { validateUsername, validatePassword } from './Regex';
import { UncontrolledPassword } from './UncontrolledPassword';
import { UncontrolledUsernameInput } from './UncontrolledUsernameInput';

const DEFAULT_ERROR_MSG = 'The username or password did not match. Please try again.';

function SignIn(props) {
  const {
    signInCallback,
    navigateToVerifyRegistrationCallback,
    navigateToForgotPasswordCallback,
    navigateToSignUpCallback,
    logoUrl = cambianLogo,
    title = 'Sign in',
    byline = 'Use your Cambian account',
    buttonText = 'Sign in',
    textBelowButton = "Don't have a Cambian account?",
    linkBelowButtonText = 'Create a free account',
    usernameType = 'email',
    orgName = 'Cambian',
    hideSignUpSection = false,
  } = props;

  const [usernameValidSchema, setUsernameValidSchema] = React.useState({});
  const [passwordValidSchema, setPasswordValidSchema] = React.useState({});

  const [serverResponseErrorMsg, setServerResponseErrorMsg] = React.useState(null);
  const [showLinkToVerifyRegistration, setShowLinkToVerifyRegistration] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);

  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const usernameInputRef = React.useRef();
  const passwordInputRef = React.useRef();
  const stackRef = React.useRef();

  const validateForm = () => {
    const usernameValidationResult = validateUsername({
      min: 1,
      requireEmailFormat: usernameType === 'email',
      value: usernameInputRef.current.value,
    });
    const passwordValidationResult = validatePassword({ min: 1, max: null, value: passwordInputRef.current.value });
    setUsernameValidSchema(usernameValidationResult);
    setPasswordValidSchema(passwordValidationResult);
    return { usernameValidationResult, passwordValidationResult };
  };

  const handleSubmit = async () => {
    const { usernameValidationResult, passwordValidationResult } = validateForm();

    const isUsernameValid = !Object.values(usernameValidationResult).some((v) => v === false);
    const isPasswordValid = !Object.values(passwordValidationResult).some((v) => v === false);

    if (!isUsernameValid) {
      usernameInputRef.current.focus();
    } else if (!isPasswordValid) {
      passwordInputRef.current.focus();
    }

    if (isUsernameValid && isPasswordValid) {
      setIsLoading(true);
      const { success, errorMsg, errorName } = await signInCallback({
        username: usernameInputRef.current.value,
        password: passwordInputRef.current.value,
      });
      if (!success) {
        console.error(errorMsg);
        if (errorName === 'UserNotConfirmedException') {
          setShowLinkToVerifyRegistration(true);
        }
        if (errorMsg) {
          setServerResponseErrorMsg(errorMsg);
        } else {
          setServerResponseErrorMsg(DEFAULT_ERROR_MSG);
        }
        setIsLoading(false);
      }
    } else {
      setServerResponseErrorMsg(null);
    }
  };

  const handleLinkButtonClicked = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    navigateToSignUpCallback();
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  return (
    <Stack direction="column" spacing={isXs ? 1 : 1.5} sx={{ width: { xs: '100%', sm: '40ch' } }}>
      <Box
        component="img"
        sx={{
          marginLeft: '-1%',
          width: { xs: '50%', sm: '55%' },
          minWidth: '100px',
          minHeight: '30px',
          height: { xs: '40%', sm: '45%' },
          marginBottom: -0.5,
        }}
        src={logoUrl}
        alt={`${orgName} full logo`}
      />
      <Typography sx={{ fontSize: { xs: 24, sm: 30 } }} fontWeight={500}>
        {title}
      </Typography>
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{byline}</Typography>
      <UncontrolledUsernameInput
        ref={usernameInputRef}
        orgName={orgName}
        usernameType={usernameType}
        validSchema={usernameValidSchema}
        onKeyDown={handleKeyDown}
      />
      <UncontrolledPassword
        id="password"
        text="Password"
        type="text"
        labelFontSize={{ xs: 14, sm: 16 }}
        inputFontSize={{ xs: 15, sm: 17 }}
        inputBoxSize="small"
        ref={passwordInputRef}
        min={1}
        validSchema={passwordValidSchema}
        onKeyDown={handleKeyDown}
      />
      {navigateToForgotPasswordCallback && (
        <Link
          component="button"
          onClick={navigateToForgotPasswordCallback}
          sx={{
            textAlign: 'left',
            width: 'fit-content',
            fontSize: { xs: 11, sm: 12 },
          }}
        >
          Forgot password?
        </Link>
      )}
      <Button
        variant="contained"
        disabled={isLoading}
        onClick={handleSubmit}
        sx={{
          fontSize: { xs: 17, sm: 19 },
          fontWeight: 600,
          padding: '11.5px 12px',
          lineHeight: 1,
        }}
      >
        {isLoading ? <CircularProgress size={isXs ? 15 : 19} /> : buttonText}
      </Button>
      {serverResponseErrorMsg && (
        <>
          <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
            {serverResponseErrorMsg}
            {showLinkToVerifyRegistration && (
              <Link
                component="button"
                onClick={() => {
                  setShowLinkToVerifyRegistration(false);
                  navigateToVerifyRegistrationCallback();
                }}
                sx={{
                  textAlign: 'left',
                  width: 'fit-content',
                  fontSize: { xs: 11, sm: 12 },
                }}
              >
                Verify account
              </Link>
            )}
          </Typography>
        </>
      )}
      {!hideSignUpSection && (
        <>
          <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{textBelowButton}</Typography>
          <Link
            component="button"
            onClick={handleLinkButtonClicked}
            sx={{
              textAlign: 'left',
              width: 'fit-content',
              fontSize: { xs: 11, sm: 12 },
            }}
          >
            {linkBelowButtonText}
          </Link>
        </>
      )}
    </Stack>
  );
}

export { SignIn };
