import * as QuestionnaireUtility from './questionnaireUtility';
import { CANNOT_LOCATE_STRING } from '../../../Common/constants';

export function isRadioButtonQuestion(question) {
  let isRadioQuestion = false;
  if (question.type === 'choice') {
    let multipleChoiceExtension = QuestionnaireUtility.extractExtension(
      question.question.extension,
      'Item/multiple-answer-choice',
    );
    if (multipleChoiceExtension !== null) {
      isRadioQuestion = !multipleChoiceExtension.valueBoolean;
    }
  }
  return isRadioQuestion;
}

export function isCheckboxQuestion(question) {
  let isCheckboxQuestion = false;
  if (question.type === 'choice') {
    let multipleChoiceExtension = QuestionnaireUtility.extractExtension(
      question.question.extension,
      'Item/multiple-answer-choice',
    );
    if (multipleChoiceExtension !== null) {
      isCheckboxQuestion = multipleChoiceExtension.valueBoolean;
    }
  }
  return isCheckboxQuestion;
}

export function isLargeButtonQuestion(question) {
  let isLargeButtonsQuestion = false;
  if (question.type === 'choice') {
    let multipleChoiceExtension = QuestionnaireUtility.extractExtension(
      question.question.extension,
      'display-large-buttons',
    );
    if (multipleChoiceExtension !== null) {
      isLargeButtonsQuestion = multipleChoiceExtension.valueBoolean;
    }
  }
  return isLargeButtonsQuestion;
}

export function isChoiceBarQuestion(question) {
  let isChoiceBarQuestion = false;
  if (question.type === 'choice') {
    let multipleChoiceExtension = QuestionnaireUtility.extractExtension(
      question.question.extension,
      'Item/display-type',
    );
    if (multipleChoiceExtension !== null) {
      isChoiceBarQuestion = 'choice-bar' === multipleChoiceExtension.valueString;
    }
  }
  return isChoiceBarQuestion;
}

export function isBodyDiagramQuestion(question) {
  let isBodyDiagramQuestion = false;
  if (question.type === 'choice') {
    let multipleChoiceExtension = QuestionnaireUtility.extractExtension(
      question.question.extension,
      'Item/display-type',
    );
    if (multipleChoiceExtension !== null) {
      isBodyDiagramQuestion = 'choice-image' === multipleChoiceExtension.valueString;
    }
  }
  return isBodyDiagramQuestion;
}

export function isNumericSliderQuestion(question) {
  let isNumericSliderQuestion = false;
  if (question.type === 'integer') {
    let multipleChoiceExtension = QuestionnaireUtility.extractExtension(
      question.question.extension,
      'Item/display-type',
    );
    if (multipleChoiceExtension !== null) {
      isNumericSliderQuestion = 'numeric-slider' === multipleChoiceExtension.valueString;
    }
  }
  return isNumericSliderQuestion;
}

export const isIntegerOnlyQuestion = (question) => {
  let isIntegerOnlyQuestion = false;
  if (question.type === 'integer') {
    let integerOnlyExtension = QuestionnaireUtility.extractExtension(question.question.extension, 'Item/integer-only');

    if (integerOnlyExtension !== null) {
      isIntegerOnlyQuestion = integerOnlyExtension.valueBoolean;
    }
  }
  return isIntegerOnlyQuestion;
};

export function isDropdownQuestion(question) {
  let isDropdownQuestion = false;
  if (question.type === 'choice') {
    let dropdownExtension = QuestionnaireUtility.extractExtension(question.question.extension, 'Item/question-type-id');

    if (dropdownExtension && dropdownExtension.valueInteger === 5543) {
      isDropdownQuestion = true;
    }
  }

  return isDropdownQuestion;
}

export function extractDescription(question) {
  let description = null;
  if (question.question.extension !== undefined) {
    let descExt = QuestionnaireUtility.extractExtension(question.question.extension, 'Item/description');
    if (descExt !== null) {
      description = CANNOT_LOCATE_STRING === descExt.valueString ? null : descExt.valueString;
    }
  }
  return description;
}

export function extractHorizontalOrientation(question) {
  let horizontalOrientation = null;
  if (question.question.extension !== undefined) {
    let descExt = QuestionnaireUtility.extractExtension(question.question.extension, 'Item/horizontal-orientation');
    if (descExt !== null) {
      horizontalOrientation = CANNOT_LOCATE_STRING === descExt.valueBoolean ? null : descExt.valueBoolean;
    }
  }
  return horizontalOrientation;
}

export function isQuestionValid(question) {
  let isValid = true;
  let questionType = question.question ? question.question.type : question.type;
  if (questionType === 'decimal') {
    let answerValue = null;
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      answerValue = question.answer.valueDecimal;
    }
    if (answerValue !== null && answerValue !== undefined && answerValue !== '') {
      isValid = isDecimalContentValid(answerValue, question)[0];
    }
  }

  if (questionType === 'integer' && isIntegerOnlyQuestion(question)) {
    let answerValue = null;
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      answerValue = question.answer.valueInteger;
    }
    if (answerValue !== null && answerValue !== undefined && answerValue !== '') {
      isValid = isDecimalContentValid(answerValue, question)[0];
    }
  }
  //TODO: add validation logic for other question type
  return isValid;
}

export function isNumberValue(inputValue) {
  let inputArray = inputValue.split('');
  if (['+', '-'].includes(inputArray[0])) {
    // check if +, - is present at first position of input value
    if (inputArray.length === 1) {
      return true;
    } else {
      inputArray.shift(); // remove '+' or '-' from input value i.e from remove element at index 0
      if (!Number.isNaN(Number(inputArray.join('')))) {
        return true;
      } else {
        return false;
      }
    }
  } else if (!Number.isNaN(Number(inputValue))) {
    return true;
  }
  return false;
}

export function isDecimalContentValid(comparingValue, question) {
  let value = comparingValue !== undefined && comparingValue !== null ? Number(comparingValue) : comparingValue;

  let isContentValid = true;
  let errorMessage = '';
  let questionExtension = question.question ? question.question.extension : question.extension;
  let minValueExtension = QuestionnaireUtility.extractExtension(questionExtension, 'Item/min-value');
  let minValue = minValueExtension ? minValueExtension.valueDecimal : null;
  let MaxValueExtension = QuestionnaireUtility.extractExtension(questionExtension, 'Item/max-value');
  let maxValue = MaxValueExtension ? MaxValueExtension.valueDecimal : null;
  let minExclusionExtension = QuestionnaireUtility.extractExtension(questionExtension, 'Item/min-exclusion');
  let minExclusion = minExclusionExtension ? minExclusionExtension.valueBoolean : null;
  let maxExclusionExtension = QuestionnaireUtility.extractExtension(questionExtension, 'Item/max-exclusion');
  let maxExclusion = maxExclusionExtension ? maxExclusionExtension.valueBoolean : null;

  minValue = minValue !== null && minValue !== undefined ? Number(minValue) : minValue;
  maxValue = maxValue !== null && maxValue !== undefined ? Number(maxValue) : maxValue;

  if (minValue != null) {
    if (minExclusion != null && minExclusion) {
      if (value <= minValue) {
        isContentValid = false;
        errorMessage = 'Value must be greater than ' + minValue;
      }
    } else {
      if (value < minValue) {
        isContentValid = false;
        errorMessage = 'Value must be greater than or equal to ' + minValue;
      }
    }
  }
  if (maxValue != null) {
    if (maxExclusion != null && maxExclusion) {
      if (value >= maxValue) {
        isContentValid = false;
        errorMessage = 'Value must be less than ' + maxValue;
      }
    } else {
      if (value > maxValue) {
        isContentValid = false;
        errorMessage = 'Value must be less than or equal to ' + maxValue;
      }
    }
  }
  if (minValue != null && maxValue != null) {
    if (minExclusion != null && minExclusion) {
      if (maxExclusion != null && maxExclusion) {
        if (value <= minValue || value >= maxValue) {
          isContentValid = false;
          errorMessage = 'Value must be greater than ' + minValue + ' and less than ' + maxValue;
        }
      } else {
        if (value <= minValue || value > maxValue) {
          isContentValid = false;
          errorMessage = 'Value must be greater than ' + minValue + ' and less than or equal to ' + maxValue;
        }
      }
    } else {
      if (maxExclusion != null && maxExclusion) {
        if (value < minValue || value >= maxValue) {
          isContentValid = false;
          errorMessage = 'Value must be greater than or equal to ' + minValue + ' and less than ' + maxValue;
        }
      } else {
        if (value < minValue || value > maxValue) {
          isContentValid = false;
          errorMessage = 'Value must be between ' + minValue + ' and ' + maxValue;
        }
      }
    }
  }
  return [isContentValid, errorMessage];
}

export function extractOtherOptionDetails(extension) {
  const otherOptionExtension = QuestionnaireUtility.extractExtension(
    extension,
    'Item/AnswerOption/ValueCoding/other-option',
  );
  const otherOptionAvailable = otherOptionExtension ? otherOptionExtension.valueString : '';
  let otherOptionId;
  let otherOptionValue = '';
  if (otherOptionAvailable) {
    let otherOptionParts = otherOptionAvailable.split(',');
    otherOptionId = otherOptionParts[0].split(':')[1];
    otherOptionValue = otherOptionParts[2] ? otherOptionParts[2].split(':')[1] : '';
  }

  return { otherOptionId, otherOptionValue };
}
