export const isEmpty = (value) => {
  return value === undefined || value === null || value.trim() === '';
};

export const hasMinLength = (value, minLength = 2) => {
  return value && value.trim().length >= minLength;
};

export const validateField = (fieldName, value) => {
  if (isEmpty(value)) {
    return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
  }

  if (!hasMinLength(value)) {
    return `Must be at least 2 characters`;
  }

  return null;
};

export const validateForm = (fields) => {
  const errors = {};

  Object.entries(fields).forEach(([fieldName, value]) => {
    if (typeof value === 'string') {
      const error = validateField(fieldName, value);
      if (error) {
        errors[fieldName] = error;
      }
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
};
