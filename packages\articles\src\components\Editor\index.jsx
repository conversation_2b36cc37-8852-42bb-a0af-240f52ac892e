import React from 'react';
import { Box } from '@mui/material';
import { Properties } from './screens/Properties';
import { Body } from './screens/Body';
import { editorScreens } from '../../containers/CommonConstants';

export const Editor = (props) => {
  const {
    currentScreen,
    articleData,
    onTitleChange,
    onNameChange,
    onDescriptionChange,
    onBodyChange,
    onThumbnailChange,
    existingArticleData,
    formErrors = {},
  } = props;

  const getEditorScreen = () => {
    switch (currentScreen) {
      case editorScreens.PROPERTIES:
        return (
          <Properties
            title={articleData.title}
            name={articleData.name}
            description={articleData.description}
            thumbnail={articleData.thumbnail}
            onTitleChange={onTitleChange}
            onNameChange={onNameChange}
            onDescriptionChange={onDescriptionChange}
            onThumbnailChange={(metadata, fileData) => onThumbnailChange(metadata, fileData)}
            existingArticleData={existingArticleData}
            formErrors={formErrors}
          />
        );
      case editorScreens.BODY:
        return (
          <Body
            body={articleData.body}
            onBodyChange={onBodyChange}
            existingArticleData={existingArticleData}
            formErrors={formErrors}
          />
        );
      default:
        return (
          <Properties
            title={articleData.title}
            name={articleData.name}
            description={articleData.description}
            thumbnail={articleData.thumbnail}
            onTitleChange={onTitleChange}
            onNameChange={onNameChange}
            onDescriptionChange={onDescriptionChange}
            onThumbnailChange={(metadata, fileData) => onThumbnailChange(metadata, fileData)}
            existingArticleData={existingArticleData}
            formErrors={formErrors}
          />
        );
    }
  };

  return <Box sx={{ height: '100%' }}>{getEditorScreen()}</Box>;
};
