import React from 'react';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';

function DisplayOnlyQuestion(props) {
  const { question } = props;

  //TODO: remove these 2 lines and related code once introduction component is get removed from editor
  let instructionButtonString =
    "<button type='button' class='btn btn-primary nextSet' value='' title='Continue to next page'>Continue &ensp;<span class='ss-icon ss-navigateright'></span></button>";
  let text = question.question.text ? question.question.text.replace(instructionButtonString, '') : '';

  return (
    <>
      <QuestionText isRequired={question.question.required} question={text} extension={question.question.extension} />
      <Explanation question={question} />
    </>
  );
}

export default DisplayOnlyQuestion;
