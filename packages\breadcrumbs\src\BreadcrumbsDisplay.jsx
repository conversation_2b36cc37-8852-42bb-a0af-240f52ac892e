import React from 'react';
import { Breadcrumbs, Typography, useTheme } from '@mui/material';
import { useBreadcrumb } from './Breadcrumbs';
import Link from 'next/link';

export const BreadcrumbsDisplay = () => {
  const { breadcrumbs } = useBreadcrumb();
  const theme = useTheme();

  if (breadcrumbs.length <= 1) {
    return null;
  }

  return (
    <Breadcrumbs aria-label="breadcrumb" sx={{ mb: 2 }}>
      {breadcrumbs.map((breadcrumb, index) => {
        const isLast = index === breadcrumbs.length - 1;
        const TypographyComponent = (
          <Typography
            component={isLast ? 'span' : 'a'}
            sx={{
              fontSize: '14px',
              color: isLast ? theme.palette.text.primary : theme.palette.primary.main,
              textDecoration: isLast ? 'none' : 'underline',
            }}
            key={breadcrumb.path}
          >
            {breadcrumb.name}
          </Typography>
        );

        return isLast ? (
          TypographyComponent
        ) : (
          <Link href={breadcrumb.path} passHref>
            {TypographyComponent}
          </Link>
        );
      })}
    </Breadcrumbs>
  );
};
