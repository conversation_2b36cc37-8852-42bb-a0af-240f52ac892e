import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Stack,
  TextField,
  IconButton,
  Autocomplete,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material/styles';
import { v4 as uuidv4 } from 'uuid';
import AddIcon from '@mui/icons-material/Add';
import Close from '@mui/icons-material/Close';
import StarIcon from '@mui/icons-material/Star';
import StarOutlineIcon from '@mui/icons-material/StarOutline';

const useStyles = (theme) => ({
  icons: {
    color: theme.palette.primary.main,
    fontSize: '20px',
  },
  closeIcon: {
    fontSize: '20px',
  },
  starButton: {
    paddingRight: 0,
  },
});

function AddressPanel(props) {
  const { t } = useTranslation();
  const theme = useTheme();
  const styles = useStyles(theme);
  const { countriesAndProvinces } = props;
  const [userAddressList, setUserAddressList] = useState(props.userDetail.addresses || []);

  useEffect(() => {
    if (userAddressList.length === 0) {
      handleAddAddress(); // Adds one address entry on mount
    }
  }, []);

  const handleTextFieldChange = (index, event, key) => {
    const updatedAddressList = [...userAddressList];
    updatedAddressList[index][key] = event.target.value;
    setUserAddressList(updatedAddressList);
    props.updateProfileDataCallback('property', 'update', 'address', updatedAddressList);
  };

  const handleAddAddress = () => {
    const newAddress = {
      id: uuidv4(),
      address1: '',
      address2: '',
      city: '',
      province: '',
      postalCode: '',
      country: '',
      primary: userAddressList.length === 0, // First address is primary by default
    };

    const updatedAddressList = [...userAddressList, newAddress];

    // Ensure only one address is primary
    if (newAddress.primary) {
      updatedAddressList.forEach((address) => {
        if (address.id !== newAddress.id) {
          address.primary = false;
        }
      });
    }

    setUserAddressList(updatedAddressList);
  };

  const handleTogglePrimary = (index) => {
    const updatedAddressList = [...userAddressList];
    updatedAddressList.forEach((address, i) => {
      address.primary = i === index; // Mark only the clicked address as primary
    });

    setUserAddressList(updatedAddressList);
    props.updateProfileDataCallback('property', 'update', 'address', updatedAddressList);
  };

  const handleRemoveAddress = (index) => {
    const updatedAddressList = userAddressList.filter((_, i) => i !== index);
    setUserAddressList(updatedAddressList);
    props.updateProfileDataCallback('property', 'delete', 'address', index);

    // Check if the item being removed is the primary one
    if (userAddressList[index].primary) {
      if (updatedAddressList.length > 0) {
        updatedAddressList[0].primary = true; // Set the first item to primary
      }
    }
    props.updateProfileDataCallback('property', 'update', 'address', updatedAddressList);
  };

  const handleNoneClick = (index, field) => {
    const updatedData = {
      ...userAddressList,
      [index]: {
        ...userAddressList[index],
        [field]: '',
      },
    };
  };

  const getLabels = (country) => {
    switch (country) {
      case 'Canada':
        return { provinceLabel: 'Province', postalCodeLabel: 'Postal Code' };
      case 'USA':
        return { provinceLabel: 'State', postalCodeLabel: 'ZIP Code' };
      default:
        return { provinceLabel: 'Province/State', postalCodeLabel: 'Postal/ZIP Code' };
    }
  };

  return (
    <Grid item xs={12} sm={10} md={6} lg={5}>
      {/* Address Fields */}
      {userAddressList.map((address, index) => {
        const { provinceLabel, postalCodeLabel } = getLabels(address.country);
        const provincesOrStates = countriesAndProvinces[address.country] || [];

        return (
          <Stack spacing={1} key={index}>
            {/* Address Line One with Star and Close Icon */}
            <Box display="flex" alignItems="center" style={{ padding: 0 }}>
              <TextField
                label={`Line 1`}
                value={address.address1}
                onChange={(e) => handleTextFieldChange(index, e, 'address1')}
                fullWidth
                sx={{ mt: index !== 0 ? '32px' : 0 }}
              />
              <Grid sx={{ mt: index !== 0 ? '32px' : 0 }}>
                <IconButton onClick={() => handleTogglePrimary(index)} sx={styles.starButton}>
                  {address.primary ? <StarIcon sx={styles.icons} /> : <StarOutlineIcon sx={styles.icons} />}
                </IconButton>
                <IconButton onClick={() => handleRemoveAddress(index)} sx={styles.starButton}>
                  <Close sx={styles.closeIcon} />
                </IconButton>
              </Grid>
            </Box>

            {/* Address Line Two */}
            <TextField
              label={`Line 2`}
              value={address.address2}
              onChange={(e) => handleTextFieldChange(index, e, 'address2')}
              fullWidth
            />

            {/* City */}
            <TextField
              label={`City`}
              value={address.city}
              onChange={(e) => handleTextFieldChange(index, e, 'city')}
              fullWidth
            />

            {/* Country */}
            <FormControl fullWidth size="small">
              <Autocomplete
                value={address.country || ''}
                onChange={(event, value) => {
                  handleTextFieldChange(index, { target: { value } }, 'country');
                  if (!value) {
                    handleTextFieldChange(index, { target: { value: '' } }, 'province');
                  }
                }}
                onInputChange={(event, newInputValue) => {
                  handleTextFieldChange(index, { target: { value: newInputValue } }, 'country');
                }}
                options={Object.keys(countriesAndProvinces)}
                renderInput={(params) => <TextField {...params} label="Country" fullWidth />}
                freeSolo
                autoSelect={false}
              />
            </FormControl>

            {/* Province */}
            <FormControl fullWidth size="small">
              {provincesOrStates.length > 0 ? (
                <>
                  <InputLabel>{provinceLabel}</InputLabel>
                  <Select
                    value={address.province || ''}
                    onChange={(e) => handleTextFieldChange(index, e, 'province')}
                    label={provinceLabel}
                    fullWidth
                  >
                    {address.province && (
                      <MenuItem value="">
                        <em>None</em>
                      </MenuItem>
                    )}
                    {provincesOrStates.map((province, idx) => (
                      <MenuItem key={idx} value={province}>
                        {province}
                      </MenuItem>
                    ))}
                  </Select>
                </>
              ) : (
                <TextField
                  label={provinceLabel}
                  value={address.province || ''}
                  onChange={(e) => handleTextFieldChange(index, e, 'province')}
                  fullWidth
                />
              )}
            </FormControl>

            {/* Postal Code */}
            <TextField
              label={postalCodeLabel}
              value={address.postalCode}
              onChange={(e) => handleTextFieldChange(index, e, 'postalCode')}
              fullWidth
            />
          </Stack>
        );
      })}

      {/* Button to Add Additional Address */}
      <Button
        size="medium"
        color="primary"
        onClick={handleAddAddress}
        startIcon={<AddIcon />}
        fullWidth
        sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
      >
        Add Address
      </Button>
    </Grid>
  );
}

export default AddressPanel;
