import React from 'react';
import { CANNOT_LOCATE_STRING } from '../../Common/constants';
import dataValidation from '../../Common/Utils/dataValidation';

function QuestionnaireDescription(props) {
  const { description } = props;

  return (
    <React.Fragment>
      {description !== CANNOT_LOCATE_STRING && !dataValidation.isDataEmpty(description) && (
        <div dangerouslySetInnerHTML={{ __html: description }} />
      )}
    </React.Fragment>
  );
}

export { QuestionnaireDescription };
