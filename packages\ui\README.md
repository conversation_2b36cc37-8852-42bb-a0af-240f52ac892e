# Publishing a Component
To Publish the component, do the following steps from the root directory:

1) npm version 0.0.2 --no-git-tag-version
   The version string should be named to a version that is not yet been published

2) npm run rollup
   This command packages up the components into the 'dist' directory, ready to be published

3) npm publish
   Publish the component to the repo.

4) Test the component
   Now that the component has been published to a repo, we recommend that the page(s) used in this project to develop the component is changed
   from using the local import to the import from the component.

NOTE: Should regularly run :
npx browserslist@latest --update-db

# Deletion of the node_modules directory
npm sometimes makes excessively deep "node_modules" directory that cannot be deleted in windows.  

To delete these directories:
npm install -g rimraf
rimraf node_modules

https://stackoverflow.com/questions/18875674/whats-the-difference-between-dependencies-devdependencies-and-peerdependencies


Detail on CAT Questionnaire:
http://build.fhir.org/ig/HL7/sdc/adaptive.html


FHIR details on coding systems
http://build.fhir.org/terminologies-systems.html

To support this coding function, each question that supports a coding system identifier(s) needs the FHIR questionnaire document to have
a coding element in the question.  Note that the current support is the most simplistic cases that covers textual cases.  There are more 
elaborate cases such as question lists that have not been covered with the code base at this time.

{
"linkId" " "1.1",
"code": [
"system": "http://loinc.org/",
"version": "2.72",
"code": "45394-4"
"display": "Patient Last name"
]
...
...
...


# Getting Started

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).  This project has been created to hold
Cambian core components.  The project itself can be used as a playground to develop and test a new component.  All components developed in this
fashion should be in ./src/lib/components/<ComponentName>/<ComponentName>.jsx.  An index file should also be in that directory and the parent index.js
file should export the new component.

The rest of the React app outside of ./src/lib is available to host your new component, both for development and test as well as demonstrating how the
component will look and how to use it.

# Setting up your environment
   
To clone the repository, you may need to include your github user name.  For example:
   git clone https://[YOUR_GITHUB_USERNAME]@github.com/cambianrepo/components.git

You will need to create and set up a personal access token - otherwise you'll not be able to publish or use components in this repo.
You’ll need to get a token.  Go to GitHub https://github.com/settings/tokens
And get write:packages / read:packages.  Click generate token and save the resulting string.

Go to a terminal window:
npm login --registry=https://npm.pkg.github.com

You’ll be prompted for github username, email and password.  Use the token you just generated as your password.  This 
will add the token and auth configuration to your “.npmrc” file in your user directory.


https://levelup.gitconnected.com/publish-react-components-as-an-npm-package-7a671a2fb7f
https://betterprogramming.pub/build-your-very-own-react-component-library-and-publish-it-to-github-package-registry-192a688a51fd
https://dev.to/alexeagleson/how-to-create-and-publish-a-react-component-library-2oe

   
   
   Using newly published component:
   The component library and all components developed here will be available to be used by running the command:

   yarn add @<GIT-USER-ID>/<GIT-PACKAGE>@<VERSION>
    
   ie 
        yarn add @cambianrepo/ui@0.0.2
    
   Once imported, components can be imported into a project by:
    
   import { <COMPONENT> } from "@<GIT-USER-ID>/<GIT-PACKAGE>";
   
   ie 
        import { Branding } from "@cambianrepo/ui";
        
   The import statement on the page used to test the component should now be changed from something like:
   
   FROM:
   import { <COMPONENT> } from "./lib/components";
   
   TO:
   import { <COMPONENT> } from "@<GIT-USER-ID>/<GIT-PACKAGE>";     

   The page used to test the component should still work exactly as it worked while developing the component locally, but by drawing in the 
   component remotely from the repo.  If all is still good that component is now ready to be used as a component import elsewhere.
   
   
   Note: All dependencies needed to use the component should be expressed in either the peerDependencies or devDependencies - but not dependencies.

## Available Scripts

In the project directory, you can run:

### `yarn start`

Runs the app in the development mode.\
Open [http://localhost:3005](http://localhost:3005) to view it in your browser.

The page will reload when you make changes.\
You may also see any lint errors in the console.

### `yarn run rollup`

Prepares the project to be published

### `yarn run build`

Builds the app for production to the `build` folder.\
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.\
Your app is ready to be deployed!

See the section about [deployment](https://facebook.github.io/create-react-app/docs/deployment) for more information.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can't go back!**

If you aren't satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you're on your own.

You don't have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn't feel obligated to use this feature. However we understand that this tool wouldn't be useful if you couldn't customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

### Code Splitting

This section has moved here: [https://facebook.github.io/create-react-app/docs/code-splitting](https://facebook.github.io/create-react-app/docs/code-splitting)

### Analyzing the Bundle Size

This section has moved here: [https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size](https://facebook.github.io/create-react-app/docs/analyzing-the-bundle-size)

### Making a Progressive Web App

This section has moved here: [https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app](https://facebook.github.io/create-react-app/docs/making-a-progressive-web-app)

### Advanced Configuration

This section has moved here: [https://facebook.github.io/create-react-app/docs/advanced-configuration](https://facebook.github.io/create-react-app/docs/advanced-configuration)

### Deployment

This section has moved here: [https://facebook.github.io/create-react-app/docs/deployment](https://facebook.github.io/create-react-app/docs/deployment)

### `yarn run build` fails to minify

This section has moved here: [https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify](https://facebook.github.io/create-react-app/docs/troubleshooting#npm-run-build-fails-to-minify)
