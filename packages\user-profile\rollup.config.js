import babel from '@rollup/plugin-babel';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import image from '@rollup/plugin-image';
import packageJson from './package.json';

const jsExtensions = ['.js', '.jsx'];

export default [
  {
    input: 'src/lib/index.jsx',
    output: [
      {
        file: packageJson.main,
        format: 'cjs',
        sourcemap: true,
      },
      {
        file: packageJson.module,
        format: 'es',
        sourcemap: true,
      },
    ],
    external: ['next', 'react', '@mui/material'],
    plugins: [
      resolve({
        extensions: jsExtensions,
      }),
      commonjs(),
      babel({
        presets: ['@babel/preset-react'],
        exclude: './node_modules/**',
        extensions: jsExtensions,
      }),
      image(),
    ],
  },
];
