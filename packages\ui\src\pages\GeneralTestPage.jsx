import React from 'react';
import { Box, But<PERSON>, Grid, Stack } from '@mui/material';
import {
  CambianBranding,
  CambianCalendar,
  CambianDatePicker,
  CambianSelectionButton,
  BodyContainer,
  ComponentBorder,
  DoublePanelBorder,
  PanelBorder,
  SingleColumnPage,
  TwoColumnPage,
  HeaderStyle,
  CambianTooltip,
} from '../lib/components';

function Empty(props) {
  return <div>This component is empty</div>;
}

function GeneralTestPage(props) {
  const [component, setComponent] = React.useState(<Empty />);

  const updateComponent = (component) => {
    setComponent(component);
  };

  // Used for <CambianCalendar /> and <CambianDatePicker />
  const [date, setDate] = React.useState(new Date());
  const [dob, setDob] = React.useState(null);
  const [reRenderCalendar, setReRenderCalendar] = React.useState(false);
  const [renderDatePicker, setRenderDatePicker] = React.useState(false);
  const minDate = new Date();

  const handleSetDate = (value) => {
    console.log(value);
    setDate(value);
    setReRenderCalendar(true);
  };

  const handleDobChange = (value) => {
    setDob(value);
    setRenderDatePicker(true);
  };

  React.useEffect(() => {
    if (reRenderCalendar) {
      updateComponent(
        <Stack direction="column" alignItems="center" spacing={2} sx={{ backgroundColor: '#fff !important' }}>
          <CambianCalendar minDate={minDate} date={date} onChange={handleSetDate} />
        </Stack>,
      );
      setReRenderCalendar(false);
    }
    // if (renderDatePicker) {
    //   updateComponent(
    //     <Stack direction="column" alignItems="center" spacing={2} sx={{ m: 2 }}>
    //       <CambianDatePicker
    //         size="large"
    //         label="Date of Birth"
    //         required={true}
    //         value={dob}
    //         isReadOnly={false}
    //         format="yyyy-MM-dd"
    //         mask="____-__-__"
    //         disableFuture={true}
    //         fullWidth={true}
    //         onChange={(newValue) => handleDobChange(newValue)}
    //         placeHolder="yyyy-mm-dd"
    //         error={false}
    //         requiredFieldText="Date of birth is required"
    //         invalidFieldText="Enter valid date"
    //       />
    //     </Stack>
    //   );
    //   setRenderDatePicker(false);
    // }
  }, [reRenderCalendar, renderDatePicker]);

  // For <CambianSelectionButton />
  const [showButton, setShowButton] = React.useState(false);
  const [buttonSelected1, setButtonSelected1] = React.useState(false);
  const [buttonSelected2, setButtonSelected2] = React.useState(false);

  const handleButton = (index) => {
    if (index === 1) {
      setButtonSelected1(!buttonSelected1);
    } else {
      setButtonSelected2(!buttonSelected2);
    }
  };

  React.useEffect(() => {
    if (showButton) {
      updateComponent(
        <Stack
          direction="column"
          alignItems="center"
          spacing={2}
          sx={{ m: 2, p: 2, backgroundColor: 'white !important' }}
        >
          <CambianSelectionButton
            variant="outlined"
            isButtonSelected={buttonSelected1}
            buttonText="8:00 AM"
            onClick={() => handleButton(1)}
          />
          <CambianSelectionButton
            variant="outlined"
            isButtonSelected={buttonSelected2}
            buttonText="8:30 AM"
            onClick={() => handleButton(2)}
          />
        </Stack>,
      );
    }
  }, [buttonSelected1, buttonSelected2]);

  function LeftHandComponent() {
    return (
      <PanelBorder>
        <Grid container justifyContent="center">
          <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2} paddingTop={2}>
            Example Left
          </Grid>
        </Grid>
      </PanelBorder>
    );
  }

  function RightHandComponent() {
    return <DoublePanelBorder>Example Right</DoublePanelBorder>;
  }

  function renderContent() {
    return <div>Margin left is set to 1, margin right is set to 2.</div>;
  }

  return (
    <>
      <div style={{ backgroundColor: '#F5F5F5' }}>
        <HeaderStyle>Core Page</HeaderStyle>
        <Grid container direction="row">
          <Box component={Grid} item xs={3} display={{ xs: 'block' }}>
            <Button
              variant="contained"
              onClick={() => {
                updateComponent(<CambianBranding imageUrl="cambian_logo.png" line1="Cambian" line2="Navigator" />);
              }}
            >
              Cambian Brand
            </Button>

            <Button
              variant="contained"
              onClick={() => {
                updateComponent(
                  <Stack direction="column" alignItems="center" spacing={2} sx={{ backgroundColor: '#fff !important' }}>
                    <CambianCalendar minDate={minDate} date={date} onChange={handleSetDate} />
                  </Stack>,
                );
              }}
            >
              Cambian Calendar
            </Button>

            <Button
              variant="contained"
              onClick={() => {
                updateComponent(
                  <Stack
                    direction="column"
                    alignItems="center"
                    spacing={2}
                    sx={{ m: 2, backgroundColor: '#fff !important' }}
                  >
                    <CambianDatePicker
                      size="large"
                      label="Date of Birth"
                      required={true}
                      value={dob}
                      isReadOnly={false}
                      format="yyyy-MM-dd"
                      mask="____-__-__"
                      disableFuture={true}
                      fullWidth={true}
                      onChange={(newValue) => handleDobChange(newValue)}
                      placeHolder="yyyy-mm-dd"
                      error={false}
                      requiredFieldText="Date of birth is required"
                      invalidFieldText="Enter valid date"
                    />
                  </Stack>,
                );
              }}
            >
              Cambian DatePicker
            </Button>
            <Button
              variant="contained"
              onClick={() => {
                setShowButton(true);
                updateComponent(
                  <Stack
                    direction="column"
                    alignItems="center"
                    spacing={2}
                    sx={{ m: 2, p: 2, backgroundColor: 'white !important' }}
                  >
                    <CambianSelectionButton
                      variant="outlined"
                      isButtonSelected={buttonSelected1}
                      buttonText="8:00 AM"
                      onClick={() => handleButton(1)}
                    />
                    <CambianSelectionButton
                      variant="outlined"
                      isButtonSelected={buttonSelected2}
                      buttonText="8:30 AM"
                      onClick={() => handleButton(2)}
                    />
                  </Stack>,
                );
              }}
            >
              Cambian Selection Button
            </Button>
          </Box>

          <Box component={Grid} item xs={9} display={{ xs: 'block' }}>
            <div
              style={{
                margin: '25px',
                outline: 'dashed 1px black',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
              }}
            >
              {component}
            </div>
          </Box>
        </Grid>
      </div>
      <br></br>
      <BodyContainer>
        <Grid sx={{ padding: 2 }}>Layout Component: PanelBorder</Grid>
        <div style={{ border: '1px solid #DEDEDE', padding: 10, margin: 10 }}>
          <PanelBorder>
            <Grid container justifyContent="center">
              <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2} paddingTop={2}>
                Use up all the space inside and position components within a grid container with padding: 2,
                paddingBottom is included in Layout component Panel Border.
              </Grid>
            </Grid>
          </PanelBorder>
        </div>

        <Grid sx={{ padding: 2 }}>Layout Component: DoublePanelBorder</Grid>
        <div style={{ border: '1px solid #DEDEDE', padding: 10, margin: 10 }}>
          <DoublePanelBorder>
            Example, for size greater than md, the left margin is set to 0 and right margin is set to 2 (16px), hence
            this can be used for the right side component in TwoColumnPage below.
          </DoublePanelBorder>
        </div>

        <Grid sx={{ padding: 2 }}>Layout Component: TwoColumnPage</Grid>
        <div style={{ border: '1px solid #DEDEDE', padding: 10, margin: 10 }}>
          <TwoColumnPage leftColumn={<LeftHandComponent />} rightColumn={<RightHandComponent />} />
        </div>

        <Grid sx={{ padding: 2 }}>Layout Component: SingleColumnPage, it includes title and subtitle</Grid>
        <div style={{ border: '1px solid #DEDEDE', padding: 10, margin: 10 }}>
          <SingleColumnPage title={'Title'} subtitle={'Subtitle'} content={renderContent()} />
        </div>

        <Grid sx={{ padding: 2 }}>Layout Component: ComponentBorder</Grid>
        <div style={{ border: '1px solid #DEDEDE', padding: 10, margin: 10 }}>
          <ComponentBorder>
            This component has padding left and right 2 but takes up 95% width with margin: 1
            <CambianTooltip title="TOOLTIP!!" placement="bottom-start">
              <span>HELP</span>
            </CambianTooltip>
          </ComponentBorder>
        </div>
      </BodyContainer>
    </>
  );
}

export default GeneralTestPage;
