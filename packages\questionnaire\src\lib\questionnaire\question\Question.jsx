import React from 'react';
import * as QuestionUtility from '../utility/questionUtility';
import ChoiceQuestion from './questionType/ChoiceQuestion';
import IntegerQuestion from './questionType/IntegerQuestion';
import CheckboxQuestion from './questionType/CheckboxQuestion';
import TextQuestion from './questionType/TextQuestion';
import DecimalQuestion from './questionType/DecimalQuestion';
import DateTimeQuestion from './questionType/DateTimeQuestion';
import ChoiceBarQuestion from './questionType/ChoiceBarQuestion';
import NumericSliderQuestion from './questionType/NumericSliderQuestion';
import LargeButtonQuestion from './questionType/LargeButtonQuestion';
import BodyDiagramQuestion from './questionType/BodyDiagramQuestion';
import DateQuestion from './questionType/DateQuestion';
import DisplayOnlyQuestion from './questionType/DisplayOnlyQuestion';
import { GridQuestion } from './questionType/GridQuestion';
import { Dropdown } from './questionType/Dropdown';

function Question(props) {
  const { question, handleQuestionResponse /* isLargeButtonEnabled */ } = props;
  if (QuestionUtility.isChoiceBarQuestion(question)) {
    return <ChoiceBarQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (QuestionUtility.isNumericSliderQuestion(question)) {
    return <NumericSliderQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (QuestionUtility.isDropdownQuestion(question)) {
    if (handleQuestionResponse) {
      return <Dropdown question={question} handleQuestionResponse={handleQuestionResponse} />;
    } else {
      return <ChoiceQuestion question={question} />;
    }
  } else if (QuestionUtility.isLargeButtonQuestion(question)) {
    return <LargeButtonQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (QuestionUtility.isRadioButtonQuestion(question)) {
    return <ChoiceQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (question.type === 'integer' && QuestionUtility.isIntegerOnlyQuestion(question)) {
    return <IntegerQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (QuestionUtility.isBodyDiagramQuestion(question)) {
    return <BodyDiagramQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (QuestionUtility.isCheckboxQuestion(question)) {
    return <CheckboxQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (question.type === 'text') {
    return <TextQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (question.type === 'decimal') {
    return <DecimalQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (question.type === 'dateTime') {
    return <DateTimeQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (question.type === 'date') {
    return <DateQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  } else if (question.type === 'display') {
    return <DisplayOnlyQuestion question={question} />;
  } else if (question.type === 'complex') {
    return <GridQuestion question={question} handleQuestionResponse={handleQuestionResponse} />;
  }

  return <div>Unknown QuestionType</div>;
}
export { Question };
