{"private": true, "workspaces": ["packages/**"], "scripts": {"lint": "eslint --fix", "format": "prettier --write \"./packages/**/*.{js,jsx,ts,tsx,css,}\" --config ./.prettierrc", "postinstall": "husky"}, "packageManager": "yarn@1.22.22", "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-react": "^7.24.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/styles": "^5.14.18", "@mui/x-data-grid": "^7.2.0", "@mui/x-date-pickers": "^7.2.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-dynamic-import-vars": "^2.1.2", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-node-resolve": "^15.2.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.34.1", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.22.3", "react-to-print": "^2.15.1", "rollup": "^2.79.1"}}