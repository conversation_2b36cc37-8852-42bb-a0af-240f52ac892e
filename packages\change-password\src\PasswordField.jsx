import React from 'react';
import { Grid, InputAdornment, IconButton } from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';
import { TextFieldElement } from 'react-hook-form-mui';

const PasswordField = ({
  label,
  name,
  formContext,
  showPassword,
  handleTogglePasswordVisibility,
  renderErrorMessages,
}) => {
  return (
    <Grid item>
      <TextFieldElement
        label={label}
        sx={{ ml: 2, mb: 2 }}
        type={showPassword ? 'text' : 'password'}
        name={name}
        required
        error={!!formContext.formState.errors[name]}
        FormHelperTextProps={{ sx: { display: 'none' } }}
        InputProps={{
          endAdornment: (
            <InputAdornment position="end">
              <IconButton onClick={handleTogglePasswordVisibility} color="primary">
                {showPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </InputAdornment>
          ),
        }}
      />
      {formContext.formState.errors[name] && renderErrorMessages(formContext.formState.errors[name].message)}
    </Grid>
  );
};

export default PasswordField;
