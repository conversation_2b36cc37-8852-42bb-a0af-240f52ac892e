import React from 'react';
import { IconButton, Stack, Typography } from '@mui/material';

const HoverIconButton = React.forwardRef((props, ref) => {
  const { menuSelectionCallback, text, textDecoration, variant, icon, color, direction, isSelected } = props;
  const [isButtonSelected, setIsButtonSelected] = React.useState();

  if (isButtonSelected !== isSelected) {
    setIsButtonSelected(isSelected);
  }

  let iconColor = isSelected ? 'secondary' : 'primary';
  let textVariant = variant === undefined ? 'subtitle1' : variant;
  let decoration = textDecoration === undefined ? 'none' : textDecoration;

  let underlineStyle = isButtonSelected ? { borderBottomStyle: 'solid', borderWidth: '2px' } : {};

  const doNothing = (event) => {};
  let callback = menuSelectionCallback === undefined ? doNothing : menuSelectionCallback;

  return (
    <IconButton
      variant="contained"
      ref={ref}
      sx={{
        '&.MuiIconButton-colorPrimary': { bgcolor: 'transparent', color: '#4D76A9' },
        '&.MuiIconButton-colorSecondary': { bgcolor: 'transparent', color: '#41638E' },
        '&.MuiButtonBase-root:hover': { bgcolor: 'transparent', color: '#41638E' },
        '&.MuiButtonBase-root:focus': { bgcolor: 'transparent', color: '#41638E' },
      }}
      color={iconColor}
      direction="row-reverse"
      onClick={(event) => {
        callback(event);
      }}
    >
      <Stack
        direction={direction === undefined ? 'column' : direction}
        alignItems="center"
        spacing={0}
        sx={underlineStyle}
      >
        {icon}
        <Typography variant={textVariant} sx={{ textDecoration: decoration }}>
          {text}
        </Typography>
      </Stack>
    </IconButton>
  );
});

export { HoverIconButton };
