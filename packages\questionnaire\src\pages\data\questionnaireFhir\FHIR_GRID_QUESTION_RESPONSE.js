export const FHIR_GRID_QUESTION_RESPONSE = {
  resourceType: 'QuestionnaireResponse',
  identifier: [],
  questionnaire: 'c12d9eb7-bf40-43e4-9b59-d021b95b8280',
  contained: [],
  status: 'completed',
  authored: '2022-11-24T12:08:17.523Z',
  extension: [
    {
      url: 'QuestionnaireResponse/questionnaire-response-type',
      valueCode: 'instrument-response',
    },
  ],
  item: [
    {
      id: 'group-639893',
      linkId: '1647245802',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      item: [
        {
          id: 'complex-639894',
          linkId: '1647262497',
          extension: [
            {
              url: 'Questionnaire/Item/complex-data-type',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/complex-value-type-name',
              valueString: 'Complex Type Hlywg4c',
            },
            {
              url: 'Questionnaire/Item/complex-value-type-id',
              valueInteger: 639867,
            },
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 639874,
            },
            {
              url: 'Questionnaire/Item/default-number-rows',
              valueInteger: 5,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Q1. Data grid question',
          item: [
            {
              linkId: '1648855996',
              item: [
                {
                  id: '639868',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/row-sequence',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1648912470',
                  text: 'Text',
                  type: 'text',
                  required: false,
                  answer: [
                    {
                      valueString: 'toe1',
                    },
                  ],
                },
                {
                  id: '639870',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 1,
                    },
                    {
                      url: 'Questionnaire/Item/row-sequence',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1656107663',
                  text: 'Numeric',
                  type: 'decimal',
                  required: false,
                  answer: [
                    {
                      valueInteger: 12,
                    },
                  ],
                },
                {
                  id: '639872',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 2,
                    },
                    {
                      url: 'Questionnaire/Item/row-sequence',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1656511950',
                  text: 'Date',
                  type: 'dateTime',
                  required: false,
                  answer: [
                    {
                      valueDate: '2022-11-01T18:30:00.000Z',
                    },
                  ],
                },
              ],
            },
          ],
          type: 'group',
        },
        {
          id: '639896',
          linkId: '1684335538',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Q2. What is your name?',
          answer: [
            {
              valueString: 'Lewis',
            },
          ],
        },
      ],
      type: 'group',
    },
    {
      id: 'group-639899',
      linkId: '1685544383',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 2,
        },
      ],
      item: [
        {
          id: '639900',
          linkId: '1685555732',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          text: 'Q1. How are you feeling today?',
          answer: [
            {
              valueCoding: {
                id: '639903',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'Not good not bad',
              },
            },
          ],
        },
        {
          id: 'complex-639909',
          linkId: '1686595975',
          extension: [
            {
              url: 'Questionnaire/Item/complex-data-type',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/complex-value-type-name',
              valueString: 'Complex Type 2jm6ZRI',
            },
            {
              url: 'Questionnaire/Item/complex-value-type-id',
              valueInteger: 639875,
            },
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 639882,
            },
            {
              url: 'Questionnaire/Item/default-number-rows',
              valueInteger: 5,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          text: 'Q2. Anyone in your family has been diagnosed with cancer',
          item: [
            {
              linkId: '1686620356',
              item: [
                {
                  id: '639876',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/row-sequence',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1686628461',
                  text: 'Name',
                  type: 'text',
                  required: false,
                  answer: [
                    {
                      valueString: 'Satyam',
                    },
                  ],
                },
                {
                  id: '639878',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 1,
                    },
                    {
                      url: 'Questionnaire/Item/row-sequence',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1686687723',
                  text: 'Age',
                  type: 'decimal',
                  required: false,
                  answer: [
                    {
                      valueInteger: 12,
                    },
                  ],
                },
                {
                  id: '639880',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/row-sequence',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1686709513',
                  text: 'Treatment Recieved',
                  type: 'text',
                  required: false,
                  answer: [
                    {
                      valueString: 'gaga',
                    },
                  ],
                },
              ],
            },
          ],
          type: 'group',
        },
      ],
      type: 'group',
    },
    {
      id: 'group-639912',
      linkId: '1691453525',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 3,
        },
      ],
      item: [
        {
          id: '639913',
          linkId: '1691461940',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 3,
            },
          ],
          text: 'Q1. Tick all that apply.',
          answer: [
            {
              valueCoding: {
                id: '639914',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'I am on medication',
              },
            },
          ],
        },
        {
          id: '639922',
          linkId: '1692459093',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 3,
            },
          ],
          text: 'Q2. Have you ever thought to harm yourself?',
          answer: [
            {
              valueCoding: {
                id: '639923',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'Yes',
              },
            },
          ],
        },
      ],
      type: 'group',
    },
    {
      id: 'group-639930',
      linkId: '1692528568',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 4,
        },
      ],
      item: [
        {
          id: 'complex-639931',
          linkId: '1692535201',
          extension: [
            {
              url: 'Questionnaire/Item/complex-data-type',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/complex-value-type-name',
              valueString: 'Complex Type akKPELC',
            },
            {
              url: 'Questionnaire/Item/complex-value-type-id',
              valueInteger: 639883,
            },
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 639888,
            },
            {
              url: 'Questionnaire/Item/default-number-rows',
              valueInteger: 5,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 4,
            },
          ],
          text: 'Q1. Please list all the major treatments you have received.',
          item: [
            {
              linkId: '1692557319',
              item: [
                {
                  id: '639884',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/row-sequence',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1692564615',
                  text: 'Treatment name',
                  type: 'text',
                  required: false,
                  answer: [
                    {
                      valueString: 'none',
                    },
                  ],
                },
                {
                  id: '639886',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/row-sequence',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1692604591',
                  text: 'Age at which treatment received',
                  type: 'text',
                  required: false,
                  answer: [
                    {
                      valueString: 'none',
                    },
                  ],
                },
              ],
            },
          ],
          type: 'group',
        },
      ],
      type: 'group',
    },
  ],
};
