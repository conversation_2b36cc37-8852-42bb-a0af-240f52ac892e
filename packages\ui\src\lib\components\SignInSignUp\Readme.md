# IMPORTANT NOTE
These components are not completed. They are being regulary updated so please keep an eye on them. It is now merged and published as 0.0.351.

# Change log
2.0.7
1. Rename SignUp's prop `termsOfUseDocumentUrl` to `userAgreementDocumentUrl`.

0.0.354
1. ** Rename SignUp's prop `password` to `passwordRestriction`. Add default value for passwordRestriction.
2. Design changes - font size, width, logo size, etc

0.0.351
1. Sign up callback and verify registration callback takes in different parameters now.
    * SignUp callback expects `username` and `password` now.
    * VerifyRegistration callback expects `username`, `password,` `firstName`, `lastName`, `verificationCode` now.

2. Renamed `buttonHandlerCallback` to be more specific to each components. For example, in `signInProps`'s `buttonHandlerCallback` is now renamed to `signInCallback`
    * SignIn: buttonHandlerCallback -> signInCallback
    * SignUp: buttonHandlerCallback -> signUpCallback
    * ForgotPassword: buttonHandlerCallback -> forgotPasswordCallback
    * VerifyRegistration: buttonHandlerCallback -> verifyRegistrationCallback
3. Use `username` over `email`. Use `usernameType` over `identificationType` in props.
4. Some bug fixes here and there.

0.0.349-SIGNIN-test
1. Initial components published.

# How to use SignInFlow component
Install this published version like the following.

```
npm install @cambianrepo/cambianreact@0.0.351
```

Import SignInFlow like the following:
```javascript
import {SignInFlow} from "@cambianrepo/cambianreact";
```

An application developer can use this `SignInFlow` component to implement their sign in process. `SigninFlow` renders `SignIn`, `SignUp`, `ForgotPassword`, and `VerifyRegistration` in step depending on its given props.

In Navigater, for example, we provide all 4 steps for the sign in process;
```jsx
<SignInFlow
    signInProps={{
      signInCallback: signInExistingUserCallback,
      byline: "Use your Cambian account",
      textBelowButton: "Don't have a Cambian account?",
    }}
    signUpProps={{
      signUpCallback: signUpNewUserCallback,
      password: passwordRestriction,
      byline: "Create a free Cambian account in a few seconds",
      textBelowButton: "Have a Cambian account?",
    }}
    forgotPasswordProps={{
      forgotPasswordCallback: forgotPasswordCallback,
    }}
    verifyRegistrationProps={{
      verifyRegistrationCallback: verfiyRegistrationCallback,
    }}
/>
```
You need to provide the props needed for each components. For example, `SignIn` component expects these props:
```
buttonHandlerCallback: signInExistingUserCallback,
byline: "Use your Cambian account",
textBelowButton: "Don't have a Cambian account?"
```
You can read the **#Component props** to figure out the props in each component.

For applications that does not want to provide "sign up" and "forgot password" features, simply do not pass the props for those components:
```jsx
<SignInFlow
    signInProps={{
      signInCallback: signInExistingUserCallback,
      logoUrl: "",
      byline: "User your Panorama account",
      textBelowButton: "Don't have a Panorama account?",
      linkBelowButtonText:"Talk to your organization adminstrator",
      usernameType: "id",
      orgName: "Panorama",
    }}
/>
```
For more examples, refer to `SignInSignUpTestPage.jsx`.

# Component props
You can check each components file to check what props it will take.

For example: line 38-50 in `SignIn.jsx` shows the props you can pass down to this component.
## 1. SignIn
```javascript
  const {
    signInCallback,
    navigateToForgotPasswordCallback,
    navigateToSignUpCallback,
    logoUrl = "/Cambian-fullLogo-blue-RGB.png",
    title = "Sign In",
    byline = "Use your account",
    buttonText = "Sign In",
    textBelowButton = "Don't have an account?",
    linkBelowButtonText = "Create a free account",
    usernameType = "email",
    orgName= ""
  } = props;
```
Notice how a lot of the props are strings and have default values.
# Crucial Callbacks
These are fuctions that composes key business logic that all application developers should write. These functions are passed to components via props The arguments of the callbacks are passed from React components.
## 1. Sign Up new user (Optional)
**Parameters:** 

username - required \
password - required

**Returns a Promise of an object like the following** 
```javascript
{ success: true, errorMsg: null } // or {success:false, errorMsg:"Internal server error"}
```
*success:* \
true - successful registration \
false - failure to register

**Example:**
```javascript
  const signUpNewUserCallback = async ({username, password}) => {
    // API call (e.g. send out email to verify a user)
    const result = await Promise.resolve({success: true, errorMsg: null})
    // returns a promise of an object with success and errorMsg
    return { success: result.success, errorMsg: result.errorMsg }
  }
```

## 2. Verify Registration Callback (Optional) 
- an username OTP(One Time Password) verification

**Parameters:** 

First Name - required \
Last Name - required \
username - required \
password - required \
verificationCode - required

**Returns a Promise of an object like the following** 
```javascript
{ success: true, errorMsg: null } // or {success:false, errorMsg:"Internal server error"}
```
*success:* \
true - successful verification \
false - failure to verify

**Example:**
```javascript
  const verfiyRegistrationCallback = async ({firstName, lastName, username, password, verficationCode}) => {
    // API call
    const result = await Promise.resolve({success: true, errorMsg: null})
    // returns a promise of an object with success and errorMsg
    return { success: result.success, errorMsg: result.errorMsg }
  }
```

## 3. Sign in user (Required)
**Parameters:** 

username(string) - required (This could be email address or an id) \
password - required

**Returns a Promise of an object like the following** 
```javascript
{ success: true, errorMsg: null } // or {success:false, errorMsg:"Internal server error"}
```
*success:* \
true - successful sign in \
false - failure to sign in

**Example:**
```javascript
  const signInExistingUserCallback = async ({username, password}) => {
    // API call to sign in a user
    const result = await Promise.resolve({success: true, errorMsg: null})
    if(result.success) {
      // Each app will navigate to wherever they want
      
      // returns a promise of an object with success and errorMsg
      return { success: true, error: result.errorMsg }
    } 
    // returns a promise of an object with success and errorMsg
    return { success:false, errorMsg: result.errorMsg }
  }

```

## 4. Forgot Password (Optional)
**Parameters:** 

username(string) - required 

**Returns a Promise of an object like the following** 
```javascript
{ success: true, errorMsg: null } // or {success:false, errorMsg:"Internal server error"}
```
*success:* \
true - if the user exists \
false - if the user does not exist
**Example:**
```javascript
  const forgotPasswordCallback = async ({username}) => {
    // API call
    const result = await Promise.resolve({success: true, errorMsg: null})
    // returns a promise of an object with success and errorMsg
    return { success: result.success, errorMsg: result.errorMsg }
  }
```

## 5. Change Password (Optional - This component is not part of the `SignInFlow` component)
**Parameters:** 

currentPassword - optional (They are not needed if the user is already authenticated and in applciation) \
newPassword - required \

**Returns a Promise of an object like the following** 
```javascript
{ success: true, errorMsg: null } // or {success:false, errorMsg:"Internal server error"}
```
*success:* \
true - successfully changed password \
false - failed to change password
**Example:**
```javascript
  const changePasswordCallback = async ({currentPassword, newPassword}) => {
    // API call
    const result = await Promise.resolve({success: true, errorMsg: null})
    // returns a promise of an object with success and errorMsg
    return { success: result.success, errorMsg: result.errorMsg }
  }
```