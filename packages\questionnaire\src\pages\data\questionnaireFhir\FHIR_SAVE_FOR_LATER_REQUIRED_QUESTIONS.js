export const FHIR_SAVE_FOR_LATER_REQUIRED_QUESTIONS = {
  resourceType: 'Questionnaire',
  id: '79a5ddbe-efcb-43ad-ba51-d58cdef79832',
  extension: [
    {
      url: 'Questionnaire/display-dial',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-description',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-score',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-score-category',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-title',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'Questionnaire/question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/trendable',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '79a5ddbe-efcb-43ad-ba51-d58cdef79832',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 0,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'default',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'default-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'X',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  identifier: [
    {
      system: 'urn:uuid',
      value: '79a5ddbe-efcb-43ad-ba51-d58cdef79832',
    },
  ],
  name: 'several questions',
  title: 'several questions',
  status: 'active',
  date: '2022-10-19T08:14:59-06:00',
  publisher: 'hyperadmin',
  description: 'several questions',
  item: [
    {
      id: 'group-728565',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      linkId: '123133',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '728568',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '129277',
          text: 'question 1',
          type: 'choice',
          required: true,
          answerOption: [
            {
              valueCoding: {
                id: '728571',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'o1',
              },
            },
            {
              valueCoding: {
                id: '728573',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'o2',
              },
            },
          ],
        },
        {
          id: '728577',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '966982',
          text: 'question 2',
          type: 'choice',
          required: true,
          answerOption: [
            {
              valueCoding: {
                id: '728580',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'o1',
              },
            },
            {
              valueCoding: {
                id: '728582',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'o2',
              },
            },
          ],
        },
        {
          id: '728586',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '877238',
          text: 'question 3',
          type: 'choice',
          required: true,
          answerOption: [
            {
              valueCoding: {
                id: '728589',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'o1',
              },
            },
            {
              valueCoding: {
                id: '728591',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'o2',
              },
            },
          ],
        },
      ],
    },
  ],
};
