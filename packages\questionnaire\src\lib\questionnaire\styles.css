/* Please do not delete this file. Needed for correct formatting while printing report */
* {
  -webkit-print-color-adjust: exact !important;
  print-color-adjust: exact !important;
  -moz-print-color-adjust: exact !important;
  -ms-print-color-adjust: exact !important;
}

@page {
  margin: 10.16mm 0in !important;
}

#items-and-responses-wrapper {
  display: block;
  width: 100%;

  height: auto;
  overflow: visible;
}

/* General print styles to ensure proper spacing */
@media print {
  @page {
    size: 330mm 427mm;
    margin: 14mm;
  }
  .questionnaireReport {
    page-break-after: avoid;
  }

  /* Add a page break before the items-and-responses-wrapper and after it */
  #items-and-responses-wrapper {
    page-break-before: avoid;
    page-break-after: avoid;
    height: auto;
    overflow: visible;
  }

  /* Ensure all direct children of the body have margin to avoid overlaps */
  body > * {
    margin-bottom: 20px;
    page-break-inside: avoid; /* Avoid breaking inside these elements */
  }
}
