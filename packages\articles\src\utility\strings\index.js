export const strings = {
  name: 'Name',
  title: 'Title',
  description: 'Description',
  edit: 'Edit',
  duplicate: 'Duplicate',
  delete: 'Delete',
  draft: 'DRAFT',
  final: 'FINAL',
  published: 'PUBLISHED',
  editor: 'Editor',
  preview: 'Preview',
  properties: 'Properties',
  saveDraft: 'Save Draft',
  publish: 'Publish',
  close: 'Close',
  import: 'Import',
  export: 'Export',
  new: 'New',
  articles: 'Articles',
  required: 'Required',
  blankArticle: 'Blank Article',
  templateGallery: 'Template Gallery',
  thumbnail: 'Thumbnail',
  summary: 'Summary',
  abstract: 'Abstract',
  save: 'Save',
  view: 'View',
  cancel: 'Cancel',
  body: 'Body',
  newArticle: 'New Article',
  search: 'Search',
  title: 'Title',
  name: 'Name',
  uploadThumbnail: 'Thumbnail',
  switchToEditor: 'Switch To Editor',
  switchToPreview: 'Switch To Preview',
  publishArticle: 'Publish Article',
  canNotEditQuestionnaireOncePublished: 'Cannot edit article once published',
  private: 'Private',
  public: 'Public',
  publishConfirmation: 'Publish Article',
  canNotEditArticleOncePublished: 'Cannot edit article once published',
  selectRepositoryYouWantToPublishIn: 'Select where you want to publish this article',
  finalizeArticle: 'Finalize article',
  finalizeDialogueQuestionText: 'You can not edit the article once finalized',
  deleteArticle: 'Delete Article',
  thisActionCanNotBeUndone: 'This action cannot be undone',
  untitledArticle: 'Unitled Article',
  draftBadge: 'Draft',
  finalBadge: 'Final',
  ok: 'Ok',
  finalize: 'Finalize',
  knowMoreAboutProperties: 'Know more about properties',
  enterYourArticleDataHere: 'Enter your article data here',
  articleNameTooltip:
    'The article name will be displayed to organization users only. It will not be displayed to individuals',
  articleTitleTooltip: 'The article title will be displayed individuals',
  articleDescriptionTooltip: 'The article description will be displayed to individuals',
};
