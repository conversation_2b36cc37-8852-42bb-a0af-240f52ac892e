{"name": "@cambianrepo/messaging", "publishConfig": {"registry": "https://npm.pkg.github.com/cambianrepo"}, "version": "0.0.7", "type": "module", "scripts": {"rollup": "rollup -c", "start": "react-scripts --max_old_space_size=5120 start", "build": "react-scripts --max_old_space_size=5120 build", "eject": "react-scripts eject", "yalc-publish": "yalc publish"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "peerDependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@mui/styles": "^5.14.18", "@mui/x-data-grid": "^6.0.0", "dayjs": "^1.11.9", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "@babel/preset-react": "^7.23.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-replace": "^3.0.0", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "prettier": "^3.1.0", "react-scripts": "^5.0.0", "rollup": "^4.6.1"}, "dependencies": {"prop-types": "^15.8.1"}, "browser": {"crypto": false}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"]}