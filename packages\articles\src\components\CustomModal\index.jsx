import React from 'react';
import { Modal, Box, Typography, Button } from '@mui/material';
import { strings } from '../../utility/strings';

export const CustomModal = ({
  open,
  onClose,
  onConfirm,
  title,
  subTitle,
  content,
  saveButtonText,
  closeButtonText,
}) => {
  return (
    <Modal open={open} onClose={onClose}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '100vh',
        }}
      >
        <Box
          sx={{
            width: 450,
            bgcolor: 'background.paper',
            boxShadow: 24,
            p: 2,
            px: 3,
          }}
        >
          <Typography variant="h5">{title}</Typography>
          <Typography sx={{ mt: 1, mb: 2 }}>{subTitle}</Typography>
          <Typography sx={{ mt: 1, mb: 2 }}>{content}</Typography>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', marginTop: 3 }}>
            <Button variant="outlined" onClick={onClose}>
              {closeButtonText}
            </Button>
            <Button
              variant="contained"
              onClick={onConfirm}
              sx={{ marginLeft: 2 }}
              color={saveButtonText === strings.delete ? 'error' : 'primary'}
            >
              {saveButtonText}
            </Button>
          </Box>
        </Box>
      </Box>
    </Modal>
  );
};
