import React, { Fragment } from 'react';
import { Box, Grid, Typography } from '@mui/material';
import { CANNOT_LOCATE_STRING } from '../../../Common/constants';
import dataValidation from '../../../Common/Utils/dataValidation';
import * as QuestionnaireUtility from '../utility/questionnaireUtility';

function DangerouslySetHtmlContent({ html, ...rest }) {
  const divRef = React.useRef(null);

  React.useEffect(() => {
    if (!html || !divRef.current) throw new Error("html prop cant't be null");

    const slotHtml = document.createRange().createContextualFragment(html); // Create a 'tiny' document and parse the html string
    divRef.current.innerHTML = ''; // Clear the container
    divRef.current.appendChild(slotHtml); // Append the new content
  }, [html, divRef]);

  return <div {...rest} ref={divRef} />;
}

function QuestionText(props) {
  const { question = '', extension, isRequired } = props || {};

  let descriptionExtension = QuestionnaireUtility.extractExtension(extension, 'Item/description');
  let description = descriptionExtension ? descriptionExtension.valueString : '';
  let questionText = isRequired ? question + ' *' : question;

  return (
    <Fragment>
      <Grid container>
        <Grid item xs={12}>
          {questionText !== CANNOT_LOCATE_STRING && !dataValidation.isDataEmpty(question) && (
            <Box sx={{ mb: 0.5 }}>
              <DangerouslySetHtmlContent html={questionText} />
            </Box>
          )}
          {description !== CANNOT_LOCATE_STRING && !dataValidation.isDataEmpty(question) && (
            <Box sx={{ my: 0 }}>
              <Typography variant="body2" dangerouslySetInnerHTML={{ __html: description }} />
            </Box>
          )}
        </Grid>
      </Grid>
    </Fragment>
  );
}

export { QuestionText };
