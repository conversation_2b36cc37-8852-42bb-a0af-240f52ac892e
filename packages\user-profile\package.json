{"name": "@cambianrepo/user-profile", "publishConfig": {"registry": "https://npm.pkg.github.com/cambianrepo"}, "version": "0.0.42", "type": "module", "engines": {"node": ">=20.0.0", "npm": "please use YARN", "yarn": ">= 1.22.18"}, "scripts": {"lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "rollup": "rollup -c", "start": "react-scripts --max_old_space_size=5120 start", "build": "react-scripts --max_old_space_size=5120 build", "format": "prettier --write \"./src/**/*.{js,jsx,css,md,json}\" --config ./.prettierrc"}, "peerDependencies": {"@mui/material": "^5.15.15", "next": "^14.2.4", "react": "^18.2.0"}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"], "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@cambianrepo/ui": "0.0.55", "react-imask": "^7.6.1"}}