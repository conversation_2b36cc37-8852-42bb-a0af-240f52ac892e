import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Link,
  CircularProgress,
  Box,
  useMediaQuery,
  TextField,
  FormHelperText,
} from '@mui/material';
import { UncontrolledPassword } from './UncontrolledPassword';
import { validatePassword } from './Regex';
import cambianLogo from './cambian-logo.png';
import { EmailResentMessage } from './EmailResentMessage';
import { DEFAULT_ERROR_MSG, CHANGE_PASSWORD_ERROR_MESSAGES } from './constant';

function ChangePassword(props) {
  const {
    changePasswordCallback,
    navigateCallback,
    resendVerificationCodeCallback,
    checkCurrentPassword,
    verifyForgotPasswordCode,
    password: { min = 1, max, requireLowercase, requireUppercase, requireNumber, requireSpecial } = {},
    logoUrl = cambianLogo,
    title = 'Change Password',
    byline = `Enter your new password ${max ? `from ${min}-${max}` : `with more than ${min}`} characters in length`,
    buttonText = 'Save',
    linkBelowButtonText = 'Back',
    orgName = 'Cambian',
    inputBoxSize = 'small',
    inputLabelFontSize = { xs: 14, sm: 16 },
    inputFontSize = { xs: 15, sm: 17 },
    setWidth = { xs: '100%', sm: '40ch' },
  } = props;
  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const [isLoading, setIsLoading] = React.useState(false);

  const currentPasswordInputRef = React.useRef();
  const newPasswordInputRef = React.useRef();
  const confirmPasswordInputRef = React.useRef();
  const otpInputRef = React.useRef();

  const [otpValidSchema, setOtpValidSchema] = React.useState({});
  const [currentPasswordValidSchema, setCurrentPasswordValidSchema] = React.useState({});
  const [newPasswordValidSchema, setNewPasswordValidSchema] = React.useState({});
  const [confirmPasswordValidSchema, setConfirmPasswordValidSchema] = React.useState({});

  const [serverResponseErrorMsg, setServerResponseErrorMsg] = React.useState(null);
  const [emailSentMessage, setEmailSentMessage] = React.useState('');

  const checkPasswordMatch = () => {
    if (newPasswordInputRef.current && confirmPasswordInputRef.current) {
      return newPasswordInputRef.current.value === confirmPasswordInputRef.current.value;
    }
  };

  const validateForm = () => {
    const currentPasswordValidationResult =
      checkCurrentPassword && validatePassword({ min: 1, max: null, value: currentPasswordInputRef.current.value });
    const newPasswordValidationResult = validatePassword({
      min,
      max,
      requireLowercase,
      requireUppercase,
      requireNumber,
      requireSpecial,
      value: newPasswordInputRef.current.value,
    });
    const confirmPasswordValidationResult = validatePassword({
      min,
      max,
      requireLowercase,
      requireUppercase,
      requireNumber,
      requireSpecial,
      value: confirmPasswordInputRef.current.value,
    });
    const otpValidationResult = verifyForgotPasswordCode && { isMinLengthValid: otpInputRef.current.value.length > 0 };

    setOtpValidSchema(otpValidationResult);
    setCurrentPasswordValidSchema(currentPasswordValidationResult);
    setNewPasswordValidSchema(newPasswordValidationResult);
    setConfirmPasswordValidSchema(confirmPasswordValidationResult);

    return {
      currentPasswordValidationResult,
      newPasswordValidationResult,
      confirmPasswordValidationResult,
      otpValidationResult,
    };
  };

  const handleSubmit = async () => {
    const {
      currentPasswordValidationResult,
      newPasswordValidationResult,
      confirmPasswordValidationResult,
      otpValidationResult,
    } = validateForm();

    const isCurrentPasswordValid =
      checkCurrentPassword && !Object.values(currentPasswordValidationResult).some((v) => v === false);
    const isNewPasswordValid = !Object.values(newPasswordValidationResult).some((v) => v === false);
    const isConfirmPasswordValid = !Object.values(confirmPasswordValidationResult).some((v) => v === false);
    const isOtpValid = !verifyForgotPasswordCode || (verifyForgotPasswordCode && otpValidationResult.isMinLengthValid);

    if (checkCurrentPassword && !isCurrentPasswordValid) {
      currentPasswordInputRef.current.focus();
    } else if (!isOtpValid) {
      otpInputRef.current.focus();
    } else if (!isNewPasswordValid) {
      newPasswordInputRef.current.focus();
    } else if (!isConfirmPasswordValid) {
      confirmPasswordInputRef.current.focus();
    } else if (verifyForgotPasswordCode && !isOtpValid) {
      otpInputRef.current.focus();
    }

    if (
      (!checkCurrentPassword || isCurrentPasswordValid) &&
      isNewPasswordValid &&
      isConfirmPasswordValid &&
      checkPasswordMatch() &&
      (!verifyForgotPasswordCode || isOtpValid)
    ) {
      setIsLoading(true);
      const { success, errorMsg, errorCode } = await changePasswordCallback({
        ...(checkCurrentPassword && { currentPassword: currentPasswordInputRef.current.value }),
        ...(verifyForgotPasswordCode && { verificationCode: otpInputRef.current.value }),
        newPassword: newPasswordInputRef.current.value,
      });
      if (!success) {
        const displayMessage = CHANGE_PASSWORD_ERROR_MESSAGES[errorCode] || DEFAULT_ERROR_MSG;

        // Display the appropriate message
        setServerResponseErrorMsg(displayMessage);
        setIsLoading(false);
      }
    } else {
      setServerResponseErrorMsg(null);
      setIsLoading(false);
    }
  };

  const handleResendVerificationCallback = async () => {
    setEmailSentMessage('');
    const { success, errorMsg } = await resendVerificationCodeCallback();
    if (!success) {
      console.error(errorMsg);
      if (errorMsg) {
        setServerResponseErrorMsg(errorMsg);
      } else {
        setServerResponseErrorMsg(DEFAULT_ERROR_MSG);
      }
    } else {
      setEmailSentMessage('Email Sent');
    }
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  const isOtpInvalid = !verifyForgotPasswordCode || otpValidSchema.isMinLengthValid === false;
  return (
    <Stack direction="column" spacing={1.5} sx={{ width: setWidth }}>
      {logoUrl && (
        <Box
          component="img"
          sx={{
            marginLeft: '-1%',
            width: { xs: '50%', sm: '55%' },
            minWidth: '100px',
            minHeight: '30px',
            height: { xs: '40%', sm: '45%' },
            marginBottom: -0.5,
          }}
          src={logoUrl}
          alt={`${orgName} full logo`}
        />
      )}

      <Typography sx={{ fontSize: { xs: 24, sm: 30 }, fontWeight: 500 }}>{title}</Typography>
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{byline}</Typography>
      {checkCurrentPassword && (
        <UncontrolledPassword
          id="currentPassword"
          text="Current Password"
          type="text"
          autoFocus="true"
          isRequired={true}
          labelFontSize={inputLabelFontSize}
          inputFontSize={inputFontSize}
          inputBoxSize={inputBoxSize}
          ref={currentPasswordInputRef}
          min={1}
          validSchema={currentPasswordValidSchema}
          onKeyDown={handleKeyDown}
        />
      )}
      {verifyForgotPasswordCode && (
        <>
          <TextField
            autoFocus
            required
            size="small"
            id="verificationCode"
            label="Verification Code"
            inputProps={{ style: { fontSize: isXs ? 14 : 17 } }}
            InputLabelProps={{ style: { fontSize: isXs ? 13 : 16, marginTop: isXs ? 2 : undefined } }}
            InputProps={{ label: isXs ? 'Verification C_' : 'Verification Code' }}
            error={isOtpInvalid}
            inputRef={otpInputRef}
            onKeyDown={handleKeyDown}
          />
          {isOtpInvalid && (
            <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '2px !important' }}>
              Please enter your verification code
            </FormHelperText>
          )}
        </>
      )}
      <UncontrolledPassword
        id="newPassword"
        text="New Password"
        type="text"
        autoFocus={!(checkCurrentPassword || verifyForgotPasswordCode) && 'true'}
        isRequired={true}
        min={min}
        max={max}
        requireLowercase={requireLowercase}
        requireUppercase={requireUppercase}
        requireNumber={requireNumber}
        requireSpecial={requireSpecial}
        matchingPassword={checkPasswordMatch()}
        labelFontSize={inputLabelFontSize}
        inputFontSize={inputFontSize}
        inputBoxSize={inputBoxSize}
        ref={newPasswordInputRef}
        validSchema={newPasswordValidSchema}
        onKeyDown={handleKeyDown}
      />
      <UncontrolledPassword
        id="confirmPassword"
        text="Confirm Password"
        type="text"
        isRequired={true}
        min={min}
        max={max}
        requireLowercase={requireLowercase}
        requireUppercase={requireUppercase}
        requireNumber={requireNumber}
        requireSpecial={requireSpecial}
        matchingPassword={checkPasswordMatch()}
        labelFontSize={inputLabelFontSize}
        inputFontSize={inputFontSize}
        inputBoxSize={inputBoxSize}
        ref={confirmPasswordInputRef}
        validSchema={confirmPasswordValidSchema}
        onKeyDown={handleKeyDown}
      />
      <Button
        variant="contained"
        disabled={isLoading}
        onClick={handleSubmit}
        sx={{ fontSize: { xs: 17, sm: 19 }, fontWeight: 600, padding: '11.5px 12px', lineHeight: 1 }}
      >
        {isLoading ? <CircularProgress size={isXs ? 15 : 19} /> : buttonText}
      </Button>
      {serverResponseErrorMsg && (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
          {serverResponseErrorMsg}
        </Typography>
      )}
      {resendVerificationCodeCallback ? (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} component={'div'}>
          <EmailResentMessage message={emailSentMessage} />
          If you don't see a code in your inbox, check your spam folder. If it's not there, the email address may not be
          confirmed, or it may not match an existing account. <br></br>
          <br></br>
          <Link component="button" onClick={handleResendVerificationCallback}>
            Resend the code
          </Link>{' '}
          {
            <>
              or{' '}
              <Link component="button" onClick={navigateCallback}>
                update your email
              </Link>
            </>
          }
        </Typography>
      ) : (
        <Link
          component="button"
          onClick={navigateCallback}
          sx={{
            textAlign: 'left',
            width: 'fit-content',
            fontSize: { xs: 11, sm: 12 },
          }}
        >
          {linkBelowButtonText}
        </Link>
      )}
    </Stack>
  );
}

export { ChangePassword };
