import React from 'react';
import { Button, Stack, Typography, Box } from '@mui/material';
import cambian<PERSON>ogo from './cambian-logo.png';

function EmailSentMessage(props) {
  const {
    username,
    navigateCallback,
    logoUrl = cambianLogo,
    title = 'Account recovery',
    orgName = 'Cambian',
    serviceName = 'Navigator',
    byline = `If this email address is associated with a ${orgName} ${serviceName} account, a password reset link will be emailed to ${username}`,
    buttonText = 'Back to Sign In',
  } = props;

  return (
    <Stack direction="column" spacing={1.5} sx={{ width: { xs: '100%', sm: '40ch' } }}>
      <Box
        component="img"
        sx={{
          marginLeft: '-1%',
          width: { xs: '50%', sm: '55%' },
          minWidth: '100px',
          minHeight: '30px',
          height: { xs: '40%', sm: '45%' },
          marginBottom: -0.5,
        }}
        src={logoUrl}
        alt={`${orgName} full logo`}
      />
      <Typography sx={{ fontSize: { xs: 24, sm: 30 }, fontWeight: 500 }}>{title}</Typography>
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{byline}</Typography>
      <Button
        variant="contained"
        onClick={navigateCallback}
        sx={{ fontSize: { xs: 17, sm: 19 }, fontWeight: 600, padding: '11.5px 12px', lineHeight: 1 }}
      >
        {buttonText}
      </Button>
    </Stack>
  );
}

export { EmailSentMessage };
