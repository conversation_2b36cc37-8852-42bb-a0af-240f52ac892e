export const FHIR_NUMERIC_QUESTIONS = {
  resourceType: 'Questionnaire',
  date: '2024-03-29T01:26:51.818Z',
  name: 'new questionnarie',
  title: 'Title',
  description: 'haha',
  extension: [
    {
      url: 'display-dial',
      valueBoolean: false,
    },
    {
      url: 'display-description',
      valueBoolean: true,
    },
    {
      url: 'display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'display-score',
      valueBoolean: false,
    },
    {
      url: 'display-score-category',
      valueBoolean: false,
    },
    {
      url: 'display-title',
      valueBoolean: true,
    },
    {
      url: 'questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'trendable',
      valueBoolean: false,
    },
    {
      url: 'pdftemplate-id',
      valueString: '',
    },
    {
      url: 'pdftemplate-name',
      valueString: '',
    },
    {
      url: 'question-identifier-prefix',
      valueString: 'Item',
    },
    {
      url: 'question-identifier-next-sequence',
      valueInteger: 5,
    },
    {
      url: 'htmltemplate-base64',
      valueString: '',
    },
    {
      url: 'pdftemplate-base64',
      valueString: '',
    },
    {
      url: 'list-of-score-definitions',
      extension: [
        {
          url: 'score-id',
          valueCode: 'd0e11c19-f721-4048-82d7-81b517880955',
        },
        {
          url: 'score-sequence',
          valueInteger: 0,
        },
        {
          url: 'score-name',
          valueString: 'NewVariable',
        },
        {
          url: 'list-of-formula-definitions',
          extension: {
            extension: [
              {
                extension: [
                  {
                    url: 'formula-name',
                    valueString: 'NewVariable-F1',
                  },
                  {
                    url: 'mathematical-expression',
                    valueString: '',
                  },
                  {
                    url: 'selection-rule',
                    valueString: 'Select Rule',
                  },
                ],
                url: 'set-of-api-formula',
              },
            ],
            url: 'list-of-formula-definitions',
          },
        },
      ],
    },
  ],
  identifier: [
    {
      use: 'old',
      system: 'http://www.cambian.com/questionnaire/identifier',
      value: 'd3ea065c-2d4f-4d38-b4c7-fc4b28d21f78',
      period: {
        start: '2023-12-13T14:06:06+00:00',
        end: '2023-12-13T14:15:33+00:00',
      },
    },
    {
      use: 'usual',
      system: 'urn:uuid',
      value: 'f6356947-59d8-495b-abf7-47973d3a1968',
      period: {
        start: '2023-12-13T14:06:06+00:00',
      },
    },
  ],
  item: [
    {
      linkId: 'Group1',
      item: [
        {
          id: '61a4AW9aYE2Bm9r2pn13Kx',
          linkId: 'Item1',
          type: 'display',
          text: 'This is a est',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5542,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
        },
      ],
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
    },
  ],
  publisher: 'Cambian',
  status: 'final',
  id: '3067bbb5-f771-42b4-beac-9678c7d04b15',
};
