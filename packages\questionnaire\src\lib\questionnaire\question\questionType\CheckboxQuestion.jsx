import React, { Fragment, useState } from 'react';
import * as QuestionUtility from '../../utility/questionUtility';
import { Checkbox, FormControlLabel, FormGroup, Stack, TextField } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import { styled } from '@mui/material/styles';
import { extractExtension } from '../../utility/questionnaireUtility';

const StyledFormControlLabel = styled((props) => <FormControlLabel {...props} />)(({ theme, selected }) => ({
  '.MuiFormControlLabel-label': selected && {
    color: theme.palette.primary.main,
  },
}));

function CheckboxQuestion(props) {
  const { question, handleQuestionResponse } = props || {};
  const otherOptionExtensionUrl = 'Item/AnswerOption/ValueCoding/other-option';

  const [isReadOnly, setIsReadOnly] = React.useState(() => handleQuestionResponse === undefined);

  let answers = question.question.answerOption === undefined ? [] : question.question.answerOption;
  const rowLayout =
    QuestionUtility.extractHorizontalOrientation(question) == null
      ? question.question.answerOption === undefined
        ? false
        : question.question.answerOption.length > 5
      : QuestionUtility.extractHorizontalOrientation(question);

  let checkedAnswers = {};
  answers.forEach((item, index) => {
    if (question.answer.find((res) => res.valueCoding.id === item.valueCoding.id)) {
      checkedAnswers[item.valueCoding.id] = true;
    } else {
      checkedAnswers[item.valueCoding.id] = false;
    }
  });

  const findOtherOptionTextResponses = () => {
    let otherOptionResponses = {};
    if (question.answer && Array.isArray(question.answer) && question.answer.length > 0) {
      question.answer.forEach((answer) => {
        const { otherOptionValue } = QuestionUtility.extractOtherOptionDetails(answer.valueCoding.extension);
        otherOptionResponses[answer.valueCoding.id] = otherOptionValue;
      });
    } else if (answers) {
      answers.forEach((answerOption) => {
        const { otherOptionValue } = QuestionUtility.extractOtherOptionDetails(answerOption.valueCoding.extension);
        otherOptionResponses[answerOption.valueCoding.id] = otherOptionValue;
      });
    }
    return otherOptionResponses;
  };

  const [response, setResponse] = useState(checkedAnswers);
  const [otherOptionResponse, setOtherOptionResponse] = useState(() => findOtherOptionTextResponses());

  const handleChange = (event) => {
    if (!isReadOnly) {
      checkedAnswers[event.target.value] = event.target.checked;
      setResponse(checkedAnswers);
      setResponse((prevResponse) => {
        const updatedResponse = { ...prevResponse, [event.target.value]: event.target.checked };
        return updatedResponse;
      });

      question.answer = [];
      question.question.answerOption.forEach((element, index) => {
        if (checkedAnswers[element.valueCoding.id]) {
          if (otherOptionResponse[element.valueCoding.id]) {
            const answerOption = { ...element };
            const textResponse = otherOptionResponse[element.valueCoding.id];
            const additionalTextResponseData = `id:${element.valueCoding.id},question:${element.valueCoding.display},${
              textResponse ? `answer:${textResponse}` : ``
            }`;
            answerOption.valueCoding.extension.forEach((extension) => {
              if (extension.url === otherOptionExtensionUrl || otherOptionExtensionUrl.includes(extension.url)) {
                extension.valueString = additionalTextResponseData;
              }
            });
            question.answer.push(answerOption);
          } else {
            question.answer.push(element);
          }
        }
      });
      handleQuestionResponse(question);
    }
  };

  const handleAdditionalTextboxChange = (event, selectedAnswer, otherOptionExtension) => {
    const TEXT_MAX_LENGTH = 70;
    if (!isReadOnly) {
      let characterLimit = TEXT_MAX_LENGTH;
      let textResponse = event.target.value.substring(0, characterLimit);
      setOtherOptionResponse((prevValue) => ({
        ...prevValue,
        [selectedAnswer.valueCoding.id]: textResponse,
      }));
      let updatedQuestion = question;
      let additionalTextResponseData = '';
      if (otherOptionExtension) {
        additionalTextResponseData = `id:${selectedAnswer.valueCoding.id},question:${
          selectedAnswer.valueCoding.display
        },${textResponse ? `answer:${textResponse}` : ``}`;
      }

      updatedQuestion.answer.forEach((answer) => {
        if (answer.valueCoding.id === selectedAnswer.valueCoding.id) {
          answer.valueCoding.extension.forEach((extension) => {
            if (extension.url === otherOptionExtensionUrl || otherOptionExtensionUrl.includes(extension.url)) {
              extension.valueString = additionalTextResponseData;
            }
          });
        }
      });

      question.answer = updatedQuestion.answer;
      handleQuestionResponse(question);
    }
  };

  return (
    <Fragment>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <FormGroup row={rowLayout} name="checkbox-group">
        {answers.map((item, index) => {
          const otherOptionExtension = extractExtension(item.valueCoding.extension, otherOptionExtensionUrl);
          const otherOptionAvailable = otherOptionExtension ? otherOptionExtension.valueString : '';
          const { otherOptionId, otherOptionValue } = QuestionUtility.extractOtherOptionDetails(
            item.valueCoding.extension,
          );

          return (
            <Stack
              key={item.valueCoding.id}
              direction={{ xs: 'column', sm: 'row' }}
              alignItems={{ xs: 'flex-start', sm: 'center' }}
            >
              <StyledFormControlLabel
                sx={{ mb: -1.5 }}
                label={item.valueCoding.display}
                selected={response[item.valueCoding.id]}
                control={
                  <Checkbox
                    size="small"
                    value={item.valueCoding.id}
                    checked={response[item.valueCoding.id]}
                    onChange={(event) => handleChange(event)}
                  />
                }
              />
              {otherOptionAvailable && otherOptionId && response[item.valueCoding.id] && (
                <TextField
                  size="small"
                  type="text"
                  multiline={isReadOnly}
                  inputProps={{
                    style: !isReadOnly ? { height: '15px' } : {},
                  }}
                  autoComplete="off"
                  sx={{
                    pt: 1,
                    width: { xs: '100%', sm: `${otherOptionValue.length}ch` },
                    minWidth: { sm: '30ch', md: '25ch' },
                    maxWidth: { sm: '45ch', md: '60ch' },
                    overflow: 'hidden',
                  }}
                  value={isReadOnly ? otherOptionValue : otherOptionResponse[item.valueCoding.id]}
                  onChange={(event) => handleAdditionalTextboxChange(event, item, otherOptionExtension)}
                />
              )}
            </Stack>
          );
        })}
      </FormGroup>
      <Explanation question={question} />
    </Fragment>
  );
}

export default CheckboxQuestion;
