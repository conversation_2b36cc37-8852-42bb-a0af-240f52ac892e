export const HTML_TEMPLATE_REPORT_RESPONSE = {
  resourceType: 'QuestionnaireResponse',
  identifier: [],
  questionnaire: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
  contained: [],
  status: 'completed',
  authored: '2023-03-22T11:27:16.602Z',
  extension: [
    {
      url: 'QuestionnaireResponse/questionnaire-response-type',
      valueCode: 'instrument-response',
    },
    {
      url: 'QuestionnaireResponse/calculated-scores',
      extension: [
        {
          url: 'QuestionnaireResponse/score',
          valueString: 'cv_percentage:0.9586667228456691',
        },
        {
          url: 'QuestionnaireResponse/score',
          valueString: 'heart_validation:24.580578538751876',
        },
      ],
    },
  ],
  item: [
    {
      id: 'group-567175',
      linkId: 'QUS1QGS0',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      item: [
        {
          id: '567177',
          linkId: 'QUS1QGS1',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Sex assigned at birth',
          answer: [
            {
              valueCoding: {
                id: '567183',
                code: '2',
                display: 'Male',
              },
            },
          ],
          type: 'choice',
        },
        {
          id: '567187',
          linkId: 'QUS1QGS2',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Age (years)',
          answer: [
            {
              valueDecimal: '25',
            },
          ],
          type: 'decimal',
        },
        {
          id: '567193',
          linkId: 'QUS1QGS3',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Systolic blood pressure weekly average (mmHg)',
          answer: [
            {
              valueDecimal: '120',
            },
          ],
          type: 'decimal',
        },
        {
          id: '567199',
          linkId: 'QUS1QGS4',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Blood pressure treated with medication',
          answer: [
            {
              valueCoding: {
                id: '567205',
                code: '2',
                display: 'No',
              },
            },
          ],
          type: 'choice',
        },
        {
          id: '567209',
          linkId: 'QUS1QGS5',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 5,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Smoker',
          answer: [
            {
              valueCoding: {
                id: '567215',
                code: '2',
                display: 'No',
              },
            },
          ],
          type: 'choice',
        },
        {
          id: '567219',
          linkId: 'QUS1QGS6',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 6,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Diabetes',
          answer: [
            {
              valueCoding: {
                id: '567225',
                code: '2',
                display: 'No',
              },
            },
          ],
          type: 'choice',
        },
        {
          id: '567229',
          linkId: 'QUS1QGS7',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 7,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Assessment type',
          answer: [
            {
              valueCoding: {
                id: '567235',
                code: '2',
                display: 'Body mass index (BMI)',
              },
            },
          ],
          type: 'choice',
        },
        {
          id: '567253',
          linkId: 'QUS1QGS10',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 10,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Height (cm)',
          answer: [
            {
              valueDecimal: '180',
            },
          ],
          type: 'decimal',
        },
        {
          id: '567260',
          linkId: 'QUS1QGS11',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 11,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          text: 'Weight (kg)',
          answer: [
            {
              valueDecimal: '80',
            },
          ],
          type: 'decimal',
        },
      ],
      type: 'group',
    },
  ],
};
