import React from 'react';
import { styled } from '@mui/material/styles';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';

const CambianTooltip = styled(({ className, placement, ...props }) => (
  <Tooltip
    {...props}
    placement={placement}
    enterTouchDelay={500}
    enterDelay={500}
    classes={{ popper: className }}
    arrow
  />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.white,
    color: 'rgba(0, 0, 0, 0.87)',
    boxShadow: theme.shadows[1],
    marginTop: '0px',
  },
  [`& .${tooltipClasses.arrow}`]: {
    '&:before': {
      border: `1px solid ${theme.palette.common.white}`,
      backgroundColor: 'white',
      boxShadow: '2px 2px 5px rgba(0, 0, 0, 0.2)',
    },
  },
  [`&.${tooltipClasses.popper}[data-popper-placement*="bottom"] .${tooltipClasses.tooltip}`]: {
    marginTop: '5px',
    marginLeft: '20px',
  },
  [`&.${tooltipClasses.popper}[data-popper-placement*="top"] .${tooltipClasses.tooltip}`]: {
    marginBottom: '5px',
  },
}));

export { CambianTooltip };
