export const FHIR_MULTIPLE_CHOICE_OTHER_RESPONSE = {
  resourceType: 'QuestionnaireResponse',
  id: '6c58e517-f952-4ec3-8133-011cbf28951b',
  extension: [
    {
      url: 'QuestionnaireResponse/questionnaire-response-type',
      valueCode: 'instrument-response',
    },
    {
      url: 'QuestionnaireResponse/calculated-scores',
      extension: [
        {
          url: 'QuestionnaireResponse/score',
          valueString: 'default:1.00',
        },
      ],
    },
  ],
  questionnaire: '23e5c796-3929-427d-9a78-871a2eca98fe',
  status: 'completed',
  authored: '2023-08-04T12:19:06-05:00',
  _authored: {
    extension: [
      {
        url: 'http://hl7.org/fhir/StructureDefinition/tz-code',
        valueString: 'Africa/Abidjan',
      },
    ],
  },
  item: [
    {
      id: 'group-743870',
      extension: [
        {
          url: 'Questionnaire/Item/question-type',
          valueString: 'group',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueString: '1',
        },
      ],
      item: [
        {
          id: '743871',
          extension: [
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueString: '1',
            },
            {
              url: 'Questionnaire/Item/question-type',
              valueString: 'choice',
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueString: '1',
            },
          ],
          linkId: 'Item1',
          text: 'Chwckbox1',
          answer: [
            {
              valueCoding: {
                id: '743878',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743878,question:4,answer:4,',
                  },
                ],
                code: '4',
                display: '4',
              },
            },
          ],
        },
        {
          id: '743882',
          extension: [
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueString: '1',
            },
            {
              url: 'Questionnaire/Item/question-type',
              valueString: 'choice',
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueString: '2',
            },
          ],
          linkId: 'Item2',
          text: 'radiobutton1',
          answer: [
            {
              valueCoding: {
                id: '743889',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743889,question:4,answer:4,',
                  },
                ],
                code: '4',
                display: '4',
              },
            },
          ],
        },
        {
          id: '743893',
          extension: [
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueString: '1',
            },
            {
              url: 'Questionnaire/Item/question-type',
              valueString: 'choice',
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueString: '3',
            },
          ],
          linkId: 'Item3',
          text: 'checkbox2',
          answer: [
            {
              valueCoding: {
                id: '743900',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743900,question:4,answer:4,',
                  },
                ],
                code: '4',
                display: '4',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-743917',
      extension: [
        {
          url: 'Questionnaire/Item/question-type',
          valueString: 'group',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueString: '2',
        },
      ],
      item: [
        {
          id: '743918',
          extension: [
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueString: '2',
            },
            {
              url: 'Questionnaire/Item/question-type',
              valueString: 'choice',
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueString: '1',
            },
          ],
          linkId: 'Item4',
          text: 'checkbox 3',
          answer: [
            {
              valueCoding: {
                id: '743923',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743923,question:3,answer:3,',
                  },
                ],
                code: '3',
                display: '3',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-743932',
      extension: [
        {
          url: 'Questionnaire/Item/question-type',
          valueString: 'group',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueString: '3',
        },
      ],
      item: [
        {
          id: '743933',
          extension: [
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueString: '3',
            },
            {
              url: 'Questionnaire/Item/question-type',
              valueString: 'choice',
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueString: '1',
            },
          ],
          linkId: 'Item6',
          text: 'checkbox4 ques',
          answer: [
            {
              valueCoding: {
                id: '743938',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743938,question:3,answer:3,',
                  },
                ],
                code: '3',
                display: '3',
              },
            },
          ],
        },
        {
          id: '743944',
          extension: [
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueString: '3',
            },
            {
              url: 'Questionnaire/Item/question-type',
              valueString: 'choice',
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueString: '2',
            },
          ],
          linkId: 'Item7',
          text: 'radiobutton2',
          answer: [
            {
              valueCoding: {
                id: '743947',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743947,question:2,answer:2,',
                  },
                ],
                code: '2',
                display: '2',
              },
            },
          ],
        },
      ],
    },
  ],
};
