import babel from '@rollup/plugin-babel';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import dynamicImportVars from '@rollup/plugin-dynamic-import-vars';
import image from '@rollup/plugin-image';
import postcss from 'rollup-plugin-postcss';

const jsExtensions = ['.js', '.jsx'];
const packageJson = require('./package.json');

export default [
  {
    input: 'src/lib/components/index.js',
    inlineDynamicImports: true,
    output: [
      {
        file: packageJson.main,
        format: 'cjs',
        sourcemap: true,
        name: 'core-lib',
      },
      {
        file: packageJson.module,
        format: 'esm',
        sourcemap: true,
      },
    ],
    external: [
      'react',
      'react-dom',
      'react-hook-use-state',
      'react-router-dom',
      '@emotion/react',
      '@emotion/styled',
      '@mui/icons-material',
      '@mui/material',
      '@mui/styles',
      'react-pdf',
      'react-phone-input-2',
    ],
    plugins: [
      resolve({
        extensions: jsExtensions,
      }),
      commonjs(),
      babel({
        presets: ['@babel/preset-react'],
        exclude: './node_modules/**',
        extensions: jsExtensions,
        babelHelpers: 'bundled',
      }),
      postcss(),
      dynamicImportVars({
        // options
      }),
      image(),
    ],
  },
];
