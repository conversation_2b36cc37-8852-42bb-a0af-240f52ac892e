import React from 'react';
import { TextField } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { format } from 'date-fns';

function DateTimeQuestion(props) {
  const { question, handleQuestionResponse } = props;
  const isReadOnly = handleQuestionResponse === undefined;

  const extractExistingAnswer = () => {
    let answer = '';
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      const [year, month, day] = question?.answer?.valueDate?.split('-').map(Number) || ['', '', ''];
      if (!year || !month || !day) return answer;

      answer = new Date(year, month - 1, day);
    }
    return answer;
  };

  const [value, setValue] = React.useState(extractExistingAnswer() ? extractExistingAnswer() : null);

  const handleChange = (newValue) => {
    if (!isReadOnly) {
      setValue(newValue);
      if (newValue instanceof Date && !isNaN(newValue)) {
        question.answer = {
          valueDate: format(newValue, 'yyyy-MM-dd'),
        };
        handleQuestionResponse(question);
      } else if (newValue === null) {
        question.answer = {
          valueDate: null,
        };
        handleQuestionResponse(question);
      }
    }
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DatePicker
          clearable
          value={value}
          format="yyyy-MM-dd"
          onChange={(newValue) => {
            handleChange(newValue);
          }}
          disableOpenPicker={isReadOnly}
          slots={{
            textField: TextField,
          }}
          slotProps={{
            textField: {
              size: 'small',
              InputProps: {
                placeholder: isReadOnly ? '' : 'YYYY-MM-DD',
              },
            },
          }}
        />
      </LocalizationProvider>
      <Explanation question={question} />
    </>
  );
}

export default DateTimeQuestion;
