import React, { useState } from 'react';
import { Accordion, AccordionDetails, AccordionSummary, Typography } from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

function PullDownCollection(props) {
  const { titleVariant, summaryVariant, title, summary, children, isExpanded } = props;
  const [expanded, setExpanded] = useState(isExpanded);

  return (
    <Accordion
      sx={{
        borderTopLeftRadius: '4px',
        borderTopRightRadius: '4px',
        borderBottomLeftRadius: '4px',
        borderBottomRightRadius: '4px',
      }}
      expanded={expanded === undefined ? false : expanded}
    >
      <AccordionSummary
        expandIcon={<ExpandMoreIcon />}
        aria-controls="panel1a-content"
        id="panel1a-header"
        onClick={() => setExpanded(!expanded)}
      >
        <Typography variant={titleVariant}>{title}</Typography>
      </AccordionSummary>

      <AccordionDetails>
        <Typography variant={summaryVariant}>{summary}</Typography>
        {children}
      </AccordionDetails>
    </Accordion>
  );
}

export { PullDownCollection };
