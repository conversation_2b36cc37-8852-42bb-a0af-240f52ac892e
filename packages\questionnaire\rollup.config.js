import babel from '@rollup/plugin-babel';
import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import image from '@rollup/plugin-image';
import packageJson from './package.json';
import postcss from 'rollup-plugin-postcss';

const jsExtensions = ['.js', '.jsx'];

export default [
  {
    input: 'src/lib/index.jsx',
    output: [
      {
        file: packageJson.main,
        format: 'cjs',
        sourcemap: true,
      },
      {
        file: packageJson.module,
        format: 'es',
        sourcemap: true,
      },
    ],
    external: [
      'react',
      'react-dom',
      '@emotion/react',
      '@emotion/styled',
      '@mui/icons-material',
      '@mui/material',
      'react-pdf',
      '@mui/x-data-grid',
      'date-fns-tz',
    ],
    plugins: [
      resolve({
        extensions: jsExtensions,
      }),
      commonjs(),
      babel({
        presets: ['@babel/preset-react'],
        exclude: './node_modules/**',
        extensions: jsExtensions,
      }),
      image(),
      postcss({
        extract: true,
        minimize: true,
      }),
    ],
  },
];
