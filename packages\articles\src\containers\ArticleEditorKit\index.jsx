import React, { useMemo, useState } from 'react';
import { ArticlesList } from '../ArticlesList';
import { ArticlesEditor } from '../ArticlesEditor';
import { pages } from '../CommonConstants';
import { Box } from '@mui/material';
import { SnackbarProvider } from 'notistack';
import { ErrorOutline, InfoOutlined, TaskAlt, WarningAmberOutlined } from '@mui/icons-material';
import { Loader } from '@/components';

const snackbarIconVariants = {
  success: <TaskAlt fontSize="small" />,
  error: <ErrorOutline fontSize="small" />,
  info: <InfoOutlined fontSize="small" />,
  warning: <WarningAmberOutlined fontSize="small" />,
};

export const ArticleEditorKit = (props) => {
  const {
    articleList = [],
    articleDetailList = [],
    onEditArticleCallback = () => {},
    onViewCallback = () => {},
    onSaveDraftCallback = () => {},
    onPublishCallback = () => {},
    onDeleteCallback = () => {},
    onDuplicateCallback = () => {},
    onImportCallback = () => {},
    onExportCallback = () => {},
  } = props;

  const [currentPage, setCurrentPage] = useState(pages.articleList);
  const [editedArticle, setEditedArticle] = useState(null);
  const [publishedRepository, setPublishRepository] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleNavigation = (page) => {
    setCurrentPage(page);
    if (page === pages.articleList) {
      setEditedArticle(null);
      setPublishRepository('');
    }
  };

  const handleEditArticle = async (articleId, publishStatus) => {
    setIsLoading(true);

    try {
      setPublishRepository(publishStatus);
      const response = await onEditArticleCallback(articleId, publishStatus);
      if (response.success && response.article) {
        setEditedArticle(response.article);
        setCurrentPage(pages.articleEditor);
      }
    } catch (error) {
      console.error('Error editing article:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewArticle = async (articleId, publishStatus) => {
    setIsLoading(true);

    try {
      setPublishRepository(publishStatus);
      const response = await onViewCallback(articleId, publishStatus);
      if (response.success && response.article) {
        setEditedArticle(response.article);
        setCurrentPage(pages.articleEditor);
      }
    } catch (error) {
      console.error('Error viewing article:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDuplicateArticle = (articleId, publishStatus) => {
    onDuplicateCallback(articleId, publishStatus);
  };

  const handleDeleteArticle = (articleId, publishStatus) => {
    onDeleteCallback(articleId, publishStatus);
  };

  const handleExportArticle = (articleId, publishStatus) => {
    onExportCallback(articleId, publishStatus);
  };

  const handlePublishArticle = (articleId, publishStatus) => {
    onPublishCallback(articleId, publishStatus);
  };

  const GetPage = useMemo(() => {
    if (currentPage === pages.articleList) {
      return (
        <ArticlesList
          handleNavigation={handleNavigation}
          articleList={articleList}
          articleDetailList={articleDetailList}
          onImportCallback={onImportCallback}
          handleEditArticle={handleEditArticle}
          handleViewArticle={handleViewArticle}
          handleDuplicateArticle={handleDuplicateArticle}
          handleDeleteArticle={handleDeleteArticle}
          handleExportArticle={handleExportArticle}
          handlePublishArticle={handlePublishArticle}
        />
      );
    } else if (currentPage === pages.articleEditor) {
      return (
        <ArticlesEditor
          publishedRepository={publishedRepository}
          handleNavigation={handleNavigation}
          onSaveDraftCallback={onSaveDraftCallback}
          onPublishCallback={onPublishCallback}
          onDuplicateCallback={onDuplicateCallback}
          onDeleteCallback={onDeleteCallback}
          onExportCallback={onExportCallback}
          existingArticleData={editedArticle}
          setExistingArticleData={setEditedArticle}
        />
      );
    }
  }, [currentPage, articleList, editedArticle, publishedRepository]);

  return (
    <SnackbarProvider iconVariant={snackbarIconVariants} maxSnack={3}>
      <Loader active={isLoading} />
      <Box>{GetPage}</Box>
    </SnackbarProvider>
  );
};
