import React from 'react';
import { <PERSON>rid, <PERSON>, Button } from '@mui/material';
import { COLOR_BLACK, COLORS, COLOR_WHITE } from '../../../../Common/constants';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';

function LargeButtonQuestion(props) {
  const { question, handleQuestionResponse } = props;

  const [isReadOnly, setIsReadOnly] = React.useState(() => handleQuestionResponse === undefined);

  let selectedAnswerId = question.answer.valueCoding === undefined ? '' : question.answer.valueCoding.id;
  const [response, setResponse] = React.useState(selectedAnswerId);

  const handleClick = (key, id) => {
    let newResponse = null;

    if (!isReadOnly) {
      if (id === response) {
        newResponse = null;
        setResponse(null);
      } else {
        newResponse = id;
        setResponse(id);
      }

      let selectedResponse = {};
      question.question.answerOption.forEach((element, index) => {
        if (element.valueCoding.id === newResponse) {
          selectedResponse = element;
        }
      });
      question.answer = selectedResponse;
      handleQuestionResponse(question);
    }
  };

  const getRandomBackgroundColor = (index) => {
    let currentIndex = index;
    if (currentIndex > 19) {
      currentIndex = currentIndex - 20;
    }
    for (let [key, value] of Object.entries(COLORS)) {
      if (Number(key) === currentIndex) {
        return `${value}`;
      }
    }
  };

  const drawLargeButton = (item, key) => {
    return (
      <Box
        sx={{
          background: `${getRandomBackgroundColor(key)}`,
          border: `4px solid ${response === item.valueCoding.id ? COLOR_BLACK : COLOR_WHITE}`,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100px',
          borderRadius: 1,
        }}
      >
        <Button
          variant="contained"
          sx={{
            width: '100%',
            height: 75,
            backgroundColor: 'transparent',
            '&:hover': { backgroundColor: 'transparent' },
          }}
          onClick={() => handleClick(key, item.valueCoding.id)}
        >
          {item.valueCoding.display}
        </Button>
      </Box>
    );
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <Grid container spacing={4} sx={{ mt: 0 }}>
        {question.question.answerOption.map((item, key) => (
          <React.Fragment key={`button_${key}`}>
            <Grid sx={{ display: 'block', displayPrint: 'none' }} item xs={12} md={6} key={`str_${key}`}>
              {drawLargeButton(item, key)}
            </Grid>

            <Grid sx={{ display: 'none', displayPrint: 'block' }} item xs={6} md={6} key={`str_print_${key}`}>
              {drawLargeButton(item, key)}
            </Grid>
          </React.Fragment>
        ))}
      </Grid>
      <Explanation question={question} />
    </>
  );
}

LargeButtonQuestion.defaultProps = {
  isEdit: true,
  responses: {},
  alignment: 'column',
  questionDescription: '',
};

export default LargeButtonQuestion;
