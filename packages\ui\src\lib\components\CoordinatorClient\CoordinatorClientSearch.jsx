import React from 'react';
import { Button, Grid, MenuItem, TextField, Typography } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

export const CoordinatorClientSearch = (props) => {
  const {
    title,
    idTypes,
    genderList,
    clientSearchHandler,
    clientSearchParams,
    handleParamsValueChange,
    searchResetHandler,
  } = props;

  const isSearchButtonDisabled = () => {
    const { firstName, lastName, email, primaryPhoneNumber, gender, dateOfBirth, healthCareIdType, healthCareIdValue } =
      clientSearchParams;

    // Check if all relevant fields are empty or null
    return (
      !firstName &&
      !lastName &&
      !email &&
      !primaryPhoneNumber &&
      !gender &&
      !dateOfBirth &&
      !healthCareIdType &&
      !healthCareIdValue
    );
  };
  const handleClientSearch = () => {
    clientSearchHandler();
  };

  return (
    <div>
      <Typography variant="h4" gutterBottom>
        {title}
      </Typography>
      <Grid container spacing={2}>
        <Grid item xs={12} sm={4}>
          <TextField
            label="First Name"
            fullWidth
            variant="outlined"
            name="firstName"
            value={clientSearchParams.firstName}
            onChange={handleParamsValueChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Last Name"
            fullWidth
            variant="outlined"
            name="lastName"
            value={clientSearchParams.lastName}
            onChange={handleParamsValueChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Type"
            select
            fullWidth
            variant="outlined"
            name="healthCareIdType"
            value={clientSearchParams.healthCareIdType}
            onChange={handleParamsValueChange}
          >
            {idTypes.map((type) => (
              <MenuItem key={type} value={type}>
                {type}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Value"
            fullWidth
            variant="outlined"
            name="healthCareIdValue"
            value={clientSearchParams.healthCareIdValue}
            onChange={handleParamsValueChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Email"
            fullWidth
            variant="outlined"
            name="email"
            value={clientSearchParams.email}
            onChange={handleParamsValueChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Phone Number"
            fullWidth
            variant="outlined"
            name="primaryPhoneNumber"
            value={clientSearchParams.primaryPhoneNumber}
            onChange={handleParamsValueChange}
          />
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Gender"
            select
            fullWidth
            variant="outlined"
            name="gender"
            value={clientSearchParams.gender}
            onChange={handleParamsValueChange}
          >
            {genderList.map((type) => (
              <MenuItem key={type} value={type}>
                {type}
              </MenuItem>
            ))}
          </TextField>
        </Grid>
        <Grid item xs={12} sm={4}>
          <LocalizationProvider required dateAdapter={AdapterDayjs}>
            <DatePicker
              maxDate={dayjs()}
              value={clientSearchParams.dateOfBirth ? dayjs(clientSearchParams.dateOfBirth) : null}
              onChange={(e) => {
                handleParamsValueChange({
                  target: {
                    name: 'dateOfBirth',
                    value: e === null ? null : dayjs(e).format('YYYY-MM-DD'),
                  },
                });
              }}
              format={'YYYY-MM-DD'}
              slots={{
                textField: TextField,
              }}
              slotProps={{
                textField: {
                  fullWidth: true,
                },
              }}
              label="Date of Birth"
              fullWidth
            />
          </LocalizationProvider>
        </Grid>
        <Grid container spacing={2} mt={2} justifyContent="flex-end">
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              disabled={isSearchButtonDisabled()}
              onClick={handleClientSearch}
            >
              Search
            </Button>
          </Grid>
          <Grid item>
            <Button variant="contained" color="primary" onClick={() => searchResetHandler()}>
              Reset
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </div>
  );
};

export default CoordinatorClientSearch;
