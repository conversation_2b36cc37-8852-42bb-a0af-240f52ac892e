import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Link,
  CircularProgress,
  Box,
  useMediaQuery,
  FormHelperText,
} from '@mui/material';
import cambianLogo from './cambian-logo.png';
import QRCode from 'react-qr-code';
import { useEffect, useState } from 'react';

function Mfa(props) {
  const {
    mfaCallback,
    mfaTimeInSeconds,
    navigateCallback,
    logoUrl = cambianLogo,
    mfaUrl,
    title = 'Secure Your Account',
    byline = mfaUrl
      ? 'Install an authenticator app on your device and scan the QR code to register Coordinator Multi Factor Authentication. Enter the verification code you have set up.'
      : 'Enter the verification code from your authenticator app. Please contact technical support if you have lost your authenticator app.',
    buttonText = 'Next',
    linkBelowButtonText = 'Back',
    orgName = 'Cambian',
  } = props;

  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const [serverResponseErrorMsg, setServerResponseErrorMsg] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const otpInputRef = React.useRef();
  const [otpValidSchema, setOtpValidSchema] = React.useState({});

  const [mfaExpiryTimeoutId, setMfaExpiryTimeoutId] = useState(null);

  useEffect(() => {
    if (mfaTimeInSeconds) {
      console.log('Clearing MFA timeout on page load');
      clearTimeout(mfaExpiryTimeoutId);
      console.log('Time to enter MFA has started.');
      setMfaExpiryTimeoutId(
        setTimeout(() => {
          console.log('Time to enter MFA has ran out.');
          navigateCallback();
        }, mfaTimeInSeconds * 1000),
      );
    }
  }, [mfaTimeInSeconds]);

  const handleSubmit = async () => {
    const otpValidationResult = { isMinLengthValid: otpInputRef.current.value.length > 0 };
    setOtpValidSchema(otpValidationResult);
    const isOtpValid = otpValidationResult.isMinLengthValid;

    if (!isOtpValid) {
      otpInputRef.current.focus();
    }
    if (isOtpValid) {
      setIsLoading(true);
      const { success, errorMsg } = await mfaCallback({ verificationCode: otpInputRef.current.value });
      if (!success) {
        console.error(errorMsg);
        if (errorMsg) {
          setServerResponseErrorMsg(errorMsg);
        }
        setIsLoading(false);
      } else if (mfaTimeInSeconds) {
        console.log('Clearing MFA timeout on successful submission');
        clearTimeout(mfaExpiryTimeoutId);
      }
    } else {
      setServerResponseErrorMsg(null);
    }
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  const isOtpInvalid = otpValidSchema.isMinLengthValid === false;
  return (
    <Stack direction="column" spacing={1.5} sx={{ width: { xs: '100%', sm: '40ch' } }}>
      <Box
        component="img"
        sx={{
          marginLeft: '-1%',
          width: { xs: '50%', sm: '55%' },
          minWidth: '100px',
          minHeight: '30px',
          height: { xs: '40%', sm: '45%' },
          marginBottom: -0.5,
        }}
        src={logoUrl}
        alt={`${orgName} full logo`}
      />
      <Typography sx={{ fontSize: { xs: 24, sm: 30 }, fontWeight: 500 }}>{title}</Typography>
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{byline}</Typography>
      {mfaUrl && (
        <div style={{ height: 'auto', margin: '0 auto', maxWidth: 150, width: '100%', marginTop: 10 }}>
          <QRCode
            size={256}
            style={{ height: 'auto', maxWidth: '100%', width: '100%' }}
            value={mfaUrl}
            viewBox={`0 0 256 256`}
          />
        </div>
      )}
      <TextField
        autoFocus
        size="small"
        id="verificationCode"
        label="Verification Code"
        inputProps={{ style: { fontSize: isXs ? 14 : 17 } }}
        InputLabelProps={{ style: { fontSize: isXs ? 13 : 16, marginTop: isXs ? 2 : undefined } }}
        InputProps={{ label: isXs ? 'Verification C_' : 'Verification Code' }}
        error={isOtpInvalid}
        inputRef={otpInputRef}
        onKeyDown={handleKeyDown}
      />
      {isOtpInvalid && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '0.2px !important' }}>
          Please enter your verification code
        </FormHelperText>
      )}
      <Button
        variant="contained"
        disabled={isLoading}
        onClick={handleSubmit}
        sx={{ fontSize: { xs: 17, sm: 19 }, fontWeight: 600, padding: '11.5px 12px', lineHeight: 1 }}
      >
        {isLoading ? <CircularProgress size={isXs ? 15 : 19} /> : buttonText}
      </Button>
      {setServerResponseErrorMsg && (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
          {serverResponseErrorMsg}
        </Typography>
      )}
      {!mfaUrl && (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} component={'div'}>
          Did you lose your authenticator app? <br />
          Contact your administrator
        </Typography>
      )}
      <Link
        component="button"
        onClick={() => {
          if (mfaTimeInSeconds) {
            console.log('Clearing MFA timeout on Back button pressed');
            clearTimeout(mfaExpiryTimeoutId);
          }
          navigateCallback();
        }}
        sx={{ textAlign: 'left', width: 'fit-content', fontSize: { xs: 11, sm: 12 } }}
      >
        {linkBelowButtonText}
      </Link>
    </Stack>
  );
}

export { Mfa };
