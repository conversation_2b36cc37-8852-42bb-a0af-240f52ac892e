import React from 'react';
import { Stack, Typography, Backdrop, CircularProgress } from '@mui/material';

export const Loader = ({ active = false, message = 'Loading...' }) => {
  return (
    <Backdrop
      sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1, backgroundColor: 'rgba(0,0,0,0.65)' }}
      open={active}
    >
      <Stack direction="column" alignItems="center" spacing={2} justifyContent="center">
        <CircularProgress color="inherit" />
      </Stack>
    </Backdrop>
  );
};
