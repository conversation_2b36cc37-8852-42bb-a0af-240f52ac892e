export const FHIR_QUESTIONNAIRE_SAVE_FOR_LATER = {
  resourceType: 'Questionnaire',
  id: '7611510a-c1b8-4e9b-969a-bfa1ad15c5c0',
  extension: [
    {
      url: 'Questionnaire/display-dial',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-description',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-score',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-score-category',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-title',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'Questionnaire/question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/trendable',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/result-page',
      valueString:
        '{"sections":[{"type":"Label","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[]},{"name":"DateTime","displayName":"","type":"Date","showInReport":true,"htmlText":"","fields":[{"format":"YYYY/MM/DD HH:mm:ss","name":"","displayName":"","sequence":1,"showInReport":true}],"variables":[]},{"type":"Questions & Answers","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[]}]}',
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '7611510a-c1b8-4e9b-969a-bfa1ad15c5c0',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 0,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'default',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'default-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'X',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  identifier: [
    {
      system: 'urn:uuid',
      value: '7611510a-c1b8-4e9b-969a-bfa1ad15c5c0',
    },
  ],
  name: 'save-for-later',
  title: 'save for later test',
  status: 'active',
  date: '2022-11-08T08:03:52-07:00',
  publisher: 'hyperadmin',
  description: 'cannot locate string',
  item: [
    {
      id: 'group-731500',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      linkId: '1077084569',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '731502',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '1077101923',
          text: 'How do you feel today',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '731506',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'good',
              },
            },
            {
              valueCoding: {
                id: '731508',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'not good',
              },
            },
          ],
        },
        {
          id: '731512',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '1079881611',
          text: 'do you have thoughts of harming yourself?',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '731516',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'yes',
              },
            },
            {
              valueCoding: {
                id: '731518',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'no',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-731523',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 2,
        },
      ],
      linkId: '1080193299',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '731525',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          linkId: '1080204813',
          text: 'do you need help in preparing meals?',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '731529',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'yes',
              },
            },
            {
              valueCoding: {
                id: '731531',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'no',
              },
            },
          ],
        },
        {
          id: '731535',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          linkId: '1080378680',
          text: 'what kind of food do you like?',
          type: 'text',
          required: false,
        },
      ],
    },
    {
      id: 'group-731583',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      linkId: '107708456323',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '731534',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '1077101923',
          text: 'how do you rate your nature',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '731506',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'good',
              },
            },
            {
              valueCoding: {
                id: '731508',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'not good',
              },
            },
          ],
        },
        {
          id: '7315183',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '1079881634',
          text: 'do you ever had thoughts of harming someone?',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '731575',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'yes',
              },
            },
            {
              valueCoding: {
                id: '731562',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'no',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-731543',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      linkId: '1077084439',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '731352',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '10771541923',
          text: 'How were you feeling over a course of a year',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '731836',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'good',
              },
            },
            {
              valueCoding: {
                id: '731838',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'not good',
              },
            },
          ],
        },
        {
          id: '731532',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '1079541611',
          text: 'do you had thoughts of harming yourself last year?',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '731376',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'yes',
              },
            },
            {
              valueCoding: {
                id: '731378',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'no',
              },
            },
          ],
        },
      ],
    },
  ],
};
