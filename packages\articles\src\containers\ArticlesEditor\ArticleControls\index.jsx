import React, { useState, useMemo, useEffect } from 'react';
import { Stack, Button, Box, FormControlLabel, Checkbox, Typography } from '@mui/material';
import { MoreVert, FileCopy, Delete, Save } from '@mui/icons-material';
import { MenuList } from '../../../components/MenuList';
import { CustomModal } from '../../../components/CustomModal';
import { Loader } from '@/components';
import { strings } from '../../../utility/strings';
import { pages, publishedStatuses } from '../../CommonConstants';
import { emptyArticleData } from '../../../utility/data';
import useNotification from '../../../hooks/useNotification';
import { v4 as uuidV4 } from 'uuid';
import { validateForm } from '../../../utility/validation';

export const ArticleControls = (props) => {
  const {
    handleNavigation,
    onSaveDraftCallback,
    onPublishCallback,
    onDuplicateCallback,
    onDeleteCallback,
    onExportCallback,
    existingArticleData,
    setExistingArticleData,
    title,
    name,
    description,
    body,
    thumbnail,
    publishedRepository = 'no',
    setFormErrors,
  } = props;

  const openSnackbar = useNotification();

  const [anchorEl, setAnchorEl] = useState(null);
  const [openPublishModal, setOpenPublishModal] = useState(false);
  const [openDeleteConfirmation, setOpenDeleteConfirmation] = useState(false);
  const [publishStatus, setPublishStatus] = useState({ isPrivate: false, isPublic: false });
  const [isSaved, setIsSaved] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const isArticleChanged = useMemo(() => {
    if (!existingArticleData) {
      return title || name || description || body || thumbnail;
    }
    return (
      existingArticleData.title !== title ||
      existingArticleData.name !== name ||
      existingArticleData.description !== description ||
      existingArticleData.body !== body ||
      existingArticleData.thumbnail !== thumbnail
    );
  }, [existingArticleData, title, name, description, body, thumbnail]);

  useEffect(() => {
    if (isArticleChanged) {
      setIsSaved(false);
    }
  }, [isArticleChanged]);

  const generateArticleData = async () => {
    let articleData = existingArticleData ? structuredClone(existingArticleData) : emptyArticleData;

    articleData = {
      ...articleData,
      artifactId: existingArticleData?.artifactId || uuidV4(),
      title,
      name,
      description,
      body,
      thumbnail: thumbnail || null,
      modifiedDate: new Date().toISOString(),
    };

    return articleData;
  };

  const handleSaveDraft = async () => {
    const { isValid, errors } = validateForm({
      name,
      title,
      body,
    });

    setFormErrors(errors);

    if (!isValid) {
      return { success: false };
    }

    setIsLoading(true);

    try {
      let draftArticle = await generateArticleData();
      if (draftArticle) {
        // Before saving, if we have a file, keep the binary data for upload
        let thumbnailFile = null;
        if (draftArticle.thumbnail && draftArticle.thumbnail._file) {
          thumbnailFile = draftArticle.thumbnail._file;

          // Create a proper data structure for the API
          draftArticle.thumbnail = {
            contentType: draftArticle.thumbnail.contentType,
            fileName: draftArticle.thumbnail.fileName,
            data: thumbnailFile,
          };
        }

        const { success, message, article } = await onSaveDraftCallback(draftArticle);

        if (success) {
          openSnackbar({ variant: 'success', msg: 'Article saved successfully' });
          setExistingArticleData(article);
          setIsSaved(true);
          return { success, article };
        } else {
          openSnackbar({ variant: 'error', msg: message || 'Error saving article' });
          return { success: false };
        }
      }
      return { success: false };
    } catch (error) {
      console.error('Error saving draft:', error);
      openSnackbar({ variant: 'error', msg: 'Error saving article' });
      return { success: false };
    } finally {
      setIsLoading(false);
    }
  };

  const handlePublishConfirm = async () => {
    let selectedPublishStatus = '';
    if (publishStatus.isPrivate && !publishStatus.isPublic) {
      selectedPublishStatus = publishedStatuses.private;
    } else if (!publishStatus.isPrivate && publishStatus.isPublic) {
      selectedPublishStatus = publishedStatuses.public;
    } else if (publishStatus.isPrivate && publishStatus.isPublic) {
      selectedPublishStatus = publishedStatuses.both;
    } else if (!publishStatus.isPrivate && !publishStatus.isPublic) {
      selectedPublishStatus = publishedStatuses.no;
    }

    setIsLoading(true);
    setOpenPublishModal(false);

    try {
      let articleData = await generateArticleData();
      if (articleData) {
        articleData.publishStatus = 'PUBLISHED';
        const { success, message } = await onPublishCallback(articleData.artifactId, selectedPublishStatus);
        if (success) {
          handleNavigation(pages.articleList);
          openSnackbar({ variant: 'success', msg: 'Article published successfully' });
        } else {
          openSnackbar({ variant: 'error', msg: message || 'Error publishing article' });
        }
      }
    } catch (error) {
      console.error('Error publishing article:', error);
      openSnackbar({ variant: 'error', msg: 'Error publishing article' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setOpenDeleteConfirmation(false);
    setIsLoading(true);
    try {
      const { success, message } = await onDeleteCallback(existingArticleData?.artifactId, publishedRepository);
      if (success) {
        handleNavigation(pages.articleList);
        openSnackbar({ variant: 'success', msg: 'Article deleted successfully' });
      } else {
        openSnackbar({ variant: 'error', msg: message || 'Error deleting article' });
      }
    } catch (error) {
      console.error('Error deleting article:', error);
      openSnackbar({ variant: 'error', msg: 'Error deleting article' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDuplicate = async () => {
    setIsLoading(true);

    try {
      if (existingArticleData?.artifactId) {
        const result = await onDuplicateCallback(existingArticleData.artifactId, publishedRepository);
        if (result?.success) {
          handleNavigation(pages.articleList);
          openSnackbar({ variant: 'success', msg: 'Article duplicated successfully' });
        } else {
          openSnackbar({ variant: 'error', msg: result?.message || 'Error duplicating article' });
        }
      } else {
        const { success, article } = await handleSaveDraft();
        if (success) {
          const { success: duplicateSuccess, message } = await onDuplicateCallback(
            article.artifactId,
            publishedRepository,
          );
          if (duplicateSuccess) {
            handleNavigation(pages.articleList);
            openSnackbar({ variant: 'success', msg: 'Article duplicated successfully' });
          } else {
            openSnackbar({ variant: 'error', msg: message || 'Error duplicating article' });
          }
        }
      }
    } catch (error) {
      console.error('Error duplicating article:', error);
      openSnackbar({ variant: 'error', msg: 'Error duplicating article' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleExport = async () => {
    setIsLoading(true);

    try {
      if (existingArticleData?.artifactId) {
        await onExportCallback(existingArticleData.artifactId, publishedRepository);
      } else {
        const { success, article } = await handleSaveDraft();
        if (success) {
          await onExportCallback(article.artifactId, publishedRepository);
        }
      }
    } catch (error) {
      console.error('Error exporting article:', error);
      openSnackbar({ variant: 'error', msg: 'Error exporting article' });
    } finally {
      setIsLoading(false);
    }
  };

  const menuItems = [
    {
      id: 0,
      label: strings.duplicate,
      icon: <FileCopy sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: handleDuplicate,
      show: true,
    },
    {
      id: 1,
      label: strings.export,
      icon: <Save sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: handleExport,
      show: true,
    },
    {
      id: 2,
      label: strings.delete,
      icon: <Delete sx={{ mr: 1, fontSize: '20px' }} />,
      handleClick: () => {
        if (!existingArticleData?.artifactId) {
          openSnackbar({ variant: 'error', msg: 'Only saved articles can be deleted' });
        } else {
          setOpenDeleteConfirmation(true);
        }
      },
      show: true,
    },
  ];

  const publishModalContent = (
    <Box sx={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}>
      <FormControlLabel
        control={
          <Checkbox
            checked={publishStatus.isPrivate}
            onChange={(e) => setPublishStatus({ ...publishStatus, isPrivate: e.target.checked })}
          />
        }
        label={strings.private}
        sx={{ mr: 4 }}
      />
      <FormControlLabel
        control={
          <Checkbox
            checked={publishStatus.isPublic}
            onChange={(e) => setPublishStatus({ ...publishStatus, isPublic: e.target.checked })}
          />
        }
        label={strings.public}
      />
    </Box>
  );

  return (
    <>
      <Loader active={isLoading} />
      <Box>
        <MenuList menuItems={menuItems} anchorEl={anchorEl} setAnchorEl={setAnchorEl} />
        <Stack direction="row" spacing={2} justifyContent="space-between" alignItems="center">
          <Stack>{title || existingArticleData?.title || strings.untitledArticle}</Stack>

          <Stack direction="row" spacing={2} alignItems="center">
            <>
              <Button
                variant="contained"
                onClick={handleSaveDraft}
                disabled={!isArticleChanged || isSaved || isLoading}
              >
                {strings.save}
              </Button>
              <Button
                variant="outlined"
                onClick={() => {
                  setOpenPublishModal(true);
                  if (publishedRepository === 'private') {
                    setPublishStatus({ isPrivate: true, isPublic: false });
                  } else if (publishedRepository === 'public') {
                    setPublishStatus({ isPrivate: false, isPublic: true });
                  } else if (publishedRepository === 'both') {
                    setPublishStatus({ isPrivate: true, isPublic: true });
                  } else {
                    setPublishStatus({ isPrivate: false, isPublic: false });
                  }
                }}
                disabled={isLoading}
              >
                {strings.publish}
              </Button>
            </>
            <MoreVert onClick={(e) => setAnchorEl(e.currentTarget)} sx={{ cursor: 'pointer', fontSize: '20px' }} />
          </Stack>
        </Stack>

        <CustomModal
          open={openPublishModal}
          onClose={() => setOpenPublishModal(false)}
          onConfirm={handlePublishConfirm}
          title={strings.publishConfirmation}
          subTitle={strings.selectRepositoryYouWantToPublishIn}
          content={publishModalContent}
          saveButtonText={strings.publish}
          closeButtonText={strings.cancel}
        />

        <CustomModal
          open={openDeleteConfirmation}
          onClose={() => setOpenDeleteConfirmation(false)}
          onConfirm={handleDelete}
          title={strings.deleteArticle}
          subTitle={strings.thisActionCanNotBeUndone}
          saveButtonText={strings.delete}
          closeButtonText={strings.cancel}
        />
      </Box>
    </>
  );
};
