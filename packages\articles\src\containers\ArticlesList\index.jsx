import React, { useRef } from 'react';
import { Box, Grid, Stack, Button, Typography } from '@mui/material';
import { pages } from '../CommonConstants';
import { strings } from '../../utility/strings';
import { ArticleTable } from './components/ArticleTable';
import { HeaderStyle, PanelBorder } from '@/components';

export const ArticlesList = (props) => {
  const { handleNavigation, onImportCallback, articleList } = props;
  const importInputRef = useRef(null);

  const makeNewArticle = () => {
    handleNavigation(pages.articleEditor);
  };

  const handleImport = (event) => {
    const articleFile = event?.target?.files && event?.target?.files[0];

    const fileReader = new FileReader();
    fileReader.readAsText(articleFile, 'UTF-8');
    fileReader.onload = async (readerEvent) => {
      let articleDraft = JSON.parse(readerEvent.target.result);

      const response = await onImportCallback(articleDraft);

      if (response?.success) {
        console.log('Article imported successfully!');
      }
      importInputRef.current.value = null;
    };
  };

  return (
    <>
     <HeaderStyle>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item xs="auto">
            {strings.articles}
          </Grid>
          <Grid item xs="auto">
            <Stack direction="row" spacing={2}>
              <Button component="label" variant="outlined">
                <input ref={importInputRef} type="file" accept="application/json" onChange={handleImport} hidden />
                {strings.import}
              </Button>
              <Button variant="contained" onClick={makeNewArticle}>
                {strings.new}
              </Button>
            </Stack>
          </Grid>
        </Grid>
      </HeaderStyle>
      <PanelBorder>
        <Box sx={{ p: 2 }}>
          <ArticleTable {...props} />
        </Box>
      </PanelBorder>
    </>
  );
};
