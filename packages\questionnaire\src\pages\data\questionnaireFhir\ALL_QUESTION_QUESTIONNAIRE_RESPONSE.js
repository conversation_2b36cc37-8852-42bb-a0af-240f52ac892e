export const ALL_QUESTION_QUESTIONNAIRE_RESPONSE = {
  resourceType: 'QuestionnaireResponse',
  identifier: [],
  questionnaire: 'Questionnaire/d89d6b08-fd37-43d6-8e23-eb37d4de974c',
  status: 'completed',
  authored: '2024-09-19T10:09:15.591Z',
  extension: [
    {
      url: 'questionnaire-response-type',
      valueCode: 'instrument-response',
    },
    {
      url: 'questionnaire-name',
      valueString: 'all questions',
    },
    {
      url: 'questionnaire-title',
      valueString: 'all questions',
    },
    {
      url: 'calculated-scores',
      extension: [],
    },
  ],
  item: [
    {
      id: 'group-ppPs5Gic3Tp8hF2j6J8wDB',
      linkId: 'Group1',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
        {
          url: 'Item/question-type',
          valueString: 'group',
        },
      ],
      item: [
        {
          id: 's3eJFn2LePkYGeMargar17',
          linkId: 'Item2',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'decimal',
            },
          ],
          text: 'Number question',
          answer: [
            {
              valueDecimal: '12',
            },
          ],
        },
        {
          id: '3g4GoLTYLoNdgJM7N1wGxf',
          linkId: 'Item3',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/integer-only',
              valueBoolean: true,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'integer',
            },
          ],
          text: 'Integer',
          answer: [
            {
              valueInteger: '113',
            },
          ],
        },
        {
          id: 'btzQUPaicQc6A4Vpiyk9LL',
          linkId: 'Item4',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Item/min-length',
              valueInteger: 0,
            },
            {
              url: 'Item/max-length',
              valueInteger: 1024,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'text',
            },
          ],
          text: 'paragraph',
          answer: [
            {
              valueString: 'faa32r',
            },
          ],
        },
        {
          id: 'tE4PTBT6XNzvpGkSYkA6Zb',
          linkId: 'Item5',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Item/min-length',
              valueInteger: 0,
            },
            {
              url: 'Item/max-length',
              valueInteger: 255,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 5,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'text',
            },
          ],
          text: 'text',
          answer: [
            {
              valueString: '2000',
            },
          ],
        },
        {
          id: 'sTeY5pZWiSjqiEGd4GbHYf',
          linkId: 'Item6',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 6,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'Checkboxes',
          answer: [
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: '2',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
          ],
        },
        {
          id: 'a2it21ad431vXsyXeVL2iN',
          linkId: 'Item7',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5543,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 7,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'radio',
          answer: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: 'r1',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
          ],
        },
        {
          id: '1DyiTZpt5B4cbXQWtoKgff',
          linkId: 'Item8',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 8,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'radio1',
          answer: [
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: 'r2',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
          ],
        },
        {
          id: 'hLTugEdSTSKZgsenEWaDNW',
          linkId: 'Item9',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 9,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'date',
            },
          ],
          text: 'date picker',
          answer: [
            {
              valueDate: '2024-08-30',
            },
          ],
        },
        {
          id: 'jiG2gAkdbdfPsesxtGkVQt',
          linkId: 'Item10',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/display-type',
              valueString: 'choice-bar',
            },
            {
              url: 'Item/bar-start-label',
              valueString: 'min',
            },
            {
              url: 'Item/bar-end-label',
              valueString: 'max',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 10,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'linear',
          answer: [
            {
              valueCoding: {
                id: 'g16Z82kQLL16fvN3y6gn8S',
                code: 4,
                display: 4,
              },
            },
          ],
        },
        {
          id: 'h4xzFtZfFUHnsHMHVe6Grc',
          linkId: 'Item11',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5013,
            },
            {
              url: 'Item/display-type',
              valueString: 'numeric-slider',
            },
            {
              url: 'Item/slider-min-value',
              valueDecimal: 0,
            },
            {
              url: 'Item/slider-max-value',
              valueDecimal: 100,
            },
            {
              url: 'Item/slider-min-label',
              valueString: 'min',
            },
            {
              url: 'Item/slider-max-label',
              valueString: 'max',
            },
            {
              url: 'Item/slider-min-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/slider-max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 11,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'integer',
            },
          ],
          text: 'numeric',
          answer: [
            {
              valueInteger: 58,
            },
          ],
        },
        {
          id: 'qMD4m3nnoAGSCduwtAJ8uo',
          linkId: 'Item12',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Item/display-type',
              valueString: 'choice-image',
            },
            {
              url: 'Item/image-path',
              valueString: '/api/media/body_diagram.jpg',
            },
            {
              url: 'Item/image-width',
              valueInteger: 795,
            },
            {
              url: 'Item/image-height',
              valueInteger: 698,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 12,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'choice',
            },
          ],
          text: 'body diagram',
          answer: [
            {
              valueCoding: {
                id: '574409',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 5,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '238,128,225,132,228,141,232,150,240,156,257,171,276,184,283,184,290,183,295,181,298,179,285,149,280,143,279,142,277,140,269,138,262,137,251,134|562,126,565,129,570,130,572,132,580,132,584,132,582,136,581,139,579,143,577,147,573,151,570,154,566,158,561,161,557,166,551,170,546,173,541,175,536,179,533,182,530,185,523,185,517,185,512,183,508,182,504,180,502,179,504,171,507,163,509,156,511,153,512,149,515,146,519,141,525,138,531,135,537,133,542,132',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '5',
                display: 'Shoulder Left',
              },
            },
          ],
        },
        {
          id: 'complex-rPXw2VzZXFQQc9FnbnRU3x',
          linkId: 'Item13',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 13,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'group',
            },
          ],
          text: 'data grid',
          item: [
            {
              item: [
                {
                  id: 0,
                  linkId: 'nVk2LXhDeZL77k4nS52zq1',
                  text: 'c1',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'text',
                    },
                  ],
                  answer: [
                    {
                      valueString: 'tetst',
                    },
                  ],
                },
                {
                  id: 1,
                  linkId: 'asoXWUPCvAbSE2UipEiFUo',
                  text: 'c2',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'decimal',
                    },
                  ],
                  answer: [
                    {
                      valueInteger: '12',
                    },
                  ],
                },
                {
                  id: 2,
                  linkId: 'tmDpZZBGY3iQDjPDH48iHr',
                  text: 'date',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'dateTime',
                    },
                  ],
                  answer: [
                    {
                      valueDate: '2024-08-31',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  text: {
    status: 'generated',
    div: 'root <html xmlns="http://www.w3.org/1999/xhtml"><head><style>[data-visible=\'false\'] {  display: none;}</style></head><body><h1>all questions</h1><p>Date: 2024-08-02 01:34:30</p><p>Here is a list of items and responses:</p>{QuestionnaireResponse.itemsAndResponses}<p style="color:gray; font-size:11px;">Questi</p></body></html>',
  },
};
