import React, { useEffect } from 'react';
import { Button, DialogContentText, Grid, Paper, Typography, Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Page } from './page/Page';
import { createQuestionnaireResponse } from './createQuestionnaireResponse';
import QuestionnaireUtility from './utility/questionnaireUtility';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import DialogActions from '@mui/material/DialogActions';
import { buildOutputSystemCodeMap } from './utility/questionnaireUtility';
import { QuestionnaireProgress } from './QuestionnaireProgress';
import { QuestionnaireDescription } from './QuestionnaireDescription';
import { hashCode } from './page/pageFunctions';

const questionnaireStartTime = new Date();

function Questionnaire(props) {
  const {
    fhirQuestionnaire, // FHIR Questionnaire payload
    fhirResponse, // Cambian "API" Questionnaire Response payload
    catPageCallback, // Callback to CAT Questionnaire.  If not defined, is not a CAT questionnaire
    questionnaireCallback, // Callback to complete or "save for later" the questionnaire
    pageTransitionCallback, // If enabled, called whenever the page is changed
    pageErrorTransitionCallback, // If page transitions are enabled, called whenever the page save has failed
    enableCancelConfirmation, // If enabled, a confirmation dialog will be shown
    isSaveForLaterActive, // If 'save for later' functionality is available while answering this questionnaire
    isStatusEnabled, // If the status / progress through questionnaire is shown
    isProgressPercentageEnabled, // If progress percentage is shown
    codingSystemCallback, // Callback to retrieve data from a coding system such as LOINC
    handleBackNavigation, //Boolean if True back button will be shown in first page
    nextButtonLabelOverride, // Next button label, default to 'Next',
    previousButtonLabelOverride, // Previous button label, default to 'Previous'
    submitButtonLabelOverride, // Submit button label, default to 'Done'
    handleSaveForLaterRef,
  } = props;

  let questionnaireId = fhirQuestionnaire.id;
  let questionnaireResponseId;

  let questionnaireResponse = {};
  if (fhirResponse !== undefined && fhirResponse !== null) {
    questionnaireResponse = fhirResponse;
    if (typeof fhirResponse === 'object') {
      questionnaireResponseId = fhirResponse.id;
    } else {
      let responseObj = JSON.parse(fhirResponse);
      questionnaireResponseId = responseObj.id;
    }
  }

  console.log('questionnaireId: ' + questionnaireId);
  console.log('questionnaireResponseId: ' + questionnaireResponseId);

  let saveForLaterIsActive = false;
  if (isSaveForLaterActive !== undefined) {
    saveForLaterIsActive = isSaveForLaterActive;
  }

  let isCancelConfirmationActive = false;
  if (enableCancelConfirmation !== undefined) {
    isCancelConfirmationActive = enableCancelConfirmation;
  }

  let enableStatus = true;
  if (isStatusEnabled !== undefined) {
    enableStatus = isStatusEnabled;
  }
  let enableProgressPercentage = false;
  if (isProgressPercentageEnabled !== undefined) {
    enableProgressPercentage = isProgressPercentageEnabled;
  }

  let nextButtonLabel = 'Next';
  if (nextButtonLabelOverride !== undefined) {
    nextButtonLabel = nextButtonLabelOverride;
  }

  let previousButtonLabel = 'Previous';
  if (previousButtonLabelOverride !== undefined) {
    previousButtonLabel = previousButtonLabelOverride;
  }

  let submitButtonLabel = 'Done';
  if (submitButtonLabelOverride !== undefined) {
    submitButtonLabel = submitButtonLabelOverride;
  }

  const questionnairePageList = (questionnairePage, questionList, questionMap) => {
    let pageList = [];
    if (questionnairePage !== undefined && questionList !== undefined && questionMap !== undefined) {
      pageList = QuestionnaireUtility.buildQuestionnairePageList(
        questionnairePage,
        questionList,
        questionMap,
        fhirQuestionnaire,
      );
      if (pageList.length == 0 && catPageCallback !== undefined) {
        const nextQuestion = catPageCallback(questionnairePage, questionList, questionMap);
        console.log(nextQuestion);
        if (nextQuestion !== null) {
          questionList.push(nextQuestion);
          questionMap.set(nextQuestion.question.id, nextQuestion);
          pageList.push(nextQuestion);
        }
      }
    }

    return pageList;
  };

  const questionCount = (questionList) => {
    let count = 0;
    let singleLevelOfDetail = true; // Hacked - more logic is needed for nested question groups
    questionList.forEach((item) => {
      if (singleLevelOfDetail) {
        count++;
      } else {
        count = count + questionCount(item.questions); // TBD - need to find correct substructure of nested questions
      }
    });
    return count;
  };
  //
  // Set up and initialize state
  //
  const [isErrorDialogOpen, setIsErrorDialogOpen] = React.useState(false);
  const [isPageFailed, setIsPageFailed] = React.useState(false);

  const [isCatQuestionnaire, setCatQuestionnaire] = React.useState(catPageCallback !== undefined);

  const [codingSystemMap] = React.useState(() =>
    QuestionnaireUtility.buildCodingSystemMap(fhirQuestionnaire.item, codingSystemCallback),
  );

  const [questionList] = React.useState(() =>
    QuestionnaireUtility.buildQuestionList(fhirQuestionnaire.item, questionnaireResponse, codingSystemMap, false),
  );

  const [maxQuestionnairePages] = React.useState(() =>
    QuestionnaireUtility.calculateQuestionnairePageLength(questionList),
  );

  const [questionMap] = React.useState(() => QuestionnaireUtility.buildQuestionMap(questionList));

  const [questionnairePage, setQuestionnairePage] = React.useState(
    QuestionnaireUtility.getQuestionnaireStartPageNumber(
      questionList,
      isSaveForLaterActive,
      fhirResponse,
      maxQuestionnairePages,
      fhirQuestionnaire,
      questionMap,
    ),
  );

  const [totalQuestions] = React.useState(questionMap.size);

  const [progress, setProgress] = React.useState(QuestionnaireUtility.calculateProgress(questionMap));

  const [questionnairePageQuestionList, setQuestionnairePageQuestionList] = React.useState(() =>
    questionnairePageList(questionnairePage, questionList, questionMap),
  );

  const [isNextPageVisible, setIsNextPageVisible] = React.useState(true);

  //
  // The following three state variable supports the page transition 'save for later' functionality
  //
  const [pageTransitionQuestionnaireResponseData, setPageTransitionQuestionnaireResponseData] = React.useState(null);
  const [pageTransitionUpdateInProgress, setPageTransitionUpdateInProgress] = React.useState(false);
  const [latestHashValue, setLatestHashValue] = React.useState(-1);

  const questionnaireTitleRef = React.useRef(null);

  useEffect(() => {
    let responseData = pageTransitionQuestionnaireResponseData;
    let pendingResponseHashCode = responseData === undefined || responseData === null ? 0 : responseData.responseHash;
    if (
      responseData !== undefined &&
      responseData !== null &&
      responseData.response !== null &&
      pageTransitionUpdateInProgress === false
    ) {
      console.log(latestHashValue + ' vs ' + pendingResponseHashCode);
      if (pendingResponseHashCode !== latestHashValue) {
        setPageTransitionUpdateInProgress(true);
        pageTransitionCallback(
          questionnaireId,
          questionnaireResponseId,
          responseData.responseJson,
          questionMap,
          function (hashValue) {
            console.log('+++ saveQuestionnaireResponseSuccessCallback ' + hashValue);
            setLatestHashValue(hashValue);
            setPageTransitionUpdateInProgress(false);
            setPageTransitionQuestionnaireResponseData(null);
            console.log('--- saveQuestionnaireResponseSuccessCallback ');
          },
          function () {
            console.log('+++ saveQuestionnaireResponseFailureCallback ');
            pageErrorTransitionCallback();
            setPageTransitionUpdateInProgress(false);
            console.log('--- saveQuestionnaireResponseFailureCallback ');
          },
        );
      }
    }
  }, [pageTransitionQuestionnaireResponseData, pageTransitionUpdateInProgress]);

  const questionnairePageAnsweredCallback = (
    page,
    map,
    isFinished,
    isCancelled,
    isSavedForLater,
    setNoVisibleComponent,
    isPageChanged,
  ) => {
    let callbackResults;
    if (isCancelled) {
      console.log('CANCELLED');
      callbackResults = questionnaireCallback(fhirQuestionnaire, null, null, false, true, false);
    } else {
      //
      // Build Question Map from the passed in map
      //
      for (let [key, value] of map) {
        questionMap.set(key, value);
      }
      console.log(questionMap);

      if (!isFinished && !isSavedForLater) {
        console.log('NOT FINISHED');
        //check pageList if no visible component found move to next page if next page is last page treat
        // questionnaire as complete
        let [nextPageList] = getQuestionnairePageList(page + 1, questionList, questionMap, questionnairePage);
        setIsNextPageVisible(nextPageList ? true : false);

        let [pageList, pageNumber] = getQuestionnairePageList(page, questionList, questionMap, questionnairePage);
        if (pageList) {
          setQuestionnairePageQuestionList(pageList);
          setQuestionnairePage(pageNumber);
          setProgress(QuestionnaireUtility.calculateProgress(questionMap));

          if (pageTransitionCallback !== undefined) {
            let fhirQuestionnaireResponse = createQuestionnaireResponse(
              fhirQuestionnaire,
              questionnaireResponse,
              questionMap,
              'in-progress',
              questionnaireStartTime,
            );
            if (isPageChanged) {
              console.log('Page Changed');
              let responseString = JSON.stringify(fhirQuestionnaireResponse);
              setPageTransitionQuestionnaireResponseData({
                response: responseString,
                responseHash: hashCode(responseString),
                responseJson: fhirQuestionnaireResponse,
              });
            }
          }
        } else {
          console.log('COMPLETED');
          let fhirQuestionnaireResponse = createQuestionnaireResponse(
            fhirQuestionnaire,
            questionnaireResponse,
            questionMap,
            'completed',
            questionnaireStartTime,
          );
          setProgress(100);
          callbackResults = questionnaireCallback(
            fhirQuestionnaire,
            fhirQuestionnaireResponse,
            questionMap,
            true,
            false,
          );
          setNoVisibleComponent(true);
        }
      } else {
        let outputSystemCodeMap = buildOutputSystemCodeMap(questionMap);
        console.log(outputSystemCodeMap);
        if (isSavedForLater) {
          console.log('SAVED FOR LATER');
          let fhirQuestionnaireResponse = createQuestionnaireResponse(
            fhirQuestionnaire,
            questionnaireResponse,
            questionMap,
            'in-progress',
            questionnaireStartTime,
          );
          callbackResults = questionnaireCallback(
            fhirQuestionnaire,
            fhirQuestionnaireResponse,
            questionMap,
            false,
            false,
            true,
            outputSystemCodeMap,
          );
        } else {
          console.log('COMPLETED');
          let fhirQuestionnaireResponse = createQuestionnaireResponse(
            fhirQuestionnaire,
            questionnaireResponse,
            questionMap,
            'completed',
            questionnaireStartTime,
          );
          setProgress(100);
          callbackResults = questionnaireCallback(
            fhirQuestionnaire,
            fhirQuestionnaireResponse,
            questionMap,
            true,
            false,
            false,
            outputSystemCodeMap,
          );
        }
      }

      return callbackResults;
    }
  };

  const getQuestionnairePageList = (page, questionList, questionMap, currentPage) => {
    if (page <= 0 && page > maxQuestionnairePages) {
      return [null, page];
    }
    let pageList = questionnairePageList(page, questionList, questionMap);
    //check pageList if no visible component found move to next page
    if (!QuestionnaireUtility.hasVisibleComponent(pageList)) {
      if (page >= maxQuestionnairePages && !isCatQuestionnaire) {
        pageList = null;
      } else if (maxQuestionnairePages === 0 && isCatQuestionnaire) {
        setQuestionnairePageQuestionList(questionnairePageList(page, questionList, questionMap));
        pageList = null;
      } else {
        [pageList, page] = getQuestionnairePageList(
          page < currentPage ? page - 1 : page + 1,
          questionList,
          questionMap,
          currentPage,
        );
      }
    }
    return [pageList, page];
  };

  const catQuestionnaireCompleteCallback = () => {
    setProgress(100);
    questionnaireCallback(fhirQuestionnaire, null, questionMap, true, false, false);
  };

  const handleProgress = (map) => {
    for (let [key, value] of map) {
      questionMap.set(key, value);
    }
    setProgress(QuestionnaireUtility.calculateProgress(questionMap));
  };

  if (
    (fhirQuestionnaire === undefined ||
      fhirQuestionnaire === null ||
      fhirQuestionnaire == {} ||
      fhirQuestionnaire.resourceType === undefined) &&
    !isErrorDialogOpen &&
    !isPageFailed
  ) {
    setIsErrorDialogOpen(true);
  }

  const questionnaireRenderedContent = () => {
    const isMalformedQuestionnaire =
      fhirQuestionnaire === undefined ||
      fhirQuestionnaire === null ||
      fhirQuestionnaire == {} ||
      fhirQuestionnaire.resourceType === undefined;
    if (isMalformedQuestionnaire) {
      console.log('FHIR Questionnaire Content: ');
      console.log(fhirQuestionnaire);
      return <div>Failure to load</div>;
    }

    return (
      <Page
        questionList={questionnairePageQuestionList}
        questionCount={totalQuestions}
        handleProgress={handleProgress}
        pageNumber={questionnairePage}
        maxPages={maxQuestionnairePages}
        isNextPageVisible={isNextPageVisible}
        isSaveForLaterActive={saveForLaterIsActive}
        isCancelConfirmationActive={isCancelConfirmationActive}
        previousButtonLabel={previousButtonLabel}
        nextButtonLabel={nextButtonLabel}
        submitButtonLabel={submitButtonLabel}
        questionnairePageAnsweredCallback={(
          page,
          map,
          isFinished,
          isCancelled,
          isSavedForLater,
          noVisibleComponent,
          isPageChanged,
        ) =>
          questionnairePageAnsweredCallback(
            page,
            map,
            isFinished,
            isCancelled,
            isSavedForLater,
            noVisibleComponent,
            isPageChanged,
          )
        }
        handleBackNavigation={handleBackNavigation}
        questionnaireTitleRef={questionnaireTitleRef}
        handleSaveForLaterRef={handleSaveForLaterRef}
      />
    );
  };

  const theme = useTheme();
  const useStyles = () => ({
    root: {
      [theme.breakpoints.between('sm', 'md')]: {
        maxWidth: '100%',
        flexBasis: '100%',
        marginBottom: '16px',
      },
    },
    main: {
      [theme.breakpoints.between('sm', 'md')]: {
        marginLeft: '0px',
      },
    },
  });
  const styles = useStyles();

  return (
    <Box sx={{ px: 2 }}>
      <Grid container columnSpacing={1} sx={{ pb: 1 }} ref={questionnaireTitleRef}>
        <Grid item xs={12} sx={{ pb: 0 }}>
          <Typography variant="h5">{isPageFailed ? 'Unknown Questionnaire' : fhirQuestionnaire.title}</Typography>
        </Grid>
        {(enableStatus || fhirQuestionnaire.description !== 'cannot locate string') && (
          <Grid item xs={12} sm={3} sx={{ display: { xs: 'none', md: 'block' }, ...styles.root }}>
            <Paper
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                gap: '8px',
                p: 1,
                px: '16px',
                pt: 2,
              }}
            >
              <QuestionnaireProgress
                progress={progress}
                isStatusEnabled={enableStatus}
                isProgressPercentageEnabled={enableProgressPercentage}
              />
              <QuestionnaireDescription description={isPageFailed ? '------' : fhirQuestionnaire.description} />
            </Paper>
          </Grid>
        )}
        <Grid
          item
          sm={enableStatus || fhirQuestionnaire.description !== 'cannot locate string' ? 9 : 12}
          sx={{ mt: { xs: 2, sm: 0 }, ...styles.root }}
        >
          <Paper sx={{ display: 'flex', flexDirection: 'column', ...styles.main }}>
            {questionnaireRenderedContent()}
          </Paper>
        </Grid>
      </Grid>

      <Dialog
        open={isErrorDialogOpen}
        onClose={() => setIsErrorDialogOpen(false)}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">Questionnaire is Malformed</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            Questionnaire content is malformed. Please contact technical support
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setIsErrorDialogOpen(false);
              setIsPageFailed(true);
            }}
          >
            OK
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export { Questionnaire };
