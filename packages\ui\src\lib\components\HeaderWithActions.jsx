import React from 'react';
import { Grid, Typography, Stack } from '@mui/material';

const HeaderWithActions = ({ title, actionButtons }) => {
  return (
    <Grid container justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
      <Grid item>
        <Typography variant="h1">{title || 'Untitled'}</Typography>
      </Grid>
      <Grid item>
        <Stack direction="row" spacing={2}>
          {actionButtons && actionButtons.map((button, index) => <React.Fragment key={index}>{button}</React.Fragment>)}
        </Stack>
      </Grid>
    </Grid>
  );
};

export default HeaderWithActions;
