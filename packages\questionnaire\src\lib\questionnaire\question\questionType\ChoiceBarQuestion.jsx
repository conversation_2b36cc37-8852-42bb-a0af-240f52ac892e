import React from 'react';
import { Grid } from '@mui/material';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import * as QuestionnaireUtility from '../../utility/questionnaireUtility';

function ChoiceBarQuestion(props) {
  const { question, handleQuestionResponse } = props;

  const [isReadOnly, setIsReadOnly] = React.useState(() => handleQuestionResponse === undefined);

  let selectedAnswerId = question.answer.valueCoding === undefined ? '' : question.answer.valueCoding.id;
  const [response, setResponse] = React.useState(selectedAnswerId);

  const range = question.question.answerOption.length + 1;
  const startLabel = QuestionnaireUtility.extractExtension(
    question.question.extension,
    'Item/bar-start-label',
  ).valueString;
  const endLabel = QuestionnaireUtility.extractExtension(question.question.extension, 'Item/bar-end-label').valueString;

  const handleChoiceBarClick = (event) => {
    if (!isReadOnly) {
      let newResponse = null;
      if (event.target.id === response) {
        newResponse = null;
      } else {
        newResponse = event.target.id;
      }
      setResponse(newResponse);

      let selectedOption = {};
      question.question.answerOption.forEach((element, index) => {
        if (element.valueCoding.id === newResponse) {
          selectedOption = element;
        }
      });

      question.answer = selectedOption;
      handleQuestionResponse(question);
    }
  };

  if (selectedAnswerId !== response) {
    setResponse(selectedAnswerId);
  }

  const configureComponentStyle = (valueId) => {
    let componentStyle;
    if (response === valueId) {
      componentStyle = {
        border: 1,
        borderColor: 'divider',
        backgroundColor: 'primary.main',
        color: 'white',
        cursor: 'pointer',
      };
    } else {
      componentStyle = {
        border: 1,
        borderColor: 'divider',
        cursor: 'pointer',
      };
    }
    return componentStyle;
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <Grid container>
        <Grid item xs={12} sx={{ overflowX: 'auto', whiteSpace: 'nowrap' }}>
          <Table>
            <TableBody>
              <TableRow>
                <TableCell sx={{ p: 0 }} colSpan={parseInt(range / 2)}>
                  <div dangerouslySetInnerHTML={{ __html: startLabel }} />
                </TableCell>
                {range > 1 && range % 2 === 1 && <TableCell></TableCell>}

                <TableCell sx={{ p: 0 }} colSpan={parseInt(range / 2)} align="right">
                  <div dangerouslySetInnerHTML={{ __html: endLabel }} />
                </TableCell>
              </TableRow>
              <TableRow>
                {question.question.answerOption.map((value, index) => {
                  return (
                    <TableCell
                      size="small"
                      id={value.valueCoding.id}
                      key={value.valueCoding.id}
                      align="center"
                      sx={configureComponentStyle(value.valueCoding.id)}
                      onClick={(event) => handleChoiceBarClick(event)}
                    >
                      {value.valueCoding.display}
                    </TableCell>
                  );
                })}
              </TableRow>
            </TableBody>
          </Table>
        </Grid>
      </Grid>
      <Explanation question={question} />
    </>
  );
}

ChoiceBarQuestion.defaultProps = {
  isEdit: true,
  responses: {},
  alignment: 'column',
  questionDescription: '',
};

export default ChoiceBarQuestion;
