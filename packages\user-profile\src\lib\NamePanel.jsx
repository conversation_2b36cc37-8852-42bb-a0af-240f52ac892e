import React from 'react';
import { <PERSON><PERSON>, <PERSON>ack, TextField } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { UploadProfile } from './UploadProfile';
import { useTranslation } from 'react-i18next';

function NamePanel(props) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { icon } = props;
  const { firstName: initialFirstName, middleName: initialMiddleName, lastName: initialLastName } = props.userDetail;

  const [firstName, setFirstName] = React.useState(initialFirstName || '');
  const [middleName, setMiddleName] = React.useState(initialMiddleName || '');
  const [lastName, setLastName] = React.useState(initialLastName || '');
  const userInitials = firstName.charAt(0).toUpperCase() + lastName.charAt(0).toUpperCase();

  const [isFirstNameValid, setIsFirstNameValid] = React.useState(!!firstName);
  const [isLastNameValid, setIsLastNameValid] = React.useState(!!lastName);
  const [firstNameError, setFirstNameError] = React.useState('');
  const [lastNameError, setLastNameError] = React.useState('');
  const [imageWarningVisible, setImageWarningVisible] = React.useState(false);

  // Update validity states based on props
  React.useEffect(() => {
    validateName(firstName, 'firstName');
    validateName(lastName, 'lastName');
  }, [firstName, lastName]);

  const validateName = (name, type) => {
    if (type === 'firstName') {
      if (name.length <= 0) {
        setIsFirstNameValid(false);
        setFirstNameError(t('First Name is a required field.'));
      } else {
        setIsFirstNameValid(true);
        setFirstNameError('');
      }
    } else if (type === 'lastName') {
      if (name.length <= 0) {
        setIsLastNameValid(false);
        setLastNameError(t('Last Name is a required field.'));
      } else {
        setIsLastNameValid(true);
        setLastNameError('');
      }
    }
  };

  const handleTextFieldChange = (event, key) => {
    const value = event.target.value;
    if (key === 'firstName') {
      setFirstName(value);
      validateName(value, 'firstName');
    } else if (key === 'middleName') {
      setMiddleName(value);
    } else if (key === 'lastName') {
      setLastName(value);
      validateName(value, 'lastName');
    }
    props.updateProfileDataCallback('property', 'change', key, value);
  };

  // Handler for profile image upload
  const handleProfileImage = (file) => {
    setImageWarningVisible(false);
    props.updateProfileDataCallback('property', 'change', 'photoImage', file);
  };

  const handleInvalidProfileImage = (target, reason) => {
    setImageWarningVisible(true);
  };

  return (
    <Stack direction="column" alignItems="left" spacing={2}>
      {(props.nameTabConfig?.includes('firstName') || !props.nameTabConfig) && (
        <TextField
          required
          error={!isFirstNameValid}
          id="first-name"
          label={t('First Name')}
          value={firstName}
          sx={{ maxWidth: '500px' }}
          onChange={(event) => handleTextFieldChange(event, 'firstName')}
          helperText={firstNameError}
        />
      )}

      {(props.nameTabConfig?.includes('middleName') || !props.nameTabConfig) && (
        <TextField
          id="middle-name"
          label={t('Middle Name')}
          value={middleName}
          sx={{ maxWidth: '500px' }}
          onChange={(event) => handleTextFieldChange(event, 'middleName')}
        />
      )}

      {(props.nameTabConfig?.includes('lastName') || !props.nameTabConfig) && (
        <TextField
          required
          error={!isLastNameValid}
          id="last-name"
          label={t('Last Name')}
          value={lastName}
          sx={{ maxWidth: '500px' }}
          onChange={(event) => handleTextFieldChange(event, 'lastName')}
          helperText={lastNameError}
        />
      )}

      {imageWarningVisible && (
        <Stack direction="column" alignItems="left" sx={{ mt: '20px' }}>
          <Alert sx={{ width: 'fit-content' }} severity="warning">
            Upload image failed. Only JPG and PNG formats accepted. Image must be under 200kb. Please try again.
          </Alert>
        </Stack>
      )}

      <UploadProfile
        defaultText={userInitials}
        changeCallback={handleProfileImage}
        errorCallback={handleInvalidProfileImage}
        maximumSize={200 * 1024}
        photoImageUrl={icon}
      />
    </Stack>
  );
}

export default NamePanel;
