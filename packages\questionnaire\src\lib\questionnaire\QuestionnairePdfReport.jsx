import React from 'react';
import { Box } from '@mui/material';
import pdfObject from 'pdfobject';

/*
*
    This component accepts couple of props
    - 'pdf':  which must be a base64
    - 'height': a string value, default to 80vh
*
*/

function QuestionnairePdfReport(props) {
  const { pdf, height } = props;

  const getPdfBlob = () => {
    const base64str = pdf;
    // decode base64 string, remove space for IE compatibility
    const binary = window.atob(base64str.replace(/\s/g, ''));
    const len = binary.length;
    const buffer = new ArrayBuffer(len);
    const view = new Uint8Array(buffer);
    for (let index = 0; index < len; index++) {
      view[index] = binary.charCodeAt(index);
    }

    const blob = new Blob([view], { type: 'application/pdf' });
    const pdfUrl = URL.createObjectURL(blob);

    return pdfUrl;
  };

  const options = {
    height: height || '80vh',
    fallbackLink:
      "<p>This browser does not support inline PDFs. Please download the PDF to view it: <a href='[url]' target='_blank'>Download PDF</a></p>",
  };

  React.useEffect(() => {
    pdfObject.embed(getPdfBlob(), '#pdf-container', options);
  }, [pdf]);

  return (
    <Box sx={{ width: '100%' }}>
      <div id="pdf-container"></div>
    </Box>
  );
}

export { QuestionnairePdfReport };
