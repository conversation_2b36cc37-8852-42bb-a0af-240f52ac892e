import React from 'react';
import TurnedInNotIcon from '@mui/icons-material/TurnedInNot';
import DeleteIcon from '@mui/icons-material/Delete';
import { Button, Stack } from '@mui/material';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';

function SaveLater(props) {
  const { isActive, disabled, handleSaveForLater, handleCancelled, isCancelConfirmationActive } = props;

  const [cancelConfirmationDialogOpen, setCancelConfirmationDialogOpen] = React.useState(false);

  if (!isActive) {
    return <></>;
  }

  return (
    <Stack justifyContent="flex-end" direction="row" alignItems="right">
      {isCancelConfirmationActive ? (
        <Button
          sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
          disabled={disabled}
          variant="text"
          startIcon={<DeleteIcon />}
          onClick={() => setCancelConfirmationDialogOpen(true)}
        >
          Delete
        </Button>
      ) : (
        <Button
          sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
          disabled={disabled}
          variant="text"
          startIcon={<DeleteIcon />}
          onClick={() => handleCancelled()}
        >
          Delete
        </Button>
      )}
      <Button
        sx={{ '&:hover': { backgroundColor: 'transparent', color: '#23527c' } }}
        disabled={disabled}
        variant="text"
        startIcon={<TurnedInNotIcon />}
        onClick={() => handleSaveForLater()}
      >
        Save for Later
      </Button>

      <Dialog key="cancel-confirmation-dialog" open={cancelConfirmationDialogOpen} sx={{}}>
        <DialogTitle>Delete Response</DialogTitle>
        {/* <DialogContent>
          <DialogContentText>Progress will not be saved.</DialogContentText>
        </DialogContent> */}
        <DialogActions>
          <Button
            variant="outlined"
            onClick={() => {
              setCancelConfirmationDialogOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            variant="contained"
            color="error"
            sx={{ marginLeft: 2 }}
            onClick={() => {
              setCancelConfirmationDialogOpen(false);
              handleCancelled();
            }}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Stack>
  );
}

export { SaveLater };
