import React from 'react';
import { ListItemIcon, ListItemText, Menu, MenuItem } from '@mui/material';

export const MenuList = (props) => {
  const { anchorEl, setAnchorEl, menuItems } = props;

  const open = <PERSON><PERSON>an(anchorEl);

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Menu anchorEl={anchorEl} open={open} onClose={handleClose}>
      {menuItems
        ?.filter((item) => item.show !== false)
        .map((item) => (
          <MenuItem
            key={item?.id}
            onClick={() => {
              handleClose();
              item.handleClick();
            }}
          >
            <ListItemIcon>{item.icon}</ListItemIcon>
            <ListItemText>{item.label}</ListItemText>
          </MenuItem>
        ))}
    </Menu>
  );
};
