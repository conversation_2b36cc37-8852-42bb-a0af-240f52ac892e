import React from 'react';
import { Box, TextField, Button, Grid, IconButton, Autocomplete } from '@mui/material';
import { styled } from '@mui/material/styles';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

// Custom styled components
const StyledTextField = styled(TextField)(() => ({
  '& fieldset': {
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
}));

const StyledButton = styled(Button)(() => ({
  borderTopLeftRadius: 0,
  borderBottomLeftRadius: 0,
}));

export const MessageFilters = ({
  // Filter values
  searchText,
  dateRange,
  startDate,
  endDate,
  dateRangePresets,
  selectedDateRangeOption,

  // Event handlers
  onSearchChange,
  onSearchClear,
  onDateRangeChange,
  onStartDateChange,
  onEndDateChange,
}) => {
  return (
    <Box sx={{ p: { xs: 1, sm: 2, md: 2 } }}>
      <Grid
        container
        spacing={2}
        sx={{
          flexDirection: { xs: 'column', sm: 'row', md: 'row' },
          alignItems: 'stretch',
          flexWrap: { xs: 'nowrap', sm: 'wrap', md: 'nowrap' },
          '& > .MuiGrid-item': { width: { xs: '100%', sm: '50%', md: 'auto' } },
        }}
      >
        {/* Quick Search Field */}
        <Grid item xs={12} sm={3} md="auto" sx={{ flexGrow: 1, minWidth: { md: '300px' } }}>
          <Box display="flex" alignItems="center">
            <StyledTextField
              variant="outlined"
              label="Search"
              size="small"
              fullWidth
              sx={{ width: { lg: '250px', md: '200px' } }}
              value={searchText}
              onChange={onSearchChange}
              InputLabelProps={{ shrink: true }}
              InputProps={{
                endAdornment: searchText && (
                  <IconButton color="primary" aria-label="clear" onClick={onSearchClear} edge="end">
                    <CloseIcon />
                  </IconButton>
                ),
              }}
            />
            <StyledButton disableElevation variant="contained" style={{ height: '40px', width: '40px', minWidth: 0 }}>
              <SearchIcon />
            </StyledButton>
          </Box>
        </Grid>

        {/* Date Range */}
        <Grid item xs={12} sm={3} md="auto" lg="auto" sx={{ flexGrow: 1, minWidth: { sm: '160px', lg: '500px' } }}>
          <Autocomplete
            disablePortal
            options={dateRangePresets}
            onChange={onDateRangeChange}
            value={selectedDateRangeOption}
            getOptionLabel={(option) => option.label}
            renderInput={(params) => <TextField {...params} label="Date Range" size="small" />}
            isOptionEqualToValue={(option, value) => option.label === value.label}
          />
        </Grid>

        {/* Start / End Date Pickers */}
        <Grid item xs={12} sm={6} md="auto" lg="auto" sx={{ display: 'flex', gap: 2 }}>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              label="Start Date"
              value={startDate}
              onChange={onStartDateChange}
              slotProps={{ textField: { size: 'small' } }}
            />
            <DatePicker
              label="End Date"
              value={endDate}
              onChange={onEndDateChange}
              slotProps={{ textField: { size: 'small' } }}
            />
          </LocalizationProvider>
        </Grid>
      </Grid>
    </Box>
  );
};
