import React from 'react';
import { <PERSON>, But<PERSON>, Grid, Stack } from '@mui/material';
import {
  QuestionnaireReportV2 as Questionnaire<PERSON>eport,
  QuestionnaireReportViewerV2 as Questionnaire<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>V<PERSON> as Banner,
} from '../lib/questionnaire';
import { FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER_RESPONSE } from './data/questionnaireFhir/FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER_RESPONSE';
import { FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER } from './data/questionnaireFhir/FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER';
import { FHIR_ALL_QUESTION } from './data/questionnaireFhir/FHIR_ALL_QUESTION';
import { FHIR_ALL_QUESTION_RESPONSE } from './data/questionnaireFhir/FHIR_ALL_QUESTION_RESPONSE';
import { FHIR_SIMPLE_GRID_QUESTION } from './data/questionnaireFhir/FHIR_SIMPLE_GRID_QUESTION';
import { FHIR_SIMPLE_GRID_QUESTION_RESPONSE } from './data/questionnaireFhir/FHIR_SIMPLE_GRID_QUESTION_RESPONSE';
import { FHIR_GRID_QUESTION } from './data/questionnaireFhir/FHIR_GRID_QUESTION';
import { FHIR_GRID_QUESTION_RESPONSE } from './data/questionnaireFhir/FHIR_GRID_QUESTION_RESPONSE';
import { TEST_CONVERSION } from './data/questionnaireFhir/TEST_CONVERSION';
import { TEST_CONVERSION_RESPONSE } from './data/questionnaireFhir/TEST_CONVERSION_RESPONSE';
import { HTML_TEMPLATE_REPORT } from './data/questionnaireFhir/HTML_TEMPLATE_REPORT';
import { HTML_TEMPLATE_REPORT_RESPONSE } from './data/questionnaireFhir/HTML_TEMPLATE_REPORT_RESPONSE';
import { FHIR_MULTIPLE_CHOICE_OTHER } from './data/questionnaireFhir/FHIR_MULTIPLE_CHOICE_OTHER';
import { FHIR_MULTIPLE_CHOICE_OTHER_RESPONSE } from './data/questionnaireFhir/FHIR_MULTIPLE_CHOICE_OTHER_RESPONSE';
import { extractExtensionV2 as extractExtension } from '../lib/questionnaire';
import { bc_cancer, bc_response, cv, cv_response } from './data/questionnaireFhir/bc_cancer';
import { REPORT_PRINT_PDF } from './data/questionnaireFhir/REPORT_PRINT_PDF';
import { REPORT_PRINT_PDF_RESPONSE } from './data/questionnaireFhir/REPORT_PRINT_PDF_RESPONSE';
import { ALL_QUESTION_QUESTIONNAIRE } from './data/questionnaireFhir/ALL_QUESTION_QUESTIONNAIRE';
import { ALL_QUESTION_QUESTIONNAIRE_RESPONSE } from './data/questionnaireFhir/ALL_QUESTION_QUESTIONNAIRE_RESPONSE';

function Empty(props) {
  return <div>This component is empty</div>;
}

function QuestionnaireFhirReportTestPage() {
  const [component, setComponent] = React.useState(<Empty />);

  const updateComponent = (component) => {
    setComponent(component);
  };

  const organization = {
    organizationName: 'Cambian',
    tagline: 'Digital Health Platform',
    aboutUs: 'Cambian is a leading digital health information solutions provider.',
    address: 'Suite 1690 13450 – 102nd Ave Surrey, BC V3T 5X3  Canada',
    email: '<EMAIL>',
    contactFax: '',
    contactPhone: '************',
  };

  const demographic = {
    individualId: '7451e9eb-3f2b-495f-b41f-cbbc446f8e99',
    dateOfBirth: '2001-12-24',
    firstName: 'Disha',
    middleName: 'Sanjay',
    lastName: 'Dharmadhikari',
    gender: 'FEMALE',
    preferredContactMechanism: 'Email',
    subscribeToNotifications: true,
    emailAddresses: [
      {
        id: '9cf79888-589b-423d-8e8f-ec9e3578f26c',
        emailAddress: '<EMAIL>',
        primary: true,
        note: 'email',
        verified: false,
      },
      {
        id: '9c73588d-39dd-4367-8b88-87e0e222de7d',
        emailAddress: '<EMAIL>',
        primary: false,
        note: null,
        verified: false,
      },
    ],
    phoneNumbers: [
      {
        id: '276dd74e-b392-4c29-b9b8-93db621be987',
        phoneNumber: '+91 **********',
        primary: false,
        note: null,
        verified: false,
      },
      {
        id: '3e0e8182-611e-464d-803a-b950c268eb09',
        phoneNumber: '+91 9921182313',
        primary: true,
        note: null,
        verified: false,
      },
    ],
    addresses: [
      {
        id: '4af0f11e-287c-463b-885d-ff204796b465',
        purpose: null,
        address1: '1234 Maple Street',
        address2: 'Apartment 567',
        city: 'Vancouver',
        postalCode: 'V6J 1Z6',
        primary: false,
        province: 'BC',
        country: 'Canada',
      },
      {
        id: '68e62cdc-c4a7-4f8c-a5e2-80c8d262eb09',
        purpose: null,
        address1: '4321 Oka Avenue',
        address2: 'suite 101',
        city: 'Vancouver ',
        postalCode: 'V5K0AL',
        primary: true,
        province: 'BC',
        country: 'Canada',
      },
    ],
    healthCareIds: [
      {
        id: '3d262397-16d1-4312-b643-fefd6f558e43',
        type: 'PHN',
        value: '**********',
        issuer: 'BC',
        primary: true,
      },
      {
        id: '1a11af42-0327-4799-a5c8-5be5395f966a',
        type: 'PHN',
        value: '**********',
        issuer: 'BC',
        primary: false,
      },
    ],
    communicationPreferences: {
      sendEmail: false,
      sendSms: false,
      notificationFrequency: 'IMMEDIATE',
    },
  };

  return (
    <div style={{ backgroundColor: '#F5F5F5' }}>
      <h1>Questionnaire FHIR REPORT Page</h1>
      <Grid container direction="row">
        <Box component={Grid} item xs={3} display={{ xs: 'block' }}>
          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>All Question Questionnaire</h4>
                  <QuestionnaireReportViewer
                    fhirQuestionnaire={ALL_QUESTION_QUESTIONNAIRE}
                    fhirResponse={ALL_QUESTION_QUESTIONNAIRE_RESPONSE}
                    browserTimezone={'America/Vancouver'}
                  />
                </Stack>,
              );
            }}
          >
            All Question Questionnaire
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>Report Print PDF</h4>
                  <QuestionnaireReportViewer
                    fhirQuestionnaire={REPORT_PRINT_PDF}
                    fhirResponse={REPORT_PRINT_PDF_RESPONSE}
                  />
                </Stack>,
              );
            }}
          >
            Report Print PDF
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2} sx={{ minWidth: '100%' }}>
                  <h4>Converted Fhir Report</h4>
                  <QuestionnaireReportViewer fhirQuestionnaire={cv} fhirResponse={cv_response} />
                </Stack>,
              );
            }}
          >
            Multiple Choice Other Option
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              const pdfExtension = extractExtension(TEST_CONVERSION_RESPONSE.extension, '/generated-pdf-report');
              const pdf = pdfExtension ? pdfExtension.valueString : '';
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2} sx={{ minWidth: '100%' }}>
                  <h4>Converted Fhir Report</h4>
                  <QuestionnaireReportViewer
                    demographic={{
                      firstName: 'Bruce',
                      lastName: 'Forde',
                      phn: '948373837',
                      dateOfBirth: '1985-11-12',
                      email: '<EMAIL>',
                    }}
                    fhirQuestionnaire={HTML_TEMPLATE_REPORT}
                    fhirResponse={HTML_TEMPLATE_REPORT_RESPONSE}
                    isWebReportAvailable={true}
                    pdf={pdf}
                  />
                </Stack>,
              );
            }}
          >
            HTML Template Report
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              const pdfExtension = extractExtension(TEST_CONVERSION_RESPONSE.extension, '/generated-pdf-report');
              const pdf = pdfExtension ? pdfExtension.valueString : '';

              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2} sx={{ minWidth: '100%' }}>
                  <h4>Converted Fhir Report</h4>
                  <QuestionnaireReportViewer
                    fhirQuestionnaire={TEST_CONVERSION}
                    fhirResponse={TEST_CONVERSION_RESPONSE}
                    isWebReportAvailable={true}
                    saveToCambianAccountCallback={() => console.log('hello world')}
                    pdf={pdf}
                  />
                </Stack>,
              );
            }}
          >
            Test Converted Fhir Report with PDF
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>EQ-5D-5L FHIR Report with Header</h4>
                  <QuestionnaireReport
                    fhirQuestionnaire={FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER}
                    fhirResponse={FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER_RESPONSE}
                  />
                </Stack>,
              );
            }}
          >
            Simple EQ-5D-5L Questionnaire
          </Button>
          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <h4>All Question Questionnaire</h4>
                  <QuestionnaireReport
                    fhirQuestionnaire={FHIR_ALL_QUESTION}
                    fhirResponse={FHIR_ALL_QUESTION_RESPONSE}
                  />
                </Stack>,
              );
            }}
          >
            All Question Questionnaire
          </Button>

          <Button
            variant="contained"
            onClick={() => {
              updateComponent(
                <Stack direction="column" alignItems="center" spacing={2}>
                  <Banner
                    demographic={demographic}
                    organization={organization}
                    bannerTemplate="<b>{Client.lastName}</b>, <i>{Client.firstName}</i> {Client.middleName} | {Org.name}"
                  ></Banner>
                  <h4>Simple Grid Question</h4>
                  <QuestionnaireReport
                    fhirQuestionnaire={FHIR_SIMPLE_GRID_QUESTION}
                    fhirResponse={FHIR_SIMPLE_GRID_QUESTION_RESPONSE}
                  />

                  <h4>Multi page grid question</h4>
                  <QuestionnaireReport
                    fhirQuestionnaire={FHIR_GRID_QUESTION}
                    fhirResponse={FHIR_GRID_QUESTION_RESPONSE}
                  />
                </Stack>,
              );
            }}
          >
            Grid Questionnaire
          </Button>
        </Box>

        <Box component={Grid} item xs={9} display={{ xs: 'block' }}>
          <div
            style={{
              margin: '25px',
              outline: 'dashed 1px black',
            }}
          >
            {component}
          </div>
        </Box>
      </Grid>
    </div>
  );
}

export { QuestionnaireFhirReportTestPage };
