import React from 'react';
import { FormControl, TextField, FormHelperText, Grid } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import * as QuestionUtility from '../../utility/questionUtility';

function DecimalQuestion(props) {
  const { question, handleQuestionResponse } = props;

  const [isReadOnly] = React.useState(() => handleQuestionResponse === undefined);
  const [isValid, setIsValid] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState('');

  const extractExistingAnswer = () => {
    let answer = '';
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      // explicit nullcheck or else it will dismiss the value 0
      answer = question.answer.valueDecimal !== null ? question.answer.valueDecimal : '';
    }
    return answer;
  };

  const [value, setValue] = React.useState(() => extractExistingAnswer());

  const handleChange = (event) => {
    let characterLimit = 255;
    const inputValue = event.target.value.trim();
    if (
      !isReadOnly &&
      QuestionUtility.isNumberValue(event.target.value) &&
      event.target.value.length <= characterLimit
    ) {
      if (event.target.value !== '') {
        setValue(inputValue);

        let [isValidContent, message] = QuestionUtility.isDecimalContentValid(event.target.value, question);
        if (isValidContent != null) {
          setIsValid(isValidContent);
          setErrorMessage(message);
        }
      } else {
        setIsValid(true);
        setErrorMessage('');
      }

      setValue(inputValue);
      question.answer = {
        valueDecimal: inputValue,
      };
      handleQuestionResponse(question);
    }
  };

  const renderHelperText = (text) => {
    if (isValid) {
      return <></>;
    }
    return (
      <FormHelperText error id="component-error-text">
        {text}
      </FormHelperText>
    );
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <Grid container>
        <Grid item xs={12} sm={7}>
          <FormControl variant="outlined" fullWidth sx={{ m: 1, ml: 0, mb: 0 }}>
            <TextField
              id="outlined-adornment-text"
              size="small"
              multiline={isReadOnly}
              value={value}
              autoComplete="off"
              onChange={(event) => handleChange(event)}
              placeholder={!isReadOnly ? 'Please enter a number' : ''}
            />
            {!isValid ? renderHelperText(errorMessage) : <></>}
          </FormControl>
        </Grid>
      </Grid>

      <Explanation question={question} />
    </>
  );
}

export default DecimalQuestion;
