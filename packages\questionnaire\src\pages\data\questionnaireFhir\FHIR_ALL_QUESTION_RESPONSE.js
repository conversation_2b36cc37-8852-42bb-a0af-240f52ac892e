export const FHIR_ALL_QUESTION_RESPONSE = {
  resourceType: 'QuestionnaireResponse',
  identifier: [],
  questionnaire: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
  contained: [],
  status: 'completed',
  authored: '2022-11-15T07:33:29.036Z',
  extension: [
    {
      url: 'QuestionnaireResponse/questionnaire-response-type',
      valueCode: 'instrument-response',
    },
  ],
  item: [
    {
      id: '527734',
      linkId: '1',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'Warning Explanation Message',
        },
        {
          url: 'Questionnaire/Item/explanation-flag',
          valueString: 'WARNING',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/horizontal-orientation',
          valueBoolean: true,
        },
        {
          url: 'Questionnaire/Item/multiple-answer-choice',
          valueBoolean: false,
        },
      ],
      text: 'Have you ever been treated in an emergency room?',
      answer: [
        {
          valueCoding: {
            id: '527737',
            code: '2',
            display: 'Yes as a child',
          },
        },
      ],
    },
    {
      id: '527754',
      linkId: '3',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'Warning Explanation Message',
        },
        {
          url: 'Questionnaire/Item/explanation-flag',
          valueString: 'WARNING',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
      ],
      text: 'Have you ever been rushed to the hospital in an ambulance? If Yes, do you remember why?',
      answer: [
        {
          valueString: 'text answer',
        },
      ],
    },
    {
      id: '538185',
      linkId: '4',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'test ',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'Warning Explanation Message',
        },
        {
          url: 'Questionnaire/Item/explanation-flag',
          valueString: 'WARNING',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
      ],
      text: 'Date and Type Question',
      answer: [
        {
          valueDateTime: '2022-11-23T19:36:00.000Z',
        },
      ],
    },
    {
      id: '538188',
      linkId: '5',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'Warning Explanation Message',
        },
        {
          url: 'Questionnaire/Item/explanation-flag',
          valueString: 'WARNING',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
      ],
      text: 'Only Date Question',
      answer: [
        {
          valueDate: '2022-11-22T18:30:00.000Z',
        },
      ],
    },
    {
      id: '527770',
      linkId: '6',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'Error Explanation Message',
        },
        {
          url: 'Questionnaire/Item/explanation-flag',
          valueString: 'ERROR',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/multiple-answer-choice',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/display-type',
          valueString: 'choice-bar',
        },
        {
          url: 'Questionnaire/Item/bar-start-label',
          valueString: 'Min',
        },
        {
          url: 'Questionnaire/Item/bar-end-label',
          valueString: 'Max',
        },
      ],
      text: 'Have you ever been admitted to the hospital?',
      answer: [
        {
          valueCoding: {
            id: '527779',
            code: '4',
            display: '4',
          },
        },
      ],
    },
    {
      id: '546452',
      linkId: '8',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'Information Explanation Message',
        },
        {
          url: 'Questionnaire/Item/explanation-flag',
          valueString: 'INFO',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/display-type',
          valueString: 'numeric-slider',
        },
        {
          url: 'Questionnaire/Item/slider-min-value',
          valueDecimal: 0,
        },
        {
          url: 'Questionnaire/Item/slider-max-value',
          valueDecimal: 100,
        },
        {
          url: 'Questionnaire/Item/slider-min-label',
          valueString: 'min',
        },
        {
          url: 'Questionnaire/Item/slider-max-label',
          valueString: 'max',
        },
        {
          url: 'Questionnaire/Item/slider-min-exclusion',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/slider-max-exclusion',
          valueBoolean: false,
        },
      ],
      text: 'Numeric slider question type',
      answer: [
        {
          valueInteger: 27,
        },
      ],
    },
    {
      id: '538821',
      linkId: '10',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'Warning Explanation Message',
        },
        {
          url: 'Questionnaire/Item/explanation-flag',
          valueString: 'WARNING',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/multiple-answer-choice',
          valueBoolean: true,
        },
        {
          url: 'Questionnaire/Item/display-type',
          valueString: 'choice-image',
        },
        {
          url: 'Questionnaire/Item/image-path',
          valueString: '/api/media/body_diagram.jpg',
        },
        {
          url: 'Questionnaire/Item/image-width',
          valueInteger: 795,
        },
        {
          url: 'Questionnaire/Item/image-height',
          valueInteger: 698,
        },
      ],
      text: 'Body diagram',
      answer: [
        {
          valueCoding: {
            id: '538852',
            extension: [
              {
                url: 'Questionnaire/Item/AnswerOption/ValueCoding/image-response-shape',
                valueString: 'poly',
              },
              {
                url: 'Questionnaire/Item/AnswerOption/ValueCoding/image-response-coordinates',
                valueString:
                  '186,136,197,139,203,139,210,138,218,135,223,134,225,141,230,151,238,158,246,165,257,173,265,179,268,182,265,218,264,218,262,218,261,227,259,257,258,263,258,271,258,274,259,278,254,278,249,276,244,274,240,272,236,268,234,265,230,259,229,253,227,250,224,245,221,241,218,237,212,234,208,234,204,234,201,236,200,238,198,240,196,244,191,252,186,262,184,266,181,270,178,273,175,275,172,276,167,278,160,278,155,279,158,271,160,264,160,261,158,247,156,229,155,220,152,212,152,199,151,192,150,186,148,184,160,174,170,163,179,153,182,147,185,140',
              },
              {
                url: 'Questionnaire/Item/AnswerOption/ValueCoding/image-response-color',
                valueString: '0',
              },
            ],
            code: '16',
            display: 'Chest',
          },
        },
      ],
    },
    {
      id: '564904',
      linkId: '9',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/min-value',
          valueDecimal: 0,
        },
        {
          url: 'Questionnaire/Item/min-exclusion',
          valueBoolean: true,
        },
        {
          url: 'Questionnaire/Item/max-exclusion',
          valueBoolean: false,
        },
      ],
      text: 'What is your age?',
      answer: [
        {
          valueDecimal: '21',
        },
      ],
    },
    {
      id: '564905',
      linkId: '10',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/min-value',
          valueDecimal: 10,
        },
        {
          url: 'Questionnaire/Item/max-value',
          valueDecimal: 20,
        },
        {
          url: 'Questionnaire/Item/min-exclusion',
          valueBoolean: true,
        },
        {
          url: 'Questionnaire/Item/max-exclusion',
          valueBoolean: true,
        },
      ],
      text: 'What is your age? (between 10 to 20 excluding 10 and 20',
      answer: [
        {
          valueDecimal: '19',
        },
      ],
    },
    {
      id: '564906',
      linkId: '11',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/min-value',
          valueDecimal: 20,
        },
        {
          url: 'Questionnaire/Item/max-value',
          valueDecimal: 40,
        },
        {
          url: 'Questionnaire/Item/min-exclusion',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/max-exclusion',
          valueBoolean: false,
        },
      ],
      text: 'What is your age? (between 20 and 40 including 20 and 40)',
      answer: [
        {
          valueDecimal: '21',
        },
      ],
    },
    {
      id: '564907',
      linkId: '12',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/explanation',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/max-value',
          valueDecimal: 100,
        },
        {
          url: 'Questionnaire/Item/min-exclusion',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/max-exclusion',
          valueBoolean: true,
        },
      ],
      text: 'What is your age? (less than 100 )',
      answer: [
        {
          valueDecimal: '50',
        },
      ],
    },
  ],
};
