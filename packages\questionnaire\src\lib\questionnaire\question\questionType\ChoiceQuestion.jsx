import React, { Fragment } from 'react';
import * as QuestionUtility from '../../utility/questionUtility';
import { FormControlLabel, Radio, RadioGroup, Stack, TextField, useRadioGroup } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import { styled } from '@mui/material/styles';
import { extractExtension } from '../../utility/questionnaireUtility';

const StyledFormControlLabel = styled((props) => <FormControlLabel {...props} />)(({ theme, checked }) => ({
  '.MuiFormControlLabel-label': checked && {
    color: theme.palette.primary.main,
  },
}));

function MyFormControlLabel(props) {
  const { value, selected } = props;
  const radioGroup = useRadioGroup();

  let checked = false;

  if (radioGroup) {
    if (selected != null && selected.length === 0) {
      checked = radioGroup.value === props.value;
    } else {
      checked = value === selected;
    }
  }

  return <StyledFormControlLabel checked={checked} {...props} />;
}

function ChoiceQuestion(props) {
  const { question, handleQuestionResponse } = props;
  const [isReadOnly, setIsReadOnly] = React.useState(() => handleQuestionResponse === undefined);
  const otherOptionExtensionUrl = 'Item/AnswerOption/ValueCoding/other-option';

  const getSelectedAnswerId = () => {
    let answerId = '';
    if (question.answer === undefined) return '';

    if (Array.isArray(question.answer)) {
      question.answer.forEach((answer) => {
        if (answer.valueCoding) {
          answerId = answer.valueCoding.id;
        }
      });
    } else if (question.answer.valueCoding !== undefined) {
      answerId = question.answer.valueCoding.id;
    }

    return answerId;
  };

  let selectedAnswerId = getSelectedAnswerId();

  const findOtherOptionTextResponses = () => {
    let otherOptionResponses = {};

    if (question.answer && selectedAnswerId) {
      const { otherOptionValue } = QuestionUtility.extractOtherOptionDetails(question.answer.valueCoding.extension);
      otherOptionResponses[question.answer.valueCoding.id] = otherOptionValue;
    } else if (question.question.answerOption) {
      question.question.answerOption.forEach((answerOption) => {
        const { otherOptionValue } = QuestionUtility.extractOtherOptionDetails(answerOption.valueCoding.extension);
        otherOptionResponses[answerOption.valueCoding.id] = otherOptionValue;
      });
    }
    return otherOptionResponses;
  };

  const [response, setResponse] = React.useState(selectedAnswerId);
  const [otherOptionResponse, setOtherOptionResponse] = React.useState(findOtherOptionTextResponses());

  const rowLayout =
    QuestionUtility.extractHorizontalOrientation(question) == null
      ? question.question.answerOption === undefined
        ? false
        : question.question.answerOption.length > 5
      : QuestionUtility.extractHorizontalOrientation(question);

  const handleRadioButtonClick = (event) => {
    if (!isReadOnly) {
      let newResponse = null;

      if (response !== '' && response !== null && Number(event.target.value) === Number(response)) {
        newResponse = null;
      } else {
        newResponse = event.target.value;
      }
      setResponse(newResponse);

      let selectedOption = {};
      if (newResponse !== null) {
        question.question.answerOption.forEach((element, index) => {
          if (Number(element.valueCoding.id) === Number(newResponse)) {
            if (otherOptionResponse[element.valueCoding.id]) {
              const answerOption = { ...element };
              const textResponse = otherOptionResponse[element.valueCoding.id];
              const additionalTextResponseData = `id:${element.valueCoding.id},question:${
                element.valueCoding.display
              },${textResponse ? `answer:${textResponse}` : ``}`;

              answerOption.valueCoding.extension.forEach((extension) => {
                if (extension.url === otherOptionExtensionUrl || otherOptionExtensionUrl.includes(extension.url)) {
                  extension.valueString = additionalTextResponseData;
                }
              });
              selectedOption = answerOption;
            } else {
              selectedOption = element;
            }
          }
        });
      }

      question.answer = selectedOption;
      handleQuestionResponse(question);
    }
  };

  if (selectedAnswerId !== response) {
    setResponse(selectedAnswerId);
  }

  const handleAdditionalTextboxChange = (event, selectedAnswer, otherOptionExtension) => {
    const TEXT_MAX_LENGTH = 70;
    if (!isReadOnly) {
      let characterLimit = TEXT_MAX_LENGTH;
      let textResponse = event.target.value.substring(0, characterLimit);
      setOtherOptionResponse({ [selectedAnswer.valueCoding.id]: textResponse });
      let updatedQuestion = question;
      let additionalTextResponseData = '';
      if (otherOptionExtension) {
        additionalTextResponseData = `id:${selectedAnswer.valueCoding.id},question:${
          selectedAnswer.valueCoding.display
        },${textResponse ? `answer:${textResponse}` : ``}`;
      }

      updatedQuestion.answer.valueCoding.extension.forEach((extension) => {
        if (extension.url === otherOptionExtensionUrl || otherOptionExtensionUrl.includes(extension.url)) {
          extension.valueString = additionalTextResponseData;
        }
      });

      question.answer = updatedQuestion.answer;
      handleQuestionResponse(question);
    }
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <RadioGroup row={rowLayout} name="radio-buttons-group" value={response}>
        {question.question.answerOption.map((item, index) => {
          const otherOptionExtension = extractExtension(item.valueCoding.extension, otherOptionExtensionUrl);
          const otherOptionAvailable = otherOptionExtension ? otherOptionExtension.valueString : '';
          const { otherOptionId, otherOptionValue } = QuestionUtility.extractOtherOptionDetails(
            item.valueCoding.extension,
          );

          return (
            <Fragment key={item.valueCoding.id}>
              <Stack direction={{ xs: 'column', sm: 'row' }} alignItems={{ xs: 'flex-start', sm: 'center' }}>
                <MyFormControlLabel
                  sx={{ mb: -1.5 }}
                  value={item.valueCoding.id}
                  control={<Radio size="small" onClick={(event) => handleRadioButtonClick(event)} />}
                  label={item.valueCoding.display}
                  selected={response}
                />

                {otherOptionAvailable && otherOptionId && response === Number(otherOptionId) && (
                  <TextField
                    size="small"
                    type="text"
                    multiline={isReadOnly}
                    inputProps={{
                      style: !isReadOnly ? { height: '15px' } : {},
                    }}
                    autoComplete="off"
                    sx={{
                      pt: 1,
                      width: { xs: '100%', sm: `${otherOptionValue.length}ch` },
                      minWidth: { sm: '30ch', md: '25ch' },
                      maxWidth: { sm: '45ch', md: '60ch' },
                      overflow: 'hidden',
                    }}
                    value={isReadOnly ? otherOptionValue : otherOptionResponse[otherOptionId]}
                    onChange={(event) => handleAdditionalTextboxChange(event, item, otherOptionExtension)}
                  />
                )}
              </Stack>
            </Fragment>
          );
        })}
      </RadioGroup>
      <Explanation question={question} />
    </>
  );
}

export default ChoiceQuestion;
