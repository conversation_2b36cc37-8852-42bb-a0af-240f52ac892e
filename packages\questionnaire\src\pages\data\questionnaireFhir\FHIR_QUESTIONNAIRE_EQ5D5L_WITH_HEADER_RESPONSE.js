export const FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER_RESPONSE = {
  resourceType: 'QuestionnaireResponse',
  identifier: [],
  questionnaire: 'e73d02bb-0bae-4300-8da5-e9630c138f2f',
  contained: [],
  status: 'completed',
  authored: '2022-11-04T08:39:25.089Z',
  extension: [
    {
      url: 'QuestionnaireResponse/questionnaire-response-type',
      valueCode: 'instrument-response',
    },
  ],
  item: [
    {
      id: 'group-559217',
      linkId: '1',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      item: [
        {
          id: '559220',
          linkId: '1',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">MOBILITY</span><br></p>',
          answer: [
            {
              valueCoding: {
                id: '559224',
                code: '0',
                display: 'I am unable to walk about',
              },
            },
          ],
        },
      ],
      type: 'group',
    },
    {
      id: 'group-559236',
      linkId: '2',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      item: [
        {
          id: '559239',
          linkId: '1',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">SELF-CARE</span><br></p>',
          answer: [
            {
              valueCoding: {
                id: '559245',
                code: '25',
                display: 'I have severe problems washing or dressing myself',
              },
            },
          ],
        },
      ],
      type: 'group',
    },
    {
      id: 'group-559255',
      linkId: '3',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      item: [
        {
          id: '559258',
          linkId: '1',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">USUAL ACTIVITIES (</span><i style="color: rgb(4, 4, 4); font-size: 15.4px;">e.g. work, study, housework, family or leisure activities</i><span style="color: rgb(4, 4, 4); font-size: 15.4px;">)</span><br></p>',
          answer: [
            {
              valueCoding: {
                id: '559266',
                code: '50',
                display: 'I have moderate problems doing my usual activities',
              },
            },
          ],
        },
      ],
      type: 'group',
    },
    {
      id: 'group-559274',
      linkId: '4',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      item: [
        {
          id: '559277',
          linkId: '1',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">PAIN / DISCOMFORT</span><br></p>',
          answer: [
            {
              valueCoding: {
                id: '559287',
                code: '75',
                display: 'I have slight pain or discomfort',
              },
            },
          ],
        },
      ],
      type: 'group',
    },
    {
      id: 'group-559293',
      linkId: '5',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      item: [
        {
          id: '559296',
          linkId: '1',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">ANXIETY / DEPRESSION</span><br></p>',
          answer: [
            {
              valueCoding: {
                id: '559304',
                code: '50',
                display: 'I am moderately anxious or depressed',
              },
            },
          ],
        },
      ],
      type: 'group',
    },
    {
      id: 'group-559312',
      linkId: '6',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      item: [
        {
          id: '559315',
          linkId: '1',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'YOUR HEALTH TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/display-type',
              valueString: 'numeric-slider',
            },
            {
              url: 'Questionnaire/Item/slider-min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/slider-max-value',
              valueDecimal: 100,
            },
            {
              url: 'Questionnaire/Item/slider-min-label',
              valueString: 'The worst health you can imagine',
            },
            {
              url: 'Questionnaire/Item/slider-max-label',
              valueString: 'The best health you can imagine',
            },
            {
              url: 'Questionnaire/Item/slider-min-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/slider-max-exclusion',
              valueBoolean: false,
            },
          ],
          text: '<ul style="color: rgb(4, 4, 4); font-size: 15.4px;"><li>We would like to know how good or bad your health is TODAY.</li><li>This scale is numbered from 0 to 100.</li><li>100 means the&nbsp;<u>best</u>&nbsp;health you can imagine. 0 means the&nbsp;<u>worst</u>&nbsp;health you can imagine.</li><li>Please click on the scale to indicate how your health is TODAY.</li></ul>',
          answer: [
            {
              valueInteger: 34,
            },
          ],
        },
      ],
      type: 'group',
    },
  ],
};
