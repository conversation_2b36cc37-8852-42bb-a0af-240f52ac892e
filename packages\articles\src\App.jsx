import React from 'react';
import { ArticleEditorKit } from './containers/ArticleEditorKit/index';
import { articleList, articleDetailList } from './data/articles';
import { v4 as uuidV4 } from 'uuid';
import { Box } from '@mui/material';

const downloadFileInJsonFormat = (jsonString, fileName) => {
  const blob = new Blob([jsonString], { type: 'application/json' });
  const href = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = href;
  link.download = fileName + '.json';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};

function App() {
  const handleEditArticle = async (articleId, publishStatus) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const article = articleDetailList.find((article) => article.artifactId === articleId);
        if (!article) {
          reject({ success: false, message: 'Article not found' });
          return;
        }
        resolve({
          success: true,
          message: '',
          article: article,
          publishStatus,
        });
      }, 500);
    });
  };

  const handleViewArticle = async (articleId, publishStatus) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const article = articleDetailList.find((article) => article.artifactId === articleId);
        if (!article) {
          reject({ success: false, message: 'Article not found' });
          return;
        }
        resolve({
          success: true,
          message: '',
          article: article,
          publishStatus,
        });
      }, 500);
    });
  };

  const handleSaveDraft = async (articleDraft, publishStatus) => {
    let newArticleDraft = structuredClone(articleDraft);
    newArticleDraft.artifactId = newArticleDraft.artifactId || uuidV4();
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '',
          article: newArticleDraft,
          publishStatus: 'no',
        });
      }, 500);
    });
  };

  const handlePublish = async (articleId, publishStatus) => {
    console.log('Published ID', articleId);
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const article = articleDetailList.find((article) => article.artifactId === articleId);
        if (!article) {
          reject({ success: false, message: 'Article not found' });
          return;
        }
        const publishedArticle = {
          ...article,
          modifiedDate: new Date().toISOString(),
          publishStatus: 'public',
        };

        resolve({
          success: true,
          message: '',
          article: publishedArticle,
          publishStatus: 'public',
        });
      }, 500);
    });
  };

  const handleDuplicate = (articleId, publishStatus) => {
    const originalArticle = articleDetailList.find((article) => article.artifactId === articleId);
    if (!originalArticle) {
      return Promise.reject({ success: false, message: 'Article not found' });
    }

    const newArtifactId = uuidV4();
    const organizationId = '190e87aa-a3ed-4041-b37d-00e9122aa26f';
    const duplicatedArticle = {
      ...structuredClone(originalArticle),
      artifactId: newArtifactId,
      modifiedDate: new Date().toISOString(),
      bodyKey: `organizations/${organizationId}/articles/${newArtifactId}/article.html`,
      thumbnailKey: `organizations/${organizationId}/articles/${newArtifactId}/thumbnail/${originalArticle.name
        .toLowerCase()
        .replace(/\s+/g, '-')}.png`,
      bodyPresignedUrl: `https://cambian-devorganizationpublished-artifacts.s3.ca-central-1.amazonaws.com/organizations/${organizationId}/articles/${newArtifactId}/article.html`,
      thumbnailPresignedUrl: `https://cambian-devorganizationpublished-artifacts.s3.ca-central-1.amazonaws.com/organizations/${organizationId}/articles/${newArtifactId}/thumbnail/${originalArticle.name
        .toLowerCase()
        .replace(/\s+/g, '-')}.png`,
      publishStatus: 'no',
    };

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          message: '',
          article: duplicatedArticle,
          publishStatus: 'no',
        });
      }, 500);
    });
  };

  const handleImport = () => {
    console.log('handle import');
  };

  const handleDelete = (articleId, publishStatus) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        console.log('Deleting article:', articleId);
        resolve({ success: true, message: '' });
      }, 500);
    });
  };

  const handleExport = (articleId, publishStatus) => {
    const articleJson = articleDetailList.find((article) => article.artifactId === articleId);
    console.log('Exporting article with status:', publishStatus, articleJson);
    downloadFileInJsonFormat(JSON.stringify(articleJson), articleJson.title);
  };

  return (
    <Box sx={{ p: '24px 4%' }}>
      <ArticleEditorKit
        articleList={articleList}
        articleDetailList={articleDetailList}
        onEditArticleCallback={handleEditArticle}
        onViewCallback={handleViewArticle}
        onSaveDraftCallback={handleSaveDraft}
        onPublishCallback={handlePublish}
        onDeleteCallback={handleDelete}
        onDuplicateCallback={handleDuplicate}
        onImportCallback={handleImport}
        onExportCallback={handleExport}
      />
    </Box>
  );
}

export default App;
