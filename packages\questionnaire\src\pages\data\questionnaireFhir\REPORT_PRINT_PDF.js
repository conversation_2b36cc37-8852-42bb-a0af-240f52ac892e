export const REPORT_PRINT_PDF = {
  resourceType: 'Questionnaire',
  date: '2024-07-03T12:31:30.917Z',
  name: 'RegressionTesting',
  title: 'Regression Test',
  description: 'Regression Testing for Questionnaire and Questionnaire Editor',
  subjectType: 'Physician-Site',
  extension: [
    {
      url: 'display-dial',
      valueBoolean: false,
    },
    {
      url: 'display-description',
      valueBoolean: true,
    },
    {
      url: 'display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'display-score',
      valueBoolean: false,
    },
    {
      url: 'display-score-category',
      valueBoolean: false,
    },
    {
      url: 'display-title',
      valueBoolean: true,
    },
    {
      url: 'questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'trendable',
      valueBoolean: false,
    },
    {
      url: 'question-identifier-next-sequence',
      valueInteger: 2,
    },
    {
      url: 'pdftemplate-id',
      valueString: '',
    },
    {
      url: 'question-identifier-prefix',
      valueString: 'Item',
    },
    {
      url: 'question-identifier-next-sequence',
      valueInteger: 5,
    },
    {
      url: 'codeBookHtmlData',
      valueString:
        '\n<h2 style="text-align: center;"><strong>General Data Format</strong></h2>\n<p>The data export file for questionnaire responses has the following characteristics:</p>\n<ol>\n<li data-list-text="1.">\n<p>The file format is &ldquo;csv&rdquo;(machine readable)</p>\n</li>\n<li data-list-text="2.">\n<p>One row for each questionnaire response (Long Format)</p>\n</li>\n<li data-list-text="3.">\n<p>Each row contains the following:</p>\n<ol style="list-style-type: lower-alpha;">\n<li data-list-text="a.">\n<p>An identifier for the questionnaire</p>\n</li>\n<li data-list-text="b.">\n<p>The group id of the group the participant is a member of</p>\n</li>\n<li data-list-text="c.">\n<p>The participant id of the user filling in the response</p>\n</li>\n<li data-list-text="d.">\n<p>The start date (YYYY-MM-DD hh:mm:ss) when the participant begins to work on the response</p>\n</li>\n<li data-list-text="e.">\n<p>The completion date (YYYY-MM-DD hh:mm:ss)</p>\n</li>\n<li data-list-text="f.">\n<p>Time spent in completing questionnaire (in seconds)</p>\n</li>\n<li data-list-text="g.">\n<p>The response for each item (Note: For multiple choice questions that allow multiple responses, each allowable response is turned into a item with the possible response of Yes or No. Free form text responses are put in double quotes (e.g.,”I experienced some pain”) so commas can be used inside the response value. Double quotes are escaped with another double quote (e.g.,”I experienced some “”phantom”” pain”). A skipped item will have the corresponding item response set to -99.)</p>\n</li>\n<li data-list-text="h.">\n<p>The computed scores if applicable (Note: if a score cannot be computed because of missing data, the corresponding field will be set to -99)</p>\n</li>\n</ol>\n</li>\n</ol><h1 style="text-align: center;">{Questionnaire.title}</h1>\n<h3>Questionnaire and Item mapping</h3>\n<p>Each questionnaire is given a unique numerical identifier and each item within a questionnaire is given a name that is used to define the columns used in the data export file.</p>\n<table style="border-collapse: collapse; width: 900px;">\n<tbody>\n<tr>\n<td style="width: 714px; border-style: solid; padding-left: 10px;">\n<p>{Questionnaire.title}</p>\n</td>\n<td style="width: 178px; border-style: solid; padding-left: 10px;">\n<p>{Questionnaire.id}</p>\n</td>\n</tr>\n</tbody>\n</table>\n<p>&nbsp;</p>\n{Questionnaire.mappedQuestionsList}\n<p>&nbsp;</p>\n<p>Each computed score within a questionnaire is given a name that is used to define the columns used in the data export file.</p>\n<table style="border-collapse: collapse; width: 900.359px;">\n<tbody>\n<tr>\n<td style="width: 714px; border-style: solid; padding-left: 10px;">\n<p>score</p>\n</td>\n<td style="width: 178px; border-style: solid; padding-left: 10px;">\n<p>S1</p>\n</td>\n</tr>\n</tbody>\n</table>\n<p>&nbsp;</p>\n<p>With the information above, columns that will be presented in an entry for {Questionnaire.title} are defined as:</p>\n<p>{Questionnaire.questionnaireColumns}</p>\n<p>&nbsp;</p>\n<h3>Item response mapping</h3>\n<p>Allowed responses for each item are shown below:</p>\n<p>&nbsp;</p>\n{Questionnaire.mappedResponseList}\n<p>&nbsp;</p>\n<h3>Sample Data</h3>\n<p>k45e7b06-1295-47f2-9577-d8e4d43c5333,Item1, Item2, Item3, S1</p>\n<p>m5187b06-8321-88i2-2342-h456w234l231,Item1, Item2, Item3, Item4, Item5, Item6, S1</p>\n',
    },
    {
      url: 'htmltemplate-base64',
      valueString:
        '<h1>{Questionnaire.title}</h1>\n<p>Date: {QuestionnaireResponse.completionDate:format(YYYY-MM-DD HH:mm:ss)}</p>\n<p>Here is a list of items and responses:</p>\n{QuestionnaireResponse.variable.item2:round(2)}\n{QuestionnaireResponse.itemsAndResponses}\n<p style="color:gray; font-size:11px;">{Questionnaire.description}</p>',
    },
    {
      url: 'pdftemplate-name',
      valueString: '',
    },
  ],
  identifier: [
    {
      use: 'old',
      system: 'questionnaire/identifier',
      value: 'd3ea065c-2d4f-4d38-b4c7-fc4b28d21f78',
      period: {
        start: '2023-12-13T14:06:06+00:00',
        end: '2023-12-13T14:15:33+00:00',
      },
    },
    {
      use: 'usual',
      system: 'urn:uuid',
      value: 'f6356947-59d8-495b-abf7-47973d3a1968',
      period: {
        start: '2023-12-13T14:06:06+00:00',
      },
    },
  ],
  item: [
    {
      linkId: 'Group1',
      item: [
        {
          id: 'hmQqq7UwLZNgAkmJ9STFYy',
          linkId: 'Item2',
          type: 'integer',
          text: 'Int',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/integer-only',
              valueBoolean: true,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/min-value',
              valueDecimal: '10',
            },
            {
              url: 'Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          required: true,
        },
        {
          id: 'hpnSoUfQ8bST1jwd45YWmG',
          linkId: 'Item1',
          type: 'display',
          text: 'This is the Information Item ',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5542,
            },
            {
              url: 'Item/description',
              valueString: "This is the Information Item's Description",
            },
            {
              url: 'Item/explanation',
              valueString: "This is the Information Item's Explanation(Type is information)",
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
        },
        {
          id: 'oDQehpSv2PCZHrKGabQKz4',
          linkId: 'Item3',
          type: 'decimal',
          text: 'Num',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation ',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/max-value',
              valueDecimal: '10',
            },
            {
              url: 'Item/min-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/max-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          required: true,
          enableWhen: [
            {
              operator: '<',
              answerString: '10',
              question: 'Item2',
            },
          ],
        },
        {
          id: 'gvDcFxY1shE8qZJP2XgDkv',
          linkId: 'Item4',
          type: 'text',
          text: 'para',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Item/min-length',
              valueInteger: 0,
            },
            {
              url: 'Item/max-length',
              valueInteger: 1024,
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Item/description',
              valueString: 'description ',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          required: true,
          enableWhen: [
            {
              operator: '>',
              answerString: '10',
              question: 'Item2',
            },
          ],
        },
        {
          id: '1n9qhF37UcpaTrTf8WesKQ',
          linkId: 'Item5',
          type: 'text',
          text: 'text',
          extension: [
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Item/min-length',
              valueInteger: 0,
            },
            {
              url: 'Item/max-length',
              valueInteger: 255,
            },
            {
              url: 'Item/description',
              valueString: 'if num is less than 10',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 5,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          required: true,
          enableWhen: [
            {
              operator: '<',
              answerString: '10',
              question: 'Item3',
            },
          ],
        },
      ],
      id: 'group-3QaowpQamQjwQp53yt6snz',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
    },
    {
      linkId: 'Group2',
      item: [
        {
          id: 'p6oKoXTqqcAef2EoX1qsVH',
          linkId: 'Item6',
          type: 'choice',
          text: 'checkbox',
          answerOption: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: '10',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: '20',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 2,
                sequence: 3,
                display: '30',
                code: 3,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 3,
                sequence: 4,
                display: '100',
                code: 4,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 4,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:3,question:100',
                  },
                ],
              },
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
        },
        {
          id: 'ikNiDEkbZGj7R7wX5kT41s',
          linkId: 'Item7',
          type: 'choice',
          text: 'dropdown menu',
          answerOption: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: '1',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: '2',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 2,
                sequence: 3,
                display: '3',
                code: 3,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
              },
            },
          ],
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5543,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          required: true,
        },
        {
          id: 'a9L1J1KhRJ6PoZnWRqDUkB',
          linkId: 'Item8',
          type: 'choice',
          text: 'LB',
          answerOption: [
            {
              valueCoding: {
                id: 0,
                sequence: 1,
                display: '1',
                code: 1,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 1,
                sequence: 2,
                display: '2',
                code: 2,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
              },
            },
            {
              valueCoding: {
                id: 2,
                sequence: 3,
                display: '3',
                code: 3,
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
              },
            },
          ],
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/display-large-buttons',
              valueBoolean: true,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'ERROR',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          required: true,
        },
      ],
      id: 'group-pQZUiv4gYwD3u6eC2ddG3p',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 2,
        },
      ],
    },
    {
      linkId: 'Group3',
      item: [
        {
          id: 'aQnnak8fDXECizv35bEkuP',
          linkId: 'Item9',
          type: 'date',
          text: 'date ',
          answerOption: [],
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/description',
              valueString: 'date and time',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 3,
            },
          ],
          required: true,
        },
      ],
      id: 'group-jq5BL9kjjMckYKSQGnH4Ue',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 3,
        },
      ],
    },
    {
      linkId: 'Group4',
      item: [
        {
          id: 'bTL7bL5sAuANwiRkujqbTQ',
          linkId: 'Item10',
          type: 'choice',
          text: 'linear scale ',
          answerOption: [
            {
              valueCoding: {
                id: '1CgYZppHAQ8AaMFZyQj1BZ',
                code: -1,
                display: -1,
              },
            },
            {
              valueCoding: {
                id: 'sCxXAxLNgaZH4kobDkKdjh',
                code: 0,
                display: 0,
              },
            },
            {
              valueCoding: {
                id: 'h2xuLiF7i2vFKiJg6NgEst',
                code: 1,
                display: 1,
              },
            },
          ],
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Item/display-type',
              valueString: 'choice-bar',
            },
            {
              url: 'Item/bar-start-label',
              valueString: '-',
            },
            {
              url: 'Item/bar-end-label',
              valueString: '+',
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'ERROR',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
          ],
          required: true,
        },
        {
          id: 'th9qCfHuU3BXo3xZ7fJpWq',
          linkId: 'Item11',
          type: 'integer',
          text: 'numeric slider',
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5013,
            },
            {
              url: 'Item/display-type',
              valueString: 'numeric-slider',
            },
            {
              url: 'Item/slider-min-value',
              valueDecimal: 5,
            },
            {
              url: 'Item/slider-max-value',
              valueDecimal: 6,
            },
            {
              url: 'Item/slider-min-label',
              valueString: 'min',
            },
            {
              url: 'Item/slider-max-label',
              valueString: 'max',
            },
            {
              url: 'Item/slider-steps',
              valueDecimal: 10,
            },
            {
              url: 'Item/slider-min-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/slider-max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
          ],
          required: true,
        },
        {
          id: 'uaqWSjNeXg1WrV5nT2BjbV',
          linkId: 'Item12',
          type: 'choice',
          text: 'body dig',
          answerOption: [
            {
              valueCoding: {
                id: '574401',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '168,49,168,43,168,39,169,34,171,29,173,26,181,20,193,15,200,14,206,14,214,15,222,18,233,27,237,36,239,41,239,47,239,49,236,51,231,52,226,52,222,53,214,53,207,53,200,53,189,53,182,53,174,52,170,50|594,10,592,10,584,11,580,12,575,14,571,17,567,21,565,23,562,27,560,31,559,34,559,39,558,43,558,49,558,53,558,56,555,57,552,59,552,61,551,65,552,68,553,71,554,73,556,75,557,80,557,82,558,84,561,84,563,85,563,89,565,94,566,97,567,100,567,102,568,105,571,107,575,107,583,107,586,107,593,107,599,107,607,107,616,106,618,101,620,97,622,94,623,89,624,86,625,85,629,83,632,77,633,74,635,70,635,65,636,63,636,60,635,58,633,57,632,57,630,57,630,54,630,38,629,33,628,29,626,26,625,23,622,19,616,17,608,13,604,12',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '1',
                display: 'Head',
              },
            },
            {
              valueCoding: {
                id: '574403',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '168,51,168,59,162,62,161,64,161,66,163,71,164,76,168,82,171,86,175,91,176,95,176,97,177,99,178,100,180,102,182,105,186,108,190,110,193,110,196,111,199,113,202,114,205,114,209,114,210,114,216,112,219,111,223,109,227,107,231,102,233,99,233,97,234,93,235,87,239,85,240,84,242,82,244,80,246,76,247,71,247,69,247,67,247,62,247,61,246,60,245,60,243,59,241,58,239,56,239,51,236,52,234,53,233,53,231,54,228,54,223,54,219,54,211,54,205,55,199,55,192,54,182,54,173,53,170,52',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '2',
                display: 'Face',
              },
            },
            {
              valueCoding: {
                id: '574405',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '183,109,183,114,183,116,182,117,173,128,174,129,176,129,179,130,182,131,186,132,188,133,191,134,195,135,198,136,201,137,204,137,208,136,212,135,220,132,234,128,230,126,229,124,228,123,228,119,228,113,228,109,220,112,216,114,212,116,210,116,208,117,206,117,204,117,201,117,199,116|570,110,571,115,570,119,568,123,566,126,570,129,576,131,587,129,596,129,618,125,623,124,618,118,616,117,615,113,614,108,604,109,595,109,582,110,580,110',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '3',
                display: 'Neck',
              },
            },
            {
              valueCoding: {
                id: '574407',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 4,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '171,130,167,133,164,134,159,135,156,136,154,136,151,136,149,137,146,137,143,138,139,139,135,141,132,144,130,146,128,147,124,152,122,156,120,160,119,163,117,167,116,172,115,181,114,188,113,189,120,191,123,191,129,192,133,192,139,190,164,167,171,160,175,153,179,149,180,146,182,142,184,138,184,135,178,133|625,125,604,129,605,133,608,138,609,142,611,144,615,149,617,152,620,154,622,156,624,158,626,160,627,163,631,164,633,168,637,169,641,172,647,178,651,181,655,183,660,185,666,185,671,185,676,184,680,182,683,181,685,181,684,177,683,172,683,168,681,162,679,157,679,154,677,149,674,146,673,143,669,140,664,139,660,137,656,136,651,135,647,134,642,132,637,131,633,131,630,130',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '4',
                display: 'Shoulder Right',
              },
            },
            {
              valueCoding: {
                id: '574409',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 5,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '238,128,225,132,228,141,232,150,240,156,257,171,276,184,283,184,290,183,295,181,298,179,285,149,280,143,279,142,277,140,269,138,262,137,251,134|562,126,565,129,570,130,572,132,580,132,584,132,582,136,581,139,579,143,577,147,573,151,570,154,566,158,561,161,557,166,551,170,546,173,541,175,536,179,533,182,530,185,523,185,517,185,512,183,508,182,504,180,502,179,504,171,507,163,509,156,511,153,512,149,515,146,519,141,525,138,531,135,537,133,542,132',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '5',
                display: 'Shoulder Left',
              },
            },
            {
              valueCoding: {
                id: '574411',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 6,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '147,184,139,191,136,193,133,193,132,194,129,194,126,194,124,193,122,193,119,193,116,192,114,191,114,211,113,214,112,219,111,221,110,226,109,231,108,236,104,247,103,249,112,245,115,244,119,244,124,247,139,254,139,247,142,239,143,231,145,227,146,223,147,221,149,212,151,211,150,197|658,187,667,187,672,186,675,186,680,184,684,183,685,206,685,211,688,217,689,223,689,228,691,231,692,236,694,240,695,242,683,238,678,237,676,240,672,242,669,246,667,248,663,251,660,252,659,242,657,237,655,233,655,230,653,226,652,221,651,217,651,213,650,209,650,208',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '6',
                display: 'Upper Arm Right',
              },
            },
            {
              valueCoding: {
                id: '574413',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 7,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '271,184,267,220,273,235,275,241,278,250,283,241,291,237,296,236,303,235,309,236,306,214,303,199,298,181,291,184,288,185,282,186,275,186|504,182,500,203,498,207,497,213,496,221,494,229,494,239,504,236,510,238,514,241,520,244,525,247,526,239,529,229,533,223,535,218,530,188,524,187,516,187,512,185,511,185',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '7',
                display: 'Upper Arm Left',
              },
            },
            {
              valueCoding: {
                id: '574415',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 8,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '138,256,127,250,119,247,117,246,114,246,112,246,110,247,103,250,98,259,95,262,91,270,95,274,99,277,103,279,108,280,113,281,119,281,124,281,129,281,138,266,138,263,138,259|660,254,669,249,677,242,682,239,689,241,694,243,695,247,698,251,701,253,705,256,707,261,703,266,700,269,695,273,692,275,688,276,682,277,677,277,672,278,671,277,665,268,662,263',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '8',
                display: 'Elbow Right',
              },
            },
            {
              valueCoding: {
                id: '574417',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 9,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '309,238,294,238,288,240,284,242,283,246,279,250,278,252,279,264,282,269,284,272,287,277,309,269,317,257,312,252,309,249,309,238,308,237|493,241,503,238,508,239,513,242,520,246,525,249,524,261,523,264,521,269,519,274,515,279,503,278,496,277,491,277,489,275,485,272,483,269,481,264,483,258,486,253,488,250,493,247',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '9',
                display: 'Elbow Left',
              },
            },
            {
              valueCoding: {
                id: '574419',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 10,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '90,272,97,279,102,281,108,282,112,283,127,283,114,306,108,315,100,328,78,312,83,300,85,292,87,282,89,276|671,279,688,278,696,275,701,271,705,267,707,263,712,278,712,283,714,288,715,292,716,297,718,299,720,303,714,308,709,311,708,313,703,317,699,320,698,320,692,311,684,302,681,296,679,289',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '10',
                display: 'Forearm Right',
              },
            },
            {
              valueCoding: {
                id: '574421',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 11,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '318,260,312,269,305,273,298,275,290,277,287,278,297,292,305,304,311,311,316,319,320,323,341,308,336,295,334,289,328,277|480,265,485,275,489,277,493,279,498,279,503,280,509,280,513,281,514,281,510,285,505,292,503,297,498,302,494,306,492,311,489,315,487,318,485,320,484,321,461,306,464,301,467,291,470,284,473,278',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '11',
                display: 'Forearm Left',
              },
            },
            {
              valueCoding: {
                id: '574423',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 12,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '78,314,76,320,75,323,73,325,71,327,65,330,67,334,70,336,72,337,73,339,79,341,84,342,89,343,91,343,99,330|720,305,699,321,701,327,703,331,706,334,708,337,726,334,731,331,732,327,733,324,733,322,729,321,726,319,724,314,723,311',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '12',
                display: 'Wrist Right',
              },
            },
            {
              valueCoding: {
                id: '574425',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 13,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '341,310,321,325,331,337,332,340,354,333,355,327,352,325,348,322,345,317|460,308,484,322,479,328,476,331,476,334,476,336,468,337,462,336,454,335,452,333,450,331,450,328,449,325,449,323,453,320,456,316,458,313',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '13',
                display: 'Wrist Left',
              },
            },
            {
              valueCoding: {
                id: '574427',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 14,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '64,332,55,334,52,335,49,336,45,339,41,341,37,343,34,344,33,344,33,347,34,350,36,350,38,351,41,351,45,349,48,349,50,348,52,349,52,353,52,355,49,361,42,368,39,371,37,373,35,376,35,378,35,380,37,380,50,367,51,366,53,366,54,368,54,371,48,382,46,386,46,388,45,390,46,392,47,392,51,389,57,375,59,372,60,371,61,371,62,372,63,375,63,376,62,380,60,387,59,389,58,392,58,394,60,393,62,392,64,388,65,385,66,382,67,380,68,379,68,377,71,377,72,377,72,378,73,380,73,383,72,386,72,389,72,391,74,391,75,390,77,386,77,382,78,380,81,371,83,367,85,363,88,357,89,355,89,353,91,351,90,345,85,345,80,344,75,343,70,341|708,339,727,335,730,334,734,330,735,323,749,328,755,332,759,333,763,335,765,336,765,339,765,342,761,343,757,343,751,343,749,341,746,344,747,346,748,350,751,354,753,356,756,359,759,360,763,366,764,369,764,371,763,372,759,371,757,370,756,367,755,364,753,363,750,362,749,361,748,360,746,359,745,361,745,363,747,367,749,370,751,373,753,376,753,378,754,381,753,384,752,385,749,385,747,381,745,378,743,373,742,370,741,368,740,364,739,363,738,363,736,364,736,367,736,370,738,373,739,377,740,379,741,381,741,383,740,385,740,387,739,387,736,385,735,383,733,378,733,376,732,373,730,371,730,369,729,368,727,369,726,371,727,374,729,381,728,385,727,386,724,386,724,383,721,378,719,372,719,368,718,365,717,363,715,361,713,359,711,356',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '14',
                display: 'Hand Right',
              },
            },
            {
              valueCoding: {
                id: '574429',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 15,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '357,327,356,333,355,335,349,338,343,339,341,340,338,340,332,341,332,347,333,351,334,353,335,356,337,358,339,362,340,363,340,368,340,366,341,387,341,388,342,389,344,388,344,386,345,384,345,381,345,379,345,376,344,374,344,372,344,371,346,369,348,369,350,370,351,372,352,374,353,376,354,378,354,380,354,382,355,387,355,388,355,390,357,390,358,387,358,386,357,381,357,378,357,376,357,375,356,373,355,371,355,370,355,367,355,365,357,364,359,364,361,366,362,367,364,370,366,373,372,384,373,385,374,385,375,384,375,383,375,382,374,381,372,378,371,376,370,374,366,368,366,367,365,364,366,362,367,361,369,360,372,361,374,363,377,365,382,372,384,373,385,373,386,372,386,371,384,370,382,368,378,364,375,360,373,357,371,355,369,352,369,349,370,346,372,345,377,345,379,345,382,346,384,345,385,344,386,343,387,341,385,340,383,339,381,339,379,338,376,337,374,336,372,336,371,335,369,333,369,331,367,330,364,329,362,328,360,327|446,323,448,329,449,333,451,335,455,336,460,338,465,339,475,339,474,347,472,350,470,354,469,357,468,360,467,363,465,366,466,371,465,375,465,380,466,381,464,383,462,384,460,383,459,381,460,377,460,371,460,367,459,364,456,364,456,367,455,369,454,372,454,374,454,377,454,378,453,382,452,387,450,388,447,387,447,380,448,373,449,370,450,366,450,364,448,362,447,362,445,363,442,367,440,371,438,377,435,380,433,382,431,382,430,381,430,378,431,374,433,371,435,368,440,364,439,361,438,359,436,359,432,360,430,363,427,364,426,367,425,369,423,370,422,370,421,369,421,367,421,364,429,355,431,351,432,350,434,347,434,345,434,343,431,342,428,342,423,343,421,343,419,341,419,338,419,336,428,334,430,331,434,327,440,325',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '15',
                display: 'Hand Left',
              },
            },
            {
              valueCoding: {
                id: '574431',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 16,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '186,136,197,139,203,139,210,138,218,135,223,134,225,141,230,151,238,158,246,165,257,173,265,179,268,182,265,218,264,218,262,218,261,227,259,257,258,263,258,271,258,274,259,278,254,278,249,276,244,274,240,272,236,268,234,265,230,259,229,253,227,250,224,245,221,241,218,237,212,234,208,234,204,234,201,236,200,238,198,240,196,244,191,252,186,262,184,266,181,270,178,273,175,275,172,276,167,278,160,278,155,279,158,271,160,264,160,261,158,247,156,229,155,220,152,212,152,199,151,192,150,186,148,184,160,174,170,163,179,153,182,147,185,140',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '16',
                display: 'Chest',
              },
            },
            {
              valueCoding: {
                id: '574433',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 17,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '156,280,153,284,151,290,150,295,150,299,149,303,148,305,148,306,157,307,161,309,166,313,169,315,174,322,177,328,178,333,178,338,177,344,177,347,190,346,195,345,201,340,206,338,210,337,214,338,221,343,224,345,228,346,236,346,240,346,239,342,238,333,240,328,242,323,247,317,253,313,258,308,263,306,266,305,264,294,262,285,261,281,260,280,255,280,249,279,245,278,240,276,234,270,231,266,229,262,228,259,227,254,226,251,224,249,222,245,221,244,219,241,217,239,214,237,211,237,208,237,205,237,203,239,201,241,199,244,198,245,197,248,196,250,194,252,193,254,192,256,190,262,189,264,188,265,187,267,186,269,184,270,182,273,181,274,180,275,178,277,173,278,169,279,167,280,164,281,160,281',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '17',
                display: 'Abdomen',
              },
            },
            {
              valueCoding: {
                id: '574435',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 18,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '180,350,193,348,199,345,205,341,208,340,211,340,213,341,216,341,218,343,221,345,221,347,222,348,237,350,231,355,228,358,226,361,224,363,222,365,221,366,220,368,217,369,215,371,212,371,209,372,207,372,204,370,202,368,200,365,199,364,195,362,193,360,190,358',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '18',
                display: 'Groin',
              },
            },
            {
              valueCoding: {
                id: '574437',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 19,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '147,308,146,320,144,345,144,362,144,375,144,382,144,391,147,390,152,387,158,382,163,376,167,369,171,362,173,356,174,349,175,338,175,332,175,327,169,318,164,314,160,312,156,309,152,308|650,284,643,287,640,290,637,292,636,295,635,298,632,301,630,307,630,311,630,315,632,326,633,333,636,339,640,346,644,353,647,355,650,359,653,360,656,362,658,362,656,322,654,305,652,293',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '19',
                display: 'Hip Right',
              },
            },
            {
              valueCoding: {
                id: '574439',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 20,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '266,307,260,309,254,313,250,317,247,321,244,325,241,330,240,335,240,338,241,344,243,351,245,357,250,365,255,372,259,378,264,383,270,387,275,389,278,389,277,371,276,360,274,345,270,324|538,287,544,291,549,296,551,299,554,304,555,310,555,314,555,319,555,322,555,325,554,328,553,332,551,338,550,342,548,345,545,349,542,355,540,358,537,361,535,363,531,364,529,366,527,367,529,345,530,334,531,325',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '20',
                display: 'Hip Left',
              },
            },
            {
              valueCoding: {
                id: '574441',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 21,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '176,350,205,373,203,377,203,379,203,384,203,389,203,393,202,396,201,402,201,405,201,409,200,413,200,416,200,421,199,431,199,435,198,440,198,446,198,450,197,453,196,457,194,459,194,465,194,468,194,472,193,474,193,475,183,471,180,471,175,470,172,470,169,470,162,470,159,471,156,471,152,472,153,468,154,464,154,461,155,458,154,453,154,449,152,445,151,441,150,438,148,434,148,431,146,424,147,421,146,416,145,410,145,406,144,401,144,398,144,395,142,396,145,396,149,391,156,385,163,382,167,375,171,368,172,365,173,361|594,359,608,354,613,350,619,343,630,323,631,332,632,336,634,340,637,346,641,353,644,355,647,358,651,362,657,364,657,383,657,397,656,404,656,413,655,420,655,427,653,434,651,440,650,444,649,446,649,451,650,458,651,462,651,468,652,473,635,472,630,471,627,473,623,473,620,475,619,477,617,478,614,479,612,480,607,456,606,451,605,448,603,445,601,426,600,403,598,385',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '21',
                display: 'Upper Leg Right',
              },
            },
            {
              valueCoding: {
                id: '574443',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 22,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '239,351,233,355,228,363,222,368,220,370,217,372,220,375,220,378,221,383,221,385,222,390,222,393,224,398,225,401,226,417,227,434,228,442,228,455,230,461,230,468,232,474,244,465,257,466,267,468,272,468,269,460,269,456,269,452,271,446,272,440,274,435,275,431,276,425,277,420,277,411,277,401,277,392,274,392,271,391,268,390,265,388,260,385,256,379,253,374,249,369,246,365,243,360,242,357|527,369,532,366,538,363,542,358,547,351,552,341,557,328,557,323,560,332,565,340,569,346,572,351,575,354,580,356,582,357,585,358,588,359,584,368,584,374,583,379,583,385,582,389,580,392,580,397,579,405,579,408,578,413,578,418,578,422,578,436,577,445,576,450,576,455,574,461,573,469,572,473,570,480,567,477,561,474,556,473,552,473,547,473,542,473,539,473,536,474,533,473,531,473,531,459,532,454,533,451,534,447,532,442,531,437,530,432,527,426,526,410',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '22',
                display: 'Upper Leg Left',
              },
            },
            {
              valueCoding: {
                id: '574445',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 23,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '191,477,181,474,175,473,170,473,163,473,157,474,153,474,152,477,152,481,153,486,152,488,151,491,150,493,149,497,148,501,148,504,148,507,148,509,154,510,158,512,163,515,168,516,171,518,175,519,179,519,184,517,189,516,185,512,184,510,184,507,183,505,184,501,187,495,188,492,190,489,190,487|613,482,622,476,628,474,633,474,639,475,649,475,651,475,655,489,655,497,657,505,648,512,640,514,637,515,633,517,629,516,624,515,622,514,618,512,616,512,613,510,615,502,617,499,617,495,616,491,614,488,613,485',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '23',
                display: 'Knee Right',
              },
            },
            {
              valueCoding: {
                id: '574447',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 24,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '233,476,244,470,250,469,256,469,263,470,272,469,272,477,272,480,272,483,271,486,272,488,272,491,274,494,275,497,276,501,269,503,264,505,262,506,257,508,254,509,249,510,246,510,242,510,239,508,236,507,238,503,238,500,238,498,237,496,236,493,235,487,235,483|531,475,531,480,530,488,528,491,528,493,528,497,527,502,526,507,526,510,539,517,544,519,551,521,555,521,562,520,566,517,571,516,569,499,568,495,567,491,568,487,570,485,570,482,560,475,546,475,540,475',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '24',
                display: 'Knee Left',
              },
            },
            {
              valueCoding: {
                id: '574449',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 25,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '146,511,146,515,145,522,145,527,145,531,144,539,145,545,146,552,147,558,148,563,149,567,150,574,151,580,166,577,171,577,178,578,183,559,185,548,187,539,189,531,189,525,188,520,188,519,183,520,178,521,173,522,167,520,166,519|614,513,614,523,616,532,618,540,619,547,620,551,622,558,623,564,625,568,625,573,625,579,625,580,636,579,645,578,654,578,654,555,656,550,658,543,658,536,659,525,659,517,658,511,658,508,657,507,648,514,643,516,638,517,632,518,627,518,623,517,619,515',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '25',
                display: 'Lower Leg Right',
              },
            },
            {
              valueCoding: {
                id: '574451',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 26,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '237,509,235,517,236,522,235,528,237,535,237,541,238,547,239,552,241,556,245,560,244,565,245,570,246,579,249,579,254,578,259,578,265,578,270,578,272,579,275,579,275,563,277,556,277,551,278,543,280,539,281,535,281,529,281,524,281,521,279,517,278,514,277,502,272,503,269,506,264,507,263,509,257,512,252,512,246,513,242,512|562,579,547,577,533,580,528,540,526,534,524,529,523,526,525,514,526,511,545,522,551,522,556,523,563,521,565,519,571,518,570,527,568,538,566,551,564,552,562,563',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '26',
                display: 'Lower Leg Left',
              },
            },
            {
              valueCoding: {
                id: '574453',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 27,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '178,580,173,579,165,579,160,579,154,580,151,582,151,584,151,589,151,593,151,598,150,602,150,605,154,605,158,605,162,606,166,606,170,606,174,605,178,606,178,603,177,601,177,598|625,581,654,580,654,605,643,606,637,606,633,605,625,605,623,599,624,592,625,587',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '27',
                display: 'Ankle Right',
              },
            },
            {
              valueCoding: {
                id: '574455',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 28,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '274,581,262,580,256,580,250,581,246,581,246,588,246,593,246,596,245,599,244,603,250,603,256,605,264,605,271,605,273,603,273,595|562,581,547,579,534,582,534,604,548,607,564,605,564,598,564,593,562,588,562,587',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '28',
                display: 'Ankle Left',
              },
            },
            {
              valueCoding: {
                id: '574457',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 29,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '151,607,155,607,160,608,164,609,171,609,176,607,179,608,178,612,177,616,176,618,175,620,175,624,175,627,174,631,175,635,175,640,174,642,175,646,175,650,175,653,175,656,175,659,175,663,175,666,171,671,170,675,169,677,164,680,162,682,160,682,159,682,158,680,156,679,154,678,152,678,150,678,146,675,145,673,145,671,142,670,141,667,141,665,141,662,141,660,141,658,142,654,142,652,143,650,145,644,148,632,149,629,149,626|626,606,640,608,645,608,651,607,655,607,658,623,661,631,664,640,664,646,664,652,663,656,662,658,659,662,656,665,652,667,650,669,647,671,641,671,636,667,634,662,632,658,629,656,628,652,628,644,628,638,629,631,628,621,628,615',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '29',
                display: 'Foot Right',
              },
            },
            {
              valueCoding: {
                id: '574459',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 30,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '243,606,248,607,251,607,256,608,260,608,266,609,272,608,272,623,272,628,273,633,274,638,277,642,280,645,280,648,282,654,282,658,283,662,282,666,281,669,279,671,275,674,272,677,270,677,267,679,264,682,263,683,260,684,257,684,256,680,253,677,252,674,250,672,248,671,247,668,246,666,245,664,245,659,246,655,247,651,248,646,247,640,247,637,246,632,246,628,245,622,245,620,246,617,243,615,242,610|563,607,548,609,534,606,534,617,532,623,531,631,528,637,526,640,524,644,524,647,524,653,526,658,530,664,536,668,540,671,545,674,548,674,553,669,556,665,562,654,563,645,561,641,560,638,561,626,562,621,562,618',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '30',
                display: 'Foot Left',
              },
            },
            {
              valueCoding: {
                id: '574461',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 31,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '585,131,603,130,605,136,607,141,609,144,611,147,614,150,616,154,619,156,622,159,623,161,624,163,627,165,629,166,632,169,635,170,638,171,639,174,644,178,648,180,651,182,654,184,656,186,648,190,644,192,638,193,633,195,627,195,623,195,616,196,610,196,604,196,599,196,596,196,588,196,580,196,572,196,566,195,560,195,553,194,548,193,543,192,538,190,534,188,531,186,536,181,540,178,551,172,559,166,565,161,574,153,578,147,581,144',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '31',
                display: 'Upper Back',
              },
            },
            {
              valueCoding: {
                id: '574463',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 32,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '531,189,537,217,543,262,559,266,572,266,593,267,602,267,614,266,622,267,632,266,640,264,643,263,642,253,642,246,643,240,643,235,643,232,643,226,645,223,646,217,647,211,648,207,657,187,648,193,640,195,633,197,624,198,610,199,596,198,587,199,578,198,568,197,559,197,549,195',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '32',
                display: 'Lower Back',
              },
            },
            {
              valueCoding: {
                id: '574465',
                extension: [
                  {
                    url: 'Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 33,
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-shape',
                    valueString: 'poly',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-coordinates',
                    valueString:
                      '542,264,542,272,541,276,539,282,539,285,549,294,555,302,557,307,557,315,557,318,564,335,568,339,572,346,576,350,579,354,583,355,586,356,589,357,597,357,607,352,615,346,621,337,625,329,629,319,628,308,630,302,633,298,636,292,639,289,642,287,647,284,649,282,646,274,645,270,644,267,643,264,635,267,627,269,614,269,613,269,606,269,598,269,585,269,572,269,562,269,555,268',
                  },
                  {
                    url: 'Item/AnswerOption/ValueCoding/image-response-color',
                    valueString: '0',
                  },
                ],
                code: '33',
                display: 'Buttocks',
              },
            },
          ],
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Item/display-type',
              valueString: 'choice-image',
            },
            {
              url: 'Item/image-path',
              valueString: '/api/media/body_diagram.jpg',
            },
            {
              url: 'Item/image-width',
              valueInteger: 795,
            },
            {
              url: 'Item/image-height',
              valueInteger: 698,
            },
            {
              url: 'Item/description',
              valueString: 'description',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'INFO',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
          ],
          required: true,
        },
        {
          id: 'complex-mTzp9th8oLzpyVfAaRr6ta',
          linkId: 'Item13',
          type: 'group',
          text: 'data grid',
          item: [
            {
              id: 'qSNPz8wuqGAQkhb1RAqJQD',
              type: 'group',
              item: [
                {
                  id: 0,
                  linkId: '3CK2SDMmUiKGqGjxGrgxTD',
                  text: 'name',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                  answer: [
                    {
                      valueString: 'fdafa',
                    },
                  ],
                },
              ],
            },
          ],
          extension: [
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/description',
              valueString: 'decription',
            },
            {
              url: 'Item/explanation',
              valueString: 'explanation',
            },
            {
              url: 'Item/explanation-flag',
              valueString: 'WARNING',
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 4,
            },
          ],
        },
      ],
      id: 'group-1e9Yagfcs3ejzsXYERWips',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 4,
        },
      ],
    },
  ],
  publisher: 'App-Scoop',
  status: 'final',
  id: '3e32b86f-4eb5-4740-88e0-ebc851954003',
};
