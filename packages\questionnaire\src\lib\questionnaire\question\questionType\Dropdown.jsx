import React from 'react';
import { Box, FormControl, Select, MenuItem, Grid } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';

function Dropdown(props) {
  const { question, handleQuestionResponse } = props;
  const [isReadOnly, setIsReadOnly] = React.useState(() => handleQuestionResponse === undefined);

  const extractExistingAnswer = () => {
    let selectedAnswerId = question.answer.valueCoding === undefined ? '' : question.answer.valueCoding.id;
    let answer = '';

    let answerElement = question.question.answerOption.find((option) => option.valueCoding.id === selectedAnswerId);

    if (answerElement) {
      answer = answerElement.valueCoding.display;
    }
    return answer;
  };

  const [answer, setAnswer] = React.useState(extractExistingAnswer());

  const handleOnChange = (event, param) => {
    const selectedValue = event.target.value;

    if (selectedValue === 'none') {
      setAnswer('');
      if (!isReadOnly) {
        question.answer = {};
        handleQuestionResponse(question);
      }
      return;
    }

    setAnswer(selectedValue);
    if (!isReadOnly) {
      let newResponse = null;
      if (selectedValue) {
        newResponse = selectedValue;
      }

      let selectedOption = {};
      question.question.answerOption.forEach((element, index) => {
        if (Number(element.valueCoding.id) === Number(newResponse)) {
          selectedOption = element;
        }
      });
      setAnswer(selectedOption.valueCoding.display);

      question.answer = selectedOption;
      handleQuestionResponse(question);
    }
  };
  const hasSelection = answer !== '';

  return (
    <Box>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <Grid container>
        <Grid item xs={12} sm={7}>
          <FormControl fullWidth size="small">
            <Select
              size="small"
              value={answer}
              onChange={(event, param) => handleOnChange(event, param)}
              displayEmpty
              renderValue={(selected) => {
                if (typeof selected === 'undefined' || selected.length === 0) {
                  return 'Select an option';
                }
                return selected;
              }}
            >
              {hasSelection && (
                <MenuItem value="none" sx={{ fontStyle: 'italic' }}>
                  None
                </MenuItem>
              )}
              {question.question.answerOption.map((item) => (
                <MenuItem key={item.valueCoding.id} value={item.valueCoding.id}>
                  {item.valueCoding.display}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      <Explanation question={question} />
    </Box>
  );
}

export { Dropdown };
