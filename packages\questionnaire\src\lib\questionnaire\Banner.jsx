import React from 'react';
import { Box, Link, Grid, Typography, IconButton, Collapse, Checkbox, FormControlLabel } from '@mui/material';
import { CANNOT_LOCATE_STRING } from '../../Common/constants';
import dataValidation from '../../Common/Utils/dataValidation';
import { buildBannerWithTemplate } from './utility/htmlReportUtility';

function Banner(props) {
  const { demographic, organization, bannerTemplate } = props;
  debugger;
  return (
    <React.Fragment>
      <Box
        id="banner"
        sx={{
          p: 5,
          '@page': { margin: '.4in 0in !important' },
          '@media print': {
            '@page': {
              margin: '14mm',
            },
          },
        }}
      >
        <div dangerouslySetInnerHTML={{ __html: buildBannerWithTemplate(demographic, organization, bannerTemplate) }} />
      </Box>
    </React.Fragment>
  );
}

export { Banner };
