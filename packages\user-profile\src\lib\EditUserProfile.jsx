import React, { useEffect, useState, useRef } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
  Skeleton,
  Tab,
  Tabs,
  Typography,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { ThemeProvider } from '@mui/material/styles';
import { CambianTheme } from '@cambianrepo/ui';
import { useTheme } from '@mui/material/styles';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import NamePanel from './NamePanel';
import DemographicsPanel from './DemographicsPanel';
import ContactPanel from './ContactPanel';
import AddressPanel from './AddressPanel';
import IdentificationPanel from './IdentificationPanel';
function EditUserProfile(props) {
  const theme = useTheme();
  const {
    visibleTabs,
    nameTabConfig,
    singleContactInfo,
    userDetails,
    updateUserDetails,
    deleteAddress,
    deleteHealthCareId,
    updateIcon,
    icon,
    sendOtp,
    verifyOtp,
    countriesAndProvinces,
    idTypesAndIssuers,
  } = props;
  const [userDetailsData, setUserDetailsData] = useState(() => {
    if (props.singleContactInfo) {
      return {
        ...userDetails,
        emailAddress: userDetails.emailAddress || '',
        phoneNumber: userDetails.phoneNumber || '',
      };
    }
    return userDetails;
  });

  const handleUpdateUserDetails = (data) => {
    updateUserDetails(data);
  };

  const handleDeleteAddress = (data) => {
    deleteAddress(data);
  };

  const handleDeleteHealthCareId = (data) => {
    deleteHealthCareId(data);
  };

  const handleUpdateIcon = (data) => {
    updateIcon(data);
  };

  const handleSendOtp = (data) => {
    sendOtp(data);
  };

  const handleVerifyOtp = (data, onSuccess) => {
    verifyOtp(data, (otpResult) => {
      onSuccess(otpResult);
    });
  };

  if (!userDetailsData) {
    return (
      <Box
        sx={{
          height: '100%',
          width: '100%',
          overflowX: 'hidden',
        }}
      >
        <Grid container direction="row" sx={{ justifyContent: 'center' }}>
          <Box component={Grid} item xs={12} sm={10} md={12}>
            <Skeleton
              variant="rounded"
              height={500}
              sx={{ mt: '25px', width: '100%', marginLeft: 3, marginRight: 3 }}
            />
          </Box>
        </Grid>
      </Box>
    );
  }

  // Render user profile details
  return (
    <Box
      sx={{
        height: '100%',
        width: '100%',
        overflowX: 'hidden',
      }}
    >
      <Grid container direction="row">
        <Box component={Grid} item xs={12} sm={10} md={12}>
          <BasicTabs
            visibleTabs={visibleTabs}
            nameTabConfig={nameTabConfig}
            singleContactInfo={singleContactInfo}
            userDetails={userDetailsData}
            handleUpdateUserDetails={handleUpdateUserDetails}
            handleDeleteAddress={handleDeleteAddress}
            handleDeleteHealthCareId={handleDeleteHealthCareId}
            handleUpdateIcon={handleUpdateIcon}
            icon={icon}
            handleSendOtp={handleSendOtp}
            handleVerifyOtp={handleVerifyOtp}
            countriesAndProvinces={countriesAndProvinces}
            idTypesAndIssuers={idTypesAndIssuers}
          />
        </Box>
      </Grid>
    </Box>
  );
}

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`vertical-tabpanel-${index}`}
      aria-labelledby={`vertical-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired,
};

function a11yProps(index) {
  return {
    id: `vertical-tab-${index}`,
    'aria-controls': `vertical-tabpanel-${index}`,
  };
}

const today = new Date(Date.now());

function getBirthMonth(date) {
  return date.getMonth() + 1;
}

function getBirthDay(date) {
  return date.getDate();
}

function getBirthYear(date) {
  return date.getFullYear();
}

const emptyToNull = (value) => (value === '' ? null : value);

const isValidEmail = (email) => {
  const EMAIL_REGEX_PATTERN = new RegExp('^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$');
  return EMAIL_REGEX_PATTERN.test(email);
};

const isValidPhoneNumber = (phoneNumber) => {
  const MOBILE_REGEX_PATTERN = new RegExp('^(\\+\\d{1,2}\\s?)?\\(?\\d{3}\\)?[\\s.-]?\\d{3}[\\s.-]?\\d{4}$');
  return MOBILE_REGEX_PATTERN.test(phoneNumber);
};

const applyEmptyToNullToObject = (obj) => {
  if (typeof obj === 'object' && obj !== null) {
    for (let key in obj) {
      obj[key] = emptyToNull(obj[key]);
      if (typeof obj[key] === 'object') {
        obj[key] = applyEmptyToNullToObject(obj[key]);
      }
    }
  }
  return obj;
};

const formatUserDetailsData = (userDetailsData) => {
  return {
    ...userDetailsData,
    middleName: emptyToNull(userDetailsData?.middleName),
    gender: emptyToNull(userDetailsData?.gender),
    emailAddresses: (userDetailsData?.emailAddresses || []).filter((email) => email.emailAddress !== ''),
    phoneNumbers: (userDetailsData?.phoneNumbers || []).filter((phone) => phone.phoneNumber !== ''),
    addresses: (userDetailsData?.addresses || [])
      .filter(
        (address) =>
          address.address1 !== '' ||
          address.address2 !== '' ||
          address.city !== '' ||
          address.province !== '' ||
          address.postalCode !== '' ||
          address.country !== '',
      )
      .map((address) => applyEmptyToNullToObject(address)),
    healthCareIds: (userDetailsData?.healthCareIds || [])
      .filter((healthCareId) => healthCareId.type !== '' || healthCareId.issuer !== '' || healthCareId.value !== '')
      .map((healthCareId) => applyEmptyToNullToObject(healthCareId)),
    preferredContactMechanism: emptyToNull(userDetailsData?.preferredContactMechanism),
  };
};

// Deep equality check function
const deepEqual = (obj1, obj2) => {
  if (obj1 === obj2) return true;
  if (typeof obj1 !== 'object' || obj1 === null || typeof obj2 !== 'object' || obj2 === null) {
    return false;
  }

  let keys1 = Object.keys(obj1);
  let keys2 = Object.keys(obj2);
  if (keys1.length !== keys2.length) return false;

  for (let key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }

  return true;
};

const ButtonSection = (props) => {
  const {
    firstValid,
    lastValid,
    emailValid,
    isUpdated,
    isInvalidDateOfBirth,
    isIconChanged,
    saveUserProfile,
    nameTabConfig,
    isFormDirty,
    userDetailsData,
    deepCopyUserDetails,
  } = props;

  const formattedUserDetailsData = formatUserDetailsData(userDetailsData);
  const isEmailValidForEach = (emailAddresses) =>
    emailAddresses.every((email) => email.emailAddress && isValidEmail(email.emailAddress));
  const isPhoneNumberValidForEach = (phoneNumbers) =>
    phoneNumbers.every((phone) => phone.phoneNumber && isValidPhoneNumber(phone.phoneNumber));

  return (
    <Button
      sx={{ marginTop: 2 }}
      disabled={
        !nameTabConfig
          ? !isUpdated ||
            !firstValid ||
            !lastValid ||
            isInvalidDateOfBirth ||
            !isEmailValidForEach(formattedUserDetailsData.emailAddresses) ||
            !isPhoneNumberValidForEach(formattedUserDetailsData.phoneNumbers) ||
            (deepEqual(formattedUserDetailsData, deepCopyUserDetails) && !isIconChanged)
          : !isFormDirty() || !firstValid || !lastValid || !emailValid
      }
      variant="contained"
      onClick={() => saveUserProfile()}
    >
      Save
    </Button>
  );
};

function BasicTabs(props) {
  const { t } = useTranslation();
  const theme = useTheme();
  const [isProfileDataChanged, setProfileDataChanged] = React.useState(false);
  const [profileChangeSuccessful, setProfileChangeSuccessful] = React.useState();
  const [emailInUse, setEmailInUse] = React.useState();
  const [value, setValue] = React.useState(0);
  const [isUpdated, setIsUpdated] = React.useState(false);
  const [userDetailsData, setUserDetailsData] = React.useState(props.userDetails);
  const [originalUserDetails, setOriginalUserDetails] = React.useState({
    ...props.userDetails,
  });
  const [deepCopyUserDetails, setDeepCopyUserDetails] = React.useState(
    JSON.parse(JSON.stringify(formatUserDetailsData(props.userDetails))),
  );
  const [originalIcon, setOriginalIcon] = React.useState(props.icon || null);
  const [updatedIcon, setUpdatedIcon] = React.useState(props.icon || null);

  React.useEffect(() => {
    setUserDetailsData(props.userDetails);
    setOriginalUserDetails({
      ...props.userDetails,
    });
    setDeepCopyUserDetails(JSON.parse(JSON.stringify(formatUserDetailsData(props.userDetails))));
  }, [props.userDetails]);

  React.useEffect(() => {
    setOriginalIcon(props.icon);
    setUpdatedIcon(props.icon);
  }, [props.icon]);

  // Validation states
  const [isFirstNameValid, setIsFirstNameValid] = React.useState(true);
  const [isLastNameValid, setIsLastNameValid] = React.useState(true);
  const [isEmailValid, setIsEmailValid] = React.useState(true);

  // Update validation states when userDetailsData changes
  React.useEffect(() => {
    setIsFirstNameValid(userDetailsData?.firstName && userDetailsData?.firstName.length > 0);
    setIsLastNameValid(userDetailsData?.lastName && userDetailsData?.lastName.length > 0);
    setIsEmailValid(
      userDetailsData?.emailAddress &&
        userDetailsData?.emailAddress.length > 0 &&
        isValidEmail(userDetailsData?.emailAddress),
    );
  }, [userDetailsData]);

  const isIconChanged =
    updatedIcon !== undefined &&
    (updatedIcon === null ? originalIcon !== null && originalIcon.iconUrl !== null : updatedIcon !== originalIcon);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const updateContactDataCallback = (type, operation, key, value) => {
    if (props.singleContactInfo) {
      if (key === 'emailAddress') {
        setIsEmailValid(value.length > 0 && EMAIL_REGEX_PATTERN.test(value));
        const updatedData = {
          ...userDetailsData,
          emailAddress: value,
        };
        setUserDetailsData(updatedData);
      } else if (key === 'phoneNumber') {
        const updatedData = {
          ...userDetailsData,
          phoneNumber: value,
        };
        setUserDetailsData(updatedData);
      }
    } else {
      if (type === 'property') {
        if (key === 'preferredContactMechanism') {
          const updatedPreferences =
            value === 'Email'
              ? {
                  sendEmail: true,
                  sendSms: false,
                  notificationFrequency: userDetailsData.communicationPreferences.notificationFrequency,
                }
              : value === 'Phone'
                ? {
                    sendEmail: false,
                    sendSms: true,
                    notificationFrequency: userDetailsData.communicationPreferences.notificationFrequency,
                  }
                : {
                    sendEmail: false,
                    sendSms: false,
                    notificationFrequency: userDetailsData.communicationPreferences.notificationFrequency,
                  };
          const updatedData = {
            ...userDetailsData,
            preferredContactMechanism: value,
            communicationPreferences: updatedPreferences,
          };
          setUserDetailsData(updatedData);
        } else if (key === 'notificationFrequency') {
          const updatedData = {
            ...userDetailsData,
            communicationPreferences: {
              ...userDetailsData.communicationPreferences,
              notificationFrequency: value,
            },
          };
          setUserDetailsData(updatedData);
        } else if (key === 'subscribeToNotifications') {
          const updatedData = {
            ...userDetailsData,
            subscribeToNotifications: value,
          };
          setUserDetailsData(updatedData);
        } else if (key === 'sendEmailAddressOTP') {
          props.handleSendOtp({
            emailAddress: value,
          });
        } else if (key === 'sendPhoneNumberOTP') {
          props.handleSendOtp({
            phoneNumber: value,
          });
        }
      } else if (type === 'emailList') {
        if (operation === 'update') {
          if (Array.isArray(value)) {
            const updatedData = {
              ...userDetailsData,
              emailAddresses: value,
            };
            setUserDetailsData(updatedData);
          } else {
            console.warn('Value is not an array. Update not performed.');
          }
        } else if (operation === 'delete') {
          if (value < userDetailsData.emailAddresses.length) {
            const updatedEmailList = userDetailsData.emailAddresses?.filter((_, i) => i !== value);
            const updatedData = {
              ...userDetailsData,
              emailAddresses: updatedEmailList,
            };
            setUserDetailsData(updatedData);
            setProfileDataChanged(true);
          }
        }
      } else if (type === 'phoneList') {
        if (operation === 'update') {
          if (Array.isArray(value)) {
            const updatedData = {
              ...userDetailsData,
              phoneNumbers: value,
            };
            setUserDetailsData(updatedData);
          } else {
            console.warn('Value is not an array. Update not performed.');
          }
        } else if (operation === 'delete') {
          if (value < userDetailsData.phoneNumbers.length) {
            const updatedPhoneList = userDetailsData.phoneNumbers?.filter((_, i) => i !== value);
            const updatedData = {
              ...userDetailsData,
              phoneNumbers: updatedPhoneList,
            };
            setUserDetailsData(updatedData);
            setProfileDataChanged(true);
          }
        }
      }
    }
    setIsUpdated(true);
  };

  // this callback processes differently from contact details callback because changes are not saved until user explicitly clicks save
  const updateProfileDataCallback = (type, operation, key, value) => {
    if (type === 'property') {
      if (key === 'firstName') {
        setIsFirstNameValid(value.length > 0);
        const updatedData = {
          ...userDetailsData,
          firstName: value,
        };
        setUserDetailsData(updatedData);
      }

      if (key === 'middleName') {
        const updatedData = {
          ...userDetailsData,
          middleName: value,
        };
        setUserDetailsData(updatedData);
      }

      if (key === 'lastName') {
        setIsLastNameValid(value.length > 0);
        const updatedData = {
          ...userDetailsData,
          lastName: value,
        };
        setUserDetailsData(updatedData);
      }

      if (key === 'dateOfBirth') {
        const updatedData = {
          ...userDetailsData,
          dateOfBirth: value,
        };
        setUserDetailsData(updatedData);
      }

      if (key === 'gender') {
        const updatedData = {
          ...userDetailsData,
          gender: value?.toUpperCase(),
        };
        setUserDetailsData(updatedData);
      }

      if (key === 'address') {
        const updatedData = { ...userDetailsData };
        const addresses = updatedData.addresses || [];

        if (operation === 'update') {
          if (Array.isArray(value)) {
            updatedData.addresses = value;
            setUserDetailsData(updatedData);
          } else {
            console.warn('Value is not an array. Update not performed.');
          }
        } else if (operation === 'delete') {
          if (value < addresses.length) {
            props.handleDeleteAddress(addresses[value].id);
            updatedData.addresses = addresses?.filter((address) => address.id !== addresses[value].id);
            setUserDetailsData(updatedData);
          }
        }

        setProfileDataChanged(true);
      }

      if (key === 'healthCareIds') {
        const updatedData = { ...userDetailsData };
        const healthCareIdList = updatedData.healthCareIds || [];

        if (operation === 'update') {
          if (Array.isArray(value)) {
            updatedData.healthCareIds = value;
            setUserDetailsData(updatedData);
          } else {
            console.warn('Value is not an array. Update not performed.');
          }
        } else if (operation === 'delete') {
          if (value < healthCareIdList.length) {
            props.handleDeleteHealthCareId(healthCareIdList[value].id);
            updatedData.healthCareIds = healthCareIdList?.filter(
              (healthCareId) => healthCareId.id !== healthCareIdList[value].id,
            );
            setUserDetailsData(updatedData);
          }
        }

        setProfileDataChanged(true);
      }

      if (type === 'property' && key === 'photoImage') {
        setUpdatedIcon(value);
      }
    }
    setIsUpdated(true);
  };

  var todayDate = new Date(getBirthYear(today), getBirthMonth(today) - 1, getBirthDay(today), 0, 0, 0, 0, 0);
  var currentBirthDate = new Date(userDetailsData?.dateOfBirth);
  const isValid = dayjs(currentBirthDate).isValid();

  const handleSave = (data) => {
    // updatedIcon can hold null value when icon is removed
    if (updatedIcon !== undefined && updatedIcon !== originalIcon) {
      props.handleUpdateIcon(updatedIcon);
      setOriginalIcon(updatedIcon);
    }
    setUserDetailsData(data);
    setOriginalUserDetails({
      ...data,
    });
    const formattedData = formatUserDetailsData(data);
    setDeepCopyUserDetails(JSON.parse(JSON.stringify(formattedData)));
    props.handleUpdateUserDetails(data);
    setIsUpdated(false);
  };
  const handleCloseChangeResultModal = () => {
    setProfileDataChanged(false);
  };

  const tabConfig = [
    { label: 'Name', component: NamePanel, key: 'name' },
    { label: 'Demographics', component: DemographicsPanel, key: 'demographics' },
    { label: 'Contact', component: ContactPanel, key: 'contact' },
    { label: 'Address', component: AddressPanel, key: 'address' },
    { label: 'Identification', component: IdentificationPanel, key: 'identification' },
  ];

  const filteredTabs = props.visibleTabs ? tabConfig.filter((tab) => props.visibleTabs.includes(tab.key)) : tabConfig;

  const isFormDirty = () => {
    if (props.nameTabConfig) {
      return (
        userDetailsData.firstName !== originalUserDetails.firstName ||
        userDetailsData.lastName !== originalUserDetails.lastName ||
        userDetailsData.phoneNumber !== originalUserDetails.phoneNumber ||
        userDetailsData.emailAddress !== originalUserDetails.emailAddress ||
        isIconChanged
      );
    }
  };

  const boxRef = useRef();
  if (boxRef.current) {
    boxRef.current.style.padding = '16px';
  }

  return (
    <Box>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={value}
          onChange={handleChange}
          aria-label="basic tabs example"
          variant="scrollable"
          scrollButtons
          allowScrollButtonsMobile
          sx={{
            '& .MuiTabs-scrollButtons': {
              width: '16px',
            },
          }}
        >
          {filteredTabs.map((tab, index) => (
            <Tab
              key={tab.key}
              label={<Typography sx={{ fontSize: { xs: 15, md: 16 }, fontWeight: 500 }}>{t(tab.label)}</Typography>}
              {...a11yProps(index)}
            />
          ))}
        </Tabs>
      </Box>

      <ThemeProvider theme={CambianTheme}>
        <Box
          ref={boxRef}
          sx={{
            '& .MuiBox-root': {
              padding: '0px',
            },
          }}
        >
          {filteredTabs.map((tab, index) => (
            <TabPanel key={tab.key} value={value} index={index}>
              {tab.key === 'name' && (
                <NamePanel
                  nameTabConfig={props.nameTabConfig}
                  userDetail={userDetailsData}
                  icon={updatedIcon}
                  updateProfileDataCallback={updateProfileDataCallback}
                />
              )}
              {tab.key === 'demographics' && (
                <DemographicsPanel userDetail={userDetailsData} updateProfileDataCallback={updateProfileDataCallback} />
              )}
              {tab.key === 'contact' && (
                <ContactPanel
                  singleContactInfo={props.singleContactInfo}
                  userDetail={userDetailsData}
                  updateProfileDataCallback={updateContactDataCallback}
                  handleVerifyOtp={props.handleVerifyOtp}
                />
              )}
              {tab.key === 'address' && (
                <AddressPanel
                  userDetail={userDetailsData}
                  countriesAndProvinces={props.countriesAndProvinces}
                  updateProfileDataCallback={updateProfileDataCallback}
                />
              )}
              {tab.key === 'identification' && (
                <IdentificationPanel
                  userDetail={userDetailsData}
                  idTypesAndIssuers={props.idTypesAndIssuers}
                  updateProfileDataCallback={updateProfileDataCallback}
                />
              )}
            </TabPanel>
          ))}
          <ButtonSection
            firstValid={isFirstNameValid}
            lastValid={isLastNameValid}
            emailValid={isEmailValid}
            isUpdated={isUpdated}
            isInvalidDateOfBirth={!isValid || currentBirthDate.getTime() > todayDate.getTime()}
            isIconChanged={isIconChanged}
            saveUserProfile={() => handleSave(userDetailsData)}
            nameTabConfig={props.nameTabConfig}
            isFormDirty={isFormDirty}
            userDetailsData={userDetailsData}
            deepCopyUserDetails={deepCopyUserDetails}
          />
        </Box>
      </ThemeProvider>

      <Dialog
        open={isProfileDataChanged && profileChangeSuccessful === false && emailInUse === false}
        onClose={handleCloseChangeResultModal}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">{t('Profile Update Unsuccessful')}</DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            {t('Profile was not updated.  Please try again or contact support.')}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button variant="contained" onClick={() => handleCloseChangeResultModal()}>
            {t('OK')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

EditUserProfile.propTypes = {
  userDetails: PropTypes.object.isRequired,
  updateUserDetails: PropTypes.func,
  deleteAddress: PropTypes.func,
  deleteHealthCareId: PropTypes.func,
  updateIcon: PropTypes.func,
  icon: PropTypes.object,
  sendOtp: PropTypes.func,
  verifyOtp: PropTypes.func,
  countriesAndProvinces: PropTypes.object.isRequired,
  idTypesAndIssuers: PropTypes.object.isRequired,
};

export { EditUserProfile };
