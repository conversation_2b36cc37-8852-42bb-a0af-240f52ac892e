import React from 'react';
import { useController } from 'react-hook-form';
import UploadIcon from '@mui/icons-material/Upload';
import CloseIcon from '@mui/icons-material/Close';
import { Stack, Button, IconButton, TextField, Link, Typography, Tooltip, FormHelperText, Box } from '@mui/material';

function FileInput({ inputName, typographyProps, buttonProps, stackProps, useControllerProps, accept, formContext }) {
  const { control, setValue } = formContext;
  const {
    field: { value, onChange: fieldOnChange },
    fieldState: { error },
  } = useController({
    ...useControllerProps,
    name: inputName,
    control,
  });

  return (
    <>
      <Box
        className="fileinput"
        sx={{
          border: error ? '1px solid #d32f2f' : '1px solid rgba(0, 0, 0, 0.23)',
          borderRadius: '4px',
          maxWidth: '500px',
          padding: '8px',
          '&:hover': {
            borderColor: error ? '#d32f2f' : 'rgba(0, 0, 0, 0.87)',
          },
        }}
      >
        <Stack direction="row" spacing={1} alignItems="center" justifyContent="space-between" {...stackProps}>
          <Typography sx={{ marginRight: '10px', color: value ? 'text.primary' : (theme) => theme.palette.grey[500] }}>
            {typographyProps.text}
          </Typography>
          {value ? (
            <>
              <div style={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
                {accept.includes('image') && (
                  <img
                    alt="Org Icon Logo"
                    width="60"
                    height="60"
                    style={{
                      border: '0.1px solid lightgray',
                      marginRight: '10px',
                      objectFit: 'cover',
                    }}
                    src={typeof value === 'string' ? value : URL.createObjectURL(value)}
                  />
                )}
                <Link
                  variant="caption"
                  href={typeof value === 'string' ? value : URL.createObjectURL(value)}
                  target="_blank"
                  fontSize={16}
                >
                  {value.name || 'click to view current file'}
                </Link>
              </div>
              <Tooltip title="Remove current file" placement="left">
                <IconButton
                  aria-label="close"
                  onClick={() => setValue(inputName, null, { shouldDirty: true })}
                  sx={{
                    color: (theme) => theme.palette.grey[500],
                  }}
                >
                  <CloseIcon sx={{ cursor: 'pointer' }} />
                </IconButton>
              </Tooltip>
            </>
          ) : (
            <Button variant="outlined" component="label" {...buttonProps}>
              <TextField
                inputProps={{ accept, type: 'file' }}
                sx={{ visibility: 'hidden', display: 'none' }}
                onChange={(e) => fieldOnChange(e.target.files[0])}
              />
              <UploadIcon />
            </Button>
          )}
        </Stack>
      </Box>
      {error && <FormHelperText error>{error.message}</FormHelperText>}
    </>
  );
}

export default FileInput;
