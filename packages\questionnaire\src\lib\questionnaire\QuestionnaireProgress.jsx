import React from 'react';
import LinearProgress from '@mui/material/LinearProgress';
import { Grid, Typography } from '@mui/material';

function QuestionnaireProgress(props) {
  const { progress, isStatusEnabled, isProgressPercentageEnabled = true } = props;
  const progressPercentage = Math.round(progress);

  return (
    <React.Fragment>
      {isStatusEnabled && (
        <Grid container columnGap={{ xs: 1, sm: 0.2 }} alignItems="center" justifyContent="flex-start">
          <Grid item xs={isProgressPercentageEnabled ? 10 : 12} sm={isProgressPercentageEnabled ? 10.8 : 12}>
            <LinearProgress sx={{ height: '5px' }} variant="determinate" value={progress} />
          </Grid>
          {isProgressPercentageEnabled && (
            <Grid item xs={1} sm={1} sx={{ textAlign: { sm: 'right' } }}>
              <Typography sx={{ fontSize: '0.7rem' }}>
                {!isNaN(progressPercentage) && `${progressPercentage}%`}
              </Typography>
            </Grid>
          )}
        </Grid>
      )}
    </React.Fragment>
  );
}

export { QuestionnaireProgress };
