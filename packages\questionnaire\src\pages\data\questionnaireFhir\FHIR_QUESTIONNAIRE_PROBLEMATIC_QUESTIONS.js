export const FHIR_QUESTIONNAIRE_PROBLEMATIC_QUESTIONS = {
  resourceType: 'Questionnaire',
  id: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
  extension: [
    {
      url: 'Questionnaire/display-dial',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-description',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-score',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-score-category',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-title',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'Questionnaire/question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/trendable',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 0,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: '_mo45',
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 1,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: '_sc45',
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 2,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: '_ua45',
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 3,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: '_pd45',
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 4,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: '_ad45',
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 5,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: '_num45',
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 6,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'Score',
        },
      ],
    },
  ],
  identifier: [
    {
      system: 'urn:uuid',
      value: '1ea56833-a9a2-4fb8-b578-cd45fe2aec94',
    },
  ],
  name: 'Other Questions',
  title: 'Other Questions',
  status: 'active',
  date: '2021-11-05T22:25:41-06:00',
  publisher: 'qnadmintool',
  description:
    'Other Questions Health Questionnaire<br/>English version for Canada<br/><br/>Copyright&copy; EuroQol Research Foundation. EQ-5D<sup>TM</sup> is a trade mark of the EuroQol Research Foundation',
  item: [
    {
      id: '527754',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
      ],
      linkId: '3',
      text: 'Have you ever been rushed to the hospital in an ambulance? If Yes, do you remember why?',
      type: 'text',
      required: false,
    },

    {
      id: '538185',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'test ',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
      ],
      linkId: '4',
      text: 'Date and Type Question',
      type: 'dateTime',
      required: false,
    },

    {
      id: '538188',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
      ],
      linkId: '5',
      text: 'Only Date Question',
      type: 'date',
      required: false,
    },
    {
      id: '546452',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/trendable',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/display-type',
          valueString: 'numeric-slider',
        },
        {
          url: 'Questionnaire/Item/slider-min-value',
          valueDecimal: 0.0,
        },
        {
          url: 'Questionnaire/Item/slider-max-value',
          valueDecimal: 100.0,
        },
        {
          url: 'Questionnaire/Item/slider-min-label',
          valueString: 'min',
        },
        {
          url: 'Questionnaire/Item/slider-max-label',
          valueString: 'max',
        },
        {
          url: 'Questionnaire/Item/slider-min-exclusion',
          valueBoolean: false,
        },
        {
          url: 'Questionnaire/Item/slider-max-exclusion',
          valueBoolean: false,
        },
      ],
      linkId: '7',
      text: 'Numeric slider question type',
      type: 'integer',
      required: false,
    },
  ],
};
