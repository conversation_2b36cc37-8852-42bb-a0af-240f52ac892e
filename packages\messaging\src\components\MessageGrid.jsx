import React, { useMemo } from 'react';
import { DataGrid } from '@mui/x-data-grid';
import { Box, IconButton, Menu, MenuItem, Alert, CircularProgress } from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import dayjs from 'dayjs';

export const MessageGrid = ({
  type = 'inbox',
  messages,
  onMessageClick,
  selectedMessageId,
  isLoading,
  error,
  sortModel,
  onSortModelChange,
  searchText,
  pageSize = 5,
  onPageSizeChange,
  pageSizeOptions = [5, 10, 25],
}) => {
  const [anchorEls, setAnchorEls] = React.useState({});
  // Handle row click
  const handleRowClick = (params) => {
    onMessageClick(params.row);
  };

  // Handle sort model change
  const handleSortModelChange = (newModel) => {
    if (newModel.length === 0) {
      const lastField = sortModel[0]?.field || (type === 'sent' ? 'sent' : 'received');
      const lastSort = sortModel[0]?.sort || 'desc';
      const newSort = lastSort === 'asc' ? 'desc' : 'asc';
      onSortModelChange([{ field: lastField, sort: newSort }]);
    } else {
      onSortModelChange(newModel);
    }
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize) => {
    onPageSizeChange?.(newPageSize);
  };

  // Apply client-side filtering based on the search text if provided
  const filteredRows = useMemo(() => {
    const rows =
      messages?.map((message, index) => {
        const baseFields = {
          id: message.id || index,
          message: message.content || '',
          isRead: message.status === 'read',
          ...message,
        };

        // Add type-specific fields
        if (type === 'sent') {
          const fullRecipient = message.recipient;
          const nameOnly = fullRecipient.match(/^(.*?)\s*<.*?>$/)?.[1].trim() || fullRecipient;
          return {
            ...baseFields,
            to: nameOnly || '',
            sent: message.timestamp,
          };
        } else {
          return {
            ...baseFields,
            from: message.sender || '',
            received: message.timestamp,
          };
        }
      }) || [];

    if (!searchText) return rows;

    const searchTerm = searchText.toLowerCase();
    if (type === 'sent') {
      return rows.filter(
        (row) =>
          (row.to && row.to.toLowerCase().includes(searchTerm)) ||
          (row.message && row.message.toLowerCase().includes(searchTerm)),
      );
    } else {
      return rows.filter(
        (row) =>
          (row.from && row.from.toLowerCase().includes(searchTerm)) ||
          (row.message && row.message.toLowerCase().includes(searchTerm)),
      );
    }
  }, [messages, searchText, type]);

  // Memoize columns based on type to prevent re-rendering issues
  const columns = useMemo(() => {
    const baseColumns = [
      {
        field: type === 'sent' ? 'to' : 'from',
        headerName: type === 'sent' ? 'To' : 'From',
        flex: 1,
        minWidth: 120,
        sortable: true,
        renderCell: (params) => (
          <Box
            sx={{
              fontWeight: type === 'inbox' && !params.row.isRead ? 'bold' : 'normal',
              display: 'flex',
              alignItems: 'center',
              height: '100%',
            }}
          >
            {params.value}
          </Box>
        ),
      },
      {
        field: 'message',
        headerName: 'Message',
        flex: 2,
        minWidth: 200,
        sortable: false,
        renderCell: (params) => (
          <Box
            sx={{
              fontWeight: type === 'inbox' && !params.row.isRead ? 'bold' : 'normal',
              display: 'flex',
              alignItems: 'center',
              height: '100%',
            }}
          >
            {params.value?.length > 25 ? `${params.value.substring(0, 25)} ...` : params.value}
          </Box>
        ),
      },
      {
        field: type === 'sent' ? 'sent' : 'received',
        headerName: type === 'sent' ? 'Sent' : 'Received',
        flex: 1,
        minWidth: 120,
        sortable: true,
        renderCell: (params) => {
          const timestamp = dayjs(params.value);
          const isToday = timestamp.isSame(dayjs(), 'day');
          const displayText = isToday ? timestamp.format('h:mm A') : timestamp.format('YYYY-MM-DD');
          return (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                height: '100%',
                fontWeight: type === 'inbox' && !params.row.isRead ? 'bold' : 'normal',
              }}
            >
              {displayText}
            </Box>
          );
        },
      },
    ];

    // Only add the actions column if we're not showing sent messages
    if (type !== 'sent') {
      baseColumns.push({
        field: 'actions',
        headerName: '',
        flex: 0.5,
        minWidth: 60,
        sortable: false,
        filterable: false,
        renderCell: (params) => {
          const rowId = params.row.id;
          return (
            <>
              <IconButton
                onClick={(event) => {
                  event.stopPropagation();
                  setAnchorEls((prev) => ({ ...prev, [rowId]: event.currentTarget }));
                }}
                size="small"
                aria-label="more options"
              >
                <MoreVertIcon />
              </IconButton>
              <Menu
                anchorEl={anchorEls[rowId]}
                open={Boolean(anchorEls[rowId])}
                onClose={() => setAnchorEls((prev) => ({ ...prev, [rowId]: null }))}
                anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
                transformOrigin={{ vertical: 'top', horizontal: 'right' }}
              >
                <MenuItem
                  onClick={() => {
                    console.log('Action for row ID:', rowId);
                    setAnchorEls((prev) => ({ ...prev, [rowId]: null }));
                  }}
                >
                  View
                </MenuItem>
              </Menu>
            </>
          );
        },
      });
    }

    return baseColumns;
  }, [type, anchorEls, setAnchorEls]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  if (!messages?.length) {
    return (
      <Box sx={{ p: 2 }}>
        <Alert severity="info">No messages found</Alert>
      </Box>
    );
  }

  return (
    <DataGrid
      rows={filteredRows}
      columns={columns}
      onRowClick={handleRowClick}
      sortModel={sortModel}
      onSortModelChange={handleSortModelChange}
      onPageSizeChange={handlePageSizeChange}
      initialState={{
        pagination: {
          paginationModel: { pageSize: pageSize, page: 0 },
        },
      }}
      pageSizeOptions={pageSizeOptions}
      hideFooterSelectedRowCount={true}
      sortingMode="server"
      sx={{
        '& .MuiDataGrid-cell:focus': {
          outline: 'none',
        },
        '& .MuiDataGrid-row:hover': {
          backgroundColor: '#f5f5f5',
        },
        '& .MuiDataGrid-sortIcon': {
          color: 'rgb(77, 118, 169)',
        },
        '& .MuiSelect-select.MuiSelect-select': {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          paddingTop: 0.5,
          paddingBottom: 0.5,
        },
      }}
    />
  );
};
