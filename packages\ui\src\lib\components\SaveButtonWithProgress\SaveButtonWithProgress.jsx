import * as React from 'react';
import Button from '@mui/material/Button';
import CircularProgress from '@mui/material/CircularProgress';
function SaveButtonWithProgress(props) {
  const { saving, onClickHandler, buttonText, btnSize } = props;
  return (
    <Button variant="contained" size={btnSize || 'small'} disabled={saving} onClick={(e) => onClickHandler(e)}>
      {saving && <CircularProgress color="inherit" size={15} sx={{ marginRight: '5px' }} />}
      {buttonText || 'Save'}
    </Button>
  );
}

export { SaveButtonWithProgress };
