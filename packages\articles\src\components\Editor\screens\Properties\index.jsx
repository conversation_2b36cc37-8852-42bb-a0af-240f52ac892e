import React, { useState, useEffect } from 'react';
import { FormLabel, TextField, Typography, Stack, IconButton, Box, Paper, Grid } from '@mui/material';
import { Close, Article, Edit } from '@mui/icons-material';
import { strings } from '../../../../utility/strings';
import { CambianTooltip } from '@cambianrepo/ui';
import { Loader } from '@/components';
import { isEmpty, hasMinLength } from '../../../../utility/validation';

export const Properties = ({
  existingArticleData,
  title,
  name,
  description,
  thumbnail,
  onTitleChange,
  onNameChange,
  onDescriptionChange,
  onThumbnailChange,
  formErrors = {},
}) => {
  const [imagePreview, setImagePreview] = useState(null);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({
    name: null,
    title: null,
  });

  useEffect(() => {
    if (formErrors && Object.keys(formErrors).length > 0) {
      setErrors((prev) => ({
        ...prev,
        name: formErrors.name || prev.name,
        title: formErrors.title || prev.title,
      }));
    }
  }, [formErrors]);

  const handleRemoveFile = () => {
    setImagePreview(null);
    onThumbnailChange(null);
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const previewUrl = URL.createObjectURL(file);
      setImagePreview(previewUrl);

      onThumbnailChange({
        contentType: file.type,
        fileName: file.name,
        _file: file,
        _previewUrl: previewUrl,
      });
    }
  };

  const handleNameChange = (value) => {
    onNameChange(value);
    if (errors.name && value.trim() !== '') {
      setErrors((prev) => ({ ...prev, name: null }));
    } else {
      validateName(value);
    }
  };

  const handleTitleChange = (value) => {
    onTitleChange(value);
    if (errors.title && value.trim() !== '') {
      setErrors((prev) => ({ ...prev, title: null }));
    } else {
      validateTitle(value);
    }
  };

  const validateName = (nameValue) => {
    let nameError = null;

    if (isEmpty(nameValue)) {
      nameError = 'Name is required';
    } else if (!hasMinLength(nameValue)) {
      nameError = 'Must be at least 2 characters';
    }

    setErrors((prev) => ({ ...prev, name: nameError }));
    return nameError === null;
  };

  const validateTitle = (titleValue) => {
    let titleError = null;

    if (isEmpty(titleValue)) {
      titleError = 'Title is required';
    } else if (!hasMinLength(titleValue)) {
      titleError = 'Must be at least 2 characters';
    }

    setErrors((prev) => ({ ...prev, title: titleError }));
    return titleError === null;
  };

  useEffect(() => {
    const fetchThumbnail = async () => {
      // Clean up any previous object URLs
      if (imagePreview && imagePreview.startsWith('blob:')) {
        URL.revokeObjectURL(imagePreview);
      }

      // Handle cases where thumbnail is provided directly with the file data
      if (thumbnail?._file) {
        const previewUrl = URL.createObjectURL(thumbnail._file);
        setImagePreview(previewUrl);
      }
      // Handle data from parent API that includes blob data
      else if (thumbnail?.data instanceof Blob) {
        const previewUrl = URL.createObjectURL(thumbnail.data);
        setImagePreview(previewUrl);
      }
      // Handle existing thumbnail from presigned URL
      else if (existingArticleData?.thumbnailPresignedUrl && !imagePreview) {
        setLoading(true);
        try {
          // First attempt to load the image directly
          setImagePreview(existingArticleData.thumbnailPresignedUrl);
        } catch (error) {
          console.error('Error loading thumbnail:', error);
        } finally {
          setLoading(false);
        }
      }
    };
    fetchThumbnail();

    // Cleanup on unmount
    return () => {
      if (imagePreview && imagePreview.startsWith('blob:')) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [thumbnail, existingArticleData?.thumbnailPresignedUrl]);

  const ThumbnailSection = () => {
    const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
  
    useEffect(() => {
      if (imagePreview) {
        const img = new Image();
        img.onload = () => {
          setImageDimensions({
            width: img.width,
            height: img.height,
          });
        };
        img.src = imagePreview;
      } else {
        setImageDimensions({ width: 0, height: 0 });
      }
    }, [imagePreview]);
  
    return (
      <Grid container direction="row" justifyContent="space-between">
        <Grid item>
          <Box position="relative" sx={{ width: 80, height: 80 }}>
            {loading ? (
              <Box
                sx={{
                  width: '100%',
                  height: '100%',
                  borderRadius: '50%',
                  bgcolor: '#bdbdbd',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Article sx={{ fontSize: 40, color: '#FFFFFF' }} />
              </Box>
            ) : imagePreview ? (
              <Box
                component="img"
                src={imagePreview}
                alt="Thumbnail preview"
                sx={{
                  width: '100%',
                  height: '100%',
                  borderRadius: '50%',
                  objectFit: 'cover',
                }}
              />
            ) : (
              <Box
                sx={{
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  bgcolor: '#bdbdbd',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Article sx={{ fontSize: 40, color: '#FFFFFF' }} />
              </Box>
            )}
  
            {imagePreview && (
              <IconButton
                onClick={handleRemoveFile}
                sx={{
                  position: 'absolute',
                  top: -8,
                  right: -8,
                  bgcolor: 'white',
                  boxShadow: 2,
                  padding: '4px',
                  '&:hover': { bgcolor: 'grey.100' },
                }}
                size="small"
              >
                <Close sx={{ fontSize: '20px' }} />
              </IconButton>
            )}
  
            <IconButton
              component="label"
              sx={{
                position: 'absolute',
                bottom: -8,
                right: -8,
                bgcolor: 'white',
                boxShadow: 2,
                padding: '4px',
                '&:hover': { bgcolor: 'grey.100' },
              }}
              size="small"
            >
              <Edit sx={{ fontSize: '20px' }} />
              <input
                type="file"
                accept="image/png, image/jpeg, image/jpg, image/gif, image/svg+xml, image/bmp, image/ico"
                hidden
                onChange={handleImageUpload}
              />
            </IconButton>
          </Box>
        </Grid>
        <Grid item xs sx={{ paddingLeft: 2 }}>
          <Box>
            <Typography sx={{ color: 'text.secondary', fontSize: '12px' }}>
              Format: PNG, JPG, JPEG, GIF
            </Typography>
            <Typography sx={{ color: 'text.secondary', fontSize: '12px' }}>
              Recommended: 300 x 300 pixels; &lt; 200kb
            </Typography>
            {imageDimensions.width > 0 && (
              <Typography sx={{ color: 'text.secondary', fontSize: '12px' }}>
                Current: {imageDimensions.width} x {imageDimensions.height} pixels
              </Typography>
            )}
          </Box>
        </Grid>
      </Grid>
    );
  };

  return (
    <>
      <Loader active={loading} message="Loading thumbnail..." />
      <Paper sx={{ border: 'none' }}>
        <Stack direction="row" alignItems="center" mb={2}>
          <Typography variant="h3">{strings.properties}</Typography>
        </Stack>
      </Paper>

      <Stack spacing={3} sx={{ maxWidth: '100%' }}>
        <Box>
          <Typography variant="subtitle1" gutterBottom>
            <CambianTooltip title={strings.articleNameTooltip}>
              <FormLabel required={true} sx={{ mb: 1, color: '#000' }}>
                {strings.name}
              </FormLabel>
            </CambianTooltip>
          </Typography>
          <TextField
            variant="outlined"
            fullWidth
            size="small"
            value={name}
            onChange={(e) => handleNameChange(e.target.value)}
            placeholder={strings.name}
            error={Boolean(errors.name)}
            helperText={errors.name}
          />
        </Box>

        <Box>
          <Typography variant="subtitle1" gutterBottom>
            <CambianTooltip title={strings.articleTitleTooltip}>
              <FormLabel required={true} sx={{ mb: 1, color: '#000' }}>
                {strings.title}
              </FormLabel>
            </CambianTooltip>
          </Typography>
          <TextField
            variant="outlined"
            fullWidth
            size="small"
            value={title}
            onChange={(e) => handleTitleChange(e.target.value)}
            placeholder={strings.title}
            error={Boolean(errors.title)}
            helperText={errors.title}
          />
        </Box>

        <Box>
          <Typography variant="subtitle1" gutterBottom>
            <CambianTooltip title={strings.articleDescriptionTooltip}>
              <FormLabel required={false} sx={{ mb: 1, color: '#000' }}>
                {strings.description}
              </FormLabel>
            </CambianTooltip>
          </Typography>
          <TextField
            multiline
            variant="outlined"
            fullWidth
            size="small"
            rows={4}
            value={description}
            onChange={(e) => onDescriptionChange(e.target.value)}
            placeholder={strings.description}
          />
        </Box>

        <Box>
          <ThumbnailSection />
        </Box>
      </Stack>
    </>
  );
};
