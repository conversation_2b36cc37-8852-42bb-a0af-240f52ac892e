import { Route, Routes, useNavigate } from 'react-router-dom';
import GeneralTestPage from './pages/GeneralTestPage';
import { But<PERSON>, Stack } from '@mui/material';
import React from 'react';

function ComponentPlayground(props) {
  let navigate = useNavigate();
  return (
    <div>
      <Routes>
        <Route path="/" element={<GeneralTestPage />} />
      </Routes>
    </div>
  );
}

export default ComponentPlayground;
