export const FHIR_SIMPLE_GRID_QUESTION = {
  resourceType: 'Questionnaire',
  date: '2024-12-15T15:41:55.735Z',
  name: 'data grid html_report',
  title: 'data grid html_report',
  description: 'data grid html_report',
  subjectType: 'Patient',
  extension: [
    {
      url: 'display-dial',
      valueBoolean: false,
    },
    {
      url: 'display-description',
      valueBoolean: true,
    },
    {
      url: 'display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'display-score',
      valueBoolean: false,
    },
    {
      url: 'display-score-category',
      valueBoolean: false,
    },
    {
      url: 'display-title',
      valueBoolean: true,
    },
    {
      url: 'questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'trendable',
      valueBoolean: false,
    },
    {
      url: 'question-identifier-next-sequence',
      valueInteger: 2,
    },
    {
      url: 'pdftemplate-id',
      valueString: '',
    },
    {
      url: 'question-identifier-prefix',
      valueString: 'Item',
    },
    {
      url: 'codeBookHtmlData',
      valueString:
        '\n<h2 style="text-align: center;"><strong>General Data Format</strong></h2>\n<p>The data export file for questionnaire responses has the following characteristics:</p>\n<ol>\n<li data-list-text="1.">\n<p>The file format is &ldquo;csv&rdquo;(machine readable)</p>\n</li>\n<li data-list-text="2.">\n<p>One row for each questionnaire response (Long Format)</p>\n</li>\n<li data-list-text="3.">\n<p>Each row contains the following:</p>\n<ol style="list-style-type: lower-alpha;">\n<li data-list-text="a.">\n<p>An identifier for the questionnaire</p>\n</li>\n<li data-list-text="b.">\n<p>The group id of the group the participant is a member of</p>\n</li>\n<li data-list-text="c.">\n<p>The participant id of the user filling in the response</p>\n</li>\n<li data-list-text="d.">\n<p>The start date (YYYY-MM-DD hh:mm:ss) when the participant begins to work on the response</p>\n</li>\n<li data-list-text="e.">\n<p>The completion date (YYYY-MM-DD hh:mm:ss)</p>\n</li>\n<li data-list-text="f.">\n<p>Time spent in completing questionnaire (in seconds)</p>\n</li>\n<li data-list-text="g.">\n<p>The response for each item (Note: For multiple choice questions that allow multiple responses, each allowable response is turned into a item with the possible response of Yes or No. Free form text responses are put in double quotes (e.g.,”I experienced some pain”) so commas can be used inside the response value. Double quotes are escaped with another double quote (e.g.,”I experienced some “”phantom”” pain”). A skipped item will have the corresponding item response set to -99.)</p>\n</li>\n<li data-list-text="h.">\n<p>The computed scores if applicable (Note: if a score cannot be computed because of missing data, the corresponding field will be set to -99)</p>\n</li>\n</ol>\n</li>\n</ol><h1 style="text-align: center;">{Questionnaire.title}</h1>\n<h3>Questionnaire and Item mapping</h3>\n<p>Each questionnaire is given a unique numerical identifier and each item within a questionnaire is given a name that is used to define the columns used in the data export file.</p>\n<table style="border-collapse: collapse; width: 900px;">\n<tbody>\n<tr>\n<td style="width: 714px; border-style: solid; padding-left: 10px;">\n<p>{Questionnaire.title}</p>\n</td>\n<td style="width: 178px; border-style: solid; padding-left: 10px;">\n<p>{Questionnaire.id}</p>\n</td>\n</tr>\n</tbody>\n</table>\n<p>&nbsp;</p>\n{Questionnaire.mappedQuestionsList}\n<p>&nbsp;</p>\n<p>Each computed score within a questionnaire is given a name that is used to define the columns used in the data export file.</p>\n<table style="border-collapse: collapse; width: 900.359px;">\n<tbody>\n<tr>\n<td style="width: 714px; border-style: solid; padding-left: 10px;">\n<p>score</p>\n</td>\n<td style="width: 178px; border-style: solid; padding-left: 10px;">\n<p>S1</p>\n</td>\n</tr>\n</tbody>\n</table>\n<p>&nbsp;</p>\n<p>With the information above, columns that will be presented in an entry for {Questionnaire.title} are defined as:</p>\n<p>{Questionnaire.questionnaireColumns}</p>\n<p>&nbsp;</p>\n<h3>Item response mapping</h3>\n<p>Allowed responses for each item are shown below:</p>\n<p>&nbsp;</p>\n{Questionnaire.mappedResponseList}\n<p>&nbsp;</p>\n<h3>Sample Data</h3>\n<p>k45e7b06-1295-47f2-9577-d8e4d43c5333,Item1, Item2, Item3, S1</p>\n<p>m5187b06-8321-88i2-2342-h456w234l231,Item1, Item2, Item3, Item4, Item5, Item6, S1</p>\n',
    },
    {
      url: 'question-identifier-next-sequence',
      valueInteger: 2,
    },
    {
      url: 'htmltemplate-base64',
      valueString:
        '<h1>{Questionnaire.title}</h1>\n<p>Date: {QuestionnaireResponse.completionDate:format(YYYY-MM-DD HH:mm:ss)}</p>\n<p>Data Grid: {QuestionnaireResponse.item.Item1:headerStyle(font-size: 36px; color: #ff0000; background-color:#b8b8b8)}</p>\n<p style="color:gray; font-size:11px;">{Questionnaire.description}</p>',
    },
    {
      url: 'pdftemplate-name',
      valueString: '',
    },
    {
      url: 'list-of-score-definitions',
      extension: [
        {
          url: 'score-id',
          valueCode: 'c8d80413-3fbb-4251-a0d5-3e285fb19656',
        },
        {
          url: 'score-sequence',
          valueInteger: 0,
        },
        {
          url: 'score-name',
          valueString: 'NewVariable',
        },
        {
          url: 'list-of-formula-definitions',
          extension: [
            {
              extension: [
                {
                  url: 'formula-name',
                  valueString: 'NewVariable-F1',
                },
                {
                  url: 'mathematical-expression',
                  valueString: '',
                },
                {
                  url: 'selection-rule',
                  valueString: 'Select Rule',
                },
              ],
              url: 'set-of-api-formula',
            },
          ],
        },
      ],
    },
  ],
  identifier: [
    {
      use: 'old',
      system: 'questionnaire/identifier',
      value: 'd3ea065c-2d4f-4d38-b4c7-fc4b28d21f78',
      period: {
        start: '2023-12-13T14:06:06+00:00',
        end: '2023-12-13T14:15:33+00:00',
      },
    },
    {
      use: 'usual',
      system: 'urn:uuid',
      value: 'f6356947-59d8-495b-abf7-47973d3a1968',
      period: {
        start: '2023-12-13T14:06:06+00:00',
      },
    },
  ],
  item: [
    {
      linkId: 'Group1',
      item: [
        {
          id: 'complex-s3mvyCojANqV2L597ukfK6',
          linkId: 'Item1',
          type: 'group',
          text: 'Data grid 1',
          item: [
            {
              id: 'dUUyTguUoCn7yjWRNxUuLu',
              type: 'group',
              item: [
                {
                  id: 0,
                  linkId: 'cU72iQ4KvsxVSoLUBppd3F',
                  text: 'text',
                  type: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                  ],
                },
                {
                  id: 1,
                  linkId: '8ae8CMsxi3NSUjyLDGzszC',
                  text: 'num',
                  type: 'decimal',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                  ],
                },
                {
                  id: 2,
                  linkId: 'x3THmYgnHKupN47YEY4Lc9',
                  text: 'date',
                  type: 'dateTime',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                  ],
                },
              ],
            },
          ],
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
        },
      ],
      id: 'group-2vNPGB9fuz9fwt8sfPQcQ6',
      type: 'group',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
    },
  ],
  publisher: 'App-Scoop',
  status: 'draft',
  id: 'c565a6d1-a3ad-45b2-9418-6a03f36dc8ff',
  url: 'Questionnaire/c565a6d1-a3ad-45b2-9418-6a03f36dc8ff',
};
