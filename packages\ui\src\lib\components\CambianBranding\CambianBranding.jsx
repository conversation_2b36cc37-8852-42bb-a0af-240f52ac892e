import { Avatar, Box } from '@mui/material';
import React from 'react';
import { useTheme } from '@mui/material/styles';

const useStyles = (theme) => ({
  brand: {
    backgroundColor: theme.palette.common.white,
    width: '160px',
    weight: '50px',
  },
  brandingImage: {
    float: 'left',
    display: 'inline-block',
    paddingTop: '3px',
  },
  brandingText: {
    float: 'left',
    textAlign: 'left',
    display: 'block',
    paddingLeft: '5px',
    paddingTop: '4px',
  },
  brandingLine1: {
    fontWeight: '600',
    // color: '#434343',
    color: theme.palette.grey[800],
    fontSize: '18px',
    lineHeight: '0.7em',
  },
  brandingLine2: {
    fontWeight: '400',
    // color: '#428bca',
    color: theme.palette.primary.main,
    fontSize: '24px',
    lineHeight: '0.7em',
    paddingTop: '6px',
  },
});

function CambianBranding(props) {
  const { line1, line2, imageUrl } = props;
  const theme = useTheme();
  const styles = useStyles(theme);

  let iconSizeStyle = { width: 40, height: 40 };
  return (
    <Box sx={{ ...styles.brand }}>
      <Box sx={{ ...styles.brandingImage }}>
        <Avatar alt={line1} src={imageUrl} sx={iconSizeStyle} />
      </Box>
      <Box sx={{ ...styles.brandingText }}>
        <Box sx={{ ...styles.brandingLine1 }}>{line1}</Box>
        <Box sx={{ ...styles.brandingLine2 }}>{line2}</Box>
      </Box>
    </Box>
  );
}

export { CambianBranding };
