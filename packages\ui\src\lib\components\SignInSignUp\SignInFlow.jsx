import React, { useState, useRef } from 'react';
import { SignIn } from './SignIn';
import { SignUp } from './SignUp';
import { ForgotPassword } from './ForgotPassword';
import { VerifyRegistration } from './VerifyRegistration';
import { EmailSentMessage } from './EmailSentMessage';
import { ChangePassword } from './ChangePassword';
import { ChangeEmail } from './ChangeEmail';
import { Mfa } from './Mfa';
import { Typography } from '@mui/material';
import { DEFAULT_ERROR_MSG, UNKNOWN_ERROR } from './constant';

function SignInFlow({
  signInProps,
  signUpProps,
  mfaProps,
  forceChangePasswordProps,
  forgotPasswordProps,
  verifyAndChangePasswordProps,
  emailSentMessageProps,
  verifyRegistrationProps,
  changeEmailProps,
}) {
  const SIGN_IN = 'sign-in';
  const MFA = 'mfa';
  const FORCE_CHANGE_PASSWORD = 'change-password';
  const SIGN_UP = 'sign-up';
  const FORGOT_PASSWORD = 'forgot-password';
  const VERIFY_AND_CHANGE_PASSWORD = 'verify-change-password';
  const VERIFY_REGISTRATION = 'verify-registration';
  const EMAIL_SENT_MESSAGE = 'email-sent-message';
  const CHANGE_EMAIL = 'change-email';
  const [selectedView, setSelectedView] = useState(
    verifyRegistrationProps?.emailAddress ? VERIFY_REGISTRATION : SIGN_IN,
  );
  const previousFormData = useRef({});
  const { password: { min = 8, max = 30 } = {} } = verifyAndChangePasswordProps ?? {};

  // This is an anti React pattern. We wanted to change the byline in the verifyAndChangePasswordProps conditionally in forgotPasswordCallback.
  // I think due to closure, the component was not receiving the updated props. Thus, made the prop into a state and supplied that to the component below.
  const [verifyAndChangePasswordByline, setVerifyAndChangePasswordByline] = useState(verifyRegistrationProps?.byline);
  const [emailAddress, setEmailAddress] = useState(verifyRegistrationProps?.emailAddress);

  const [mfaUrl, setMfaUrl] = useState(null);

  const forgotPasswordCallback = async (form) => {
    const { success, errorMsg } = await forgotPasswordProps.forgotPasswordCallback(form);
    previousFormData.current = form;
    if (success && verifyAndChangePasswordProps) {
      const updatedByline = (
        <>
          {`Enter the verification code sent to `}
          <Typography component="span" variant="inherit" sx={{ fontWeight: 'bold' }}>
            {form.username || 'your email'}
          </Typography>
          {` and new password from ${min}-${max} characters in length`}
        </>
      );

      setVerifyAndChangePasswordByline(updatedByline);
      setSelectedView(VERIFY_AND_CHANGE_PASSWORD);
    } else if (success) {
      setSelectedView(EMAIL_SENT_MESSAGE);
    }
    return { success, errorMsg };
  };

  const changePasswordCallback = async (form) => {
    const { success, errorCode } = await verifyAndChangePasswordProps.changePasswordCallback({
      ...form,
      ...previousFormData.current,
    });
    if (success) {
      setSelectedView(SIGN_IN);
    }
    return { success, errorCode };
  };

  const forceChangePasswordCallback = async ({ newPassword }) => {
    const { success, response, errorCode } = await forceChangePasswordProps.forceChangePasswordCallback({
      newPassword,
      username: previousFormData.current?.username,
      session: previousFormData.current?.session,
      userId: previousFormData.current?.userId,
    });

    if (mfaProps && success) {
      if (!response.session || !response.challengeName) {
        console.error('A forceChangePasswordCallback function in signInProps must return session and challengeName.');
        return { success: false, errorCode: UNKNOWN_ERROR };
      }
      if (response.challengeName === 'MFA_SETUP' && !response.mfaUrl) {
        return { success: false, errorCode: 'MFAQRIssue' };
      }
      previousFormData.current.password = newPassword;
      previousFormData.current.session = response.session;
      previousFormData.current.challengeName = response.challengeName;
      setMfaUrl(response.mfaUrl);
      setSelectedView(MFA);
    }
    return { success, errorCode };
  };

  // form: {username, password}
  const signInCallback = async (form) => {
    const { success, response = {}, errorMsg, errorName } = await signInProps.signInCallback(form);

    setEmailAddress(form.username);
    previousFormData.current = form;
    previousFormData.current.session = response.session;
    previousFormData.current.userId = response.userId;
    previousFormData.current.challengeName = response.challengeName;

    if (response.challengeName === 'NEW_PASSWORD_REQUIRED') {
      setSelectedView(FORCE_CHANGE_PASSWORD);
      return { success, errorMsg };
    }

    if (errorName === 'UserNotConfirmedException') {
      return { success, errorMsg: 'Your account has not been verified. ', errorName };
    }

    if (mfaProps && success) {
      if (!response.session || !response.challengeName) {
        console.error('A signInCallback function in signInProps must return session and challengeName.');
        return { success: false, errorMsg: `${DEFAULT_ERROR_MSG}` };
      }
      if (response.challengeName === 'MFA_SETUP' && !response.mfaUrl) {
        return { success: false, errorMsg: `${DEFAULT_ERROR_MSG} Unable to populate MFA QR code.` };
      }
      setMfaUrl(response.mfaUrl);
      setSelectedView(MFA);
    }
    return { success, errorMsg };
  };

  //form: {verificationCode}
  const mfaCallback = async (form) => {
    const { success, errorMsg } = await mfaProps.mfaCallback({
      username: previousFormData.current?.username,
      session: previousFormData.current?.session,
      challengeName: previousFormData.current?.challengeName,
      verificationCode: form.verificationCode,
    });
    return { success, errorMsg };
  };

  // form: {username, password, firstName, lastName}
  const signUpCallback = async (form) => {
    const { success, isVerificationDisabled, errorMsg } = await signUpProps.signUpCallback(form);
    previousFormData.current = form;
    if (success && !isVerificationDisabled) {
      setEmailAddress(form.username);
      setSelectedView(VERIFY_REGISTRATION);
    } else if (success) {
      setSelectedView(SIGN_IN);
    }
    return { success, errorMsg };
  };

  const verifyRegistrationCallback = async (form) => {
    const username = previousFormData.current?.username ?? emailAddress;
    const { success, errorMsg } = await verifyRegistrationProps.verifyRegistrationCallback({
      ...form,
      ...previousFormData.current,
      username,
    });
    if (success && verifyRegistrationProps.autoSignInEnabled && previousFormData.current) {
      // If the useRef value is undefined, it means that the user was directed to VerifyRegistration page due to the cookie's unverfied-user value.
      // In this case, the password value is not saved in the local useRef variable. Thus, auto sign in should not start.
      const { success, errorMsg } = await signInProps.signInCallback({
        username,
        password: previousFormData.current.password,
      });
      if (!success) {
        setSelectedView(SIGN_IN);
      }
      return { success, errorMsg };
    } else if (success) {
      setSelectedView(SIGN_IN);
    }
    return { success, errorMsg };
  };

  const changeEmailCallback = async (form) => {
    const { success, errorMsg } = await changeEmailProps.changeEmailCallback({
      ...form,
      username: previousFormData.current?.username ?? emailAddress,
    });
    if (success) {
      previousFormData.current.username = form.newUsername;
      setEmailAddress(form.newUsername);
      setSelectedView(VERIFY_REGISTRATION);
    }
    return { success, errorMsg };
  };

  const resendForgotPasswordVerificationCodeCallback = async () => {
    return await forgotPasswordProps.forgotPasswordCallback({ username: previousFormData.current.username });
  };

  const navigateToForgotPasswordCallback = () => {
    setSelectedView(FORGOT_PASSWORD);
  };

  const navigateToSignInCallback = () => {
    setSelectedView(SIGN_IN);
  };

  const navigateToSignUpCallback = () => {
    setSelectedView(SIGN_UP);
  };

  const navigateToVerifyRegistrationCallback = () => {
    setSelectedView(VERIFY_REGISTRATION);
  };
  const navigateToChangeEmailCallback = () => {
    setSelectedView(CHANGE_EMAIL);
  };

  const renderSelectedView = () => {
    switch (selectedView) {
      case SIGN_IN:
        return (
          <SignIn
            {...signInProps}
            signInCallback={signInCallback}
            navigateToVerifyRegistrationCallback={navigateToVerifyRegistrationCallback}
            navigateToForgotPasswordCallback={forgotPasswordProps ? navigateToForgotPasswordCallback : undefined}
            navigateToSignUpCallback={signUpProps ? navigateToSignUpCallback : undefined}
          />
        );
      case FORCE_CHANGE_PASSWORD:
        return (
          <ChangePassword
            {...forceChangePasswordProps}
            navigateCallback={navigateToSignInCallback}
            changePasswordCallback={forceChangePasswordCallback}
            password={forceChangePasswordProps.passwordRequirements}
          />
        );
      case MFA:
        return (
          <Mfa {...mfaProps} mfaUrl={mfaUrl} mfaCallback={mfaCallback} navigateCallback={navigateToSignInCallback} />
        );
      case FORGOT_PASSWORD:
        return (
          <ForgotPassword
            {...forgotPasswordProps}
            navigateCallback={navigateToSignInCallback}
            forgotPasswordCallback={forgotPasswordCallback}
          />
        );
      case VERIFY_AND_CHANGE_PASSWORD:
        return (
          <ChangePassword
            {...verifyAndChangePasswordProps}
            byline={verifyAndChangePasswordByline}
            navigateCallback={navigateToForgotPasswordCallback}
            verifyForgotPasswordCode={true}
            changePasswordCallback={changePasswordCallback}
            resendVerificationCodeCallback={resendForgotPasswordVerificationCodeCallback}
          />
        );
      case EMAIL_SENT_MESSAGE:
        return (
          <EmailSentMessage
            {...emailSentMessageProps}
            navigateCallback={navigateToSignInCallback}
            {...previousFormData.current}
          />
        );
      case SIGN_UP:
        return <SignUp {...signUpProps} navigateCallback={navigateToSignInCallback} signUpCallback={signUpCallback} />;
      case VERIFY_REGISTRATION:
        return (
          <VerifyRegistration
            {...verifyRegistrationProps}
            emailAddress={emailAddress}
            navigateCallback={navigateToSignUpCallback}
            verifyRegistrationCallback={verifyRegistrationCallback}
            navigateToChangeEmailCallback={changeEmailProps ? navigateToChangeEmailCallback : undefined}
          />
        );
      case CHANGE_EMAIL:
        return (
          <ChangeEmail
            {...changeEmailProps}
            navigateCallback={navigateToVerifyRegistrationCallback}
            currentEmail={emailAddress}
            changeEmailCallback={changeEmailCallback}
          />
        );
      default:
        break;
    }
  };

  return <>{renderSelectedView()}</>;
}

export { SignInFlow };
