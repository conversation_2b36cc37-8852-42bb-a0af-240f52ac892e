import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, Typo<PERSON>, Link, CircularProgress, Box, useMediaQuery } from '@mui/material';
import { UncontrolledUsernameInput } from './UncontrolledUsernameInput';
import { validateUsername } from './Regex';
import cambian<PERSON>ogo from './cambian-logo.png';

function ForgotPassword(props) {
  const {
    forgotPasswordCallback,
    navigateCallback,
    logoUrl = cambianLogo,
    title = 'Forgot password?',
    byline = 'Reset your password in two quick steps',
    buttonText = 'Reset password',
    linkBelowButtonText = 'Back',
    orgName = 'Cambian',
    usernameType = 'email',
  } = props;

  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const [serverResponseError, setServerResponseError] = React.useState(false);
  const [isLoading, setIsLoading] = React.useState(false);

  const usernameInputRef = React.useRef();
  const [usernameValidSchema, setUsernameValidSchema] = React.useState({});

  const handleSubmit = async () => {
    const usernameValidationResult = validateUsername({
      min: 1,
      requireEmailFormat: usernameType === 'email',
      value: usernameInputRef.current.value,
    });
    setUsernameValidSchema(usernameValidationResult);
    const isUsernameValid = !Object.values(usernameValidationResult).some((v) => v === false);

    if (!isUsernameValid) {
      usernameInputRef.current.focus();
    }
    if (isUsernameValid) {
      setIsLoading(true);
      const { success, errorMsg } = await forgotPasswordCallback({ username: usernameInputRef.current.value });
      if (!success) {
        console.error(errorMsg);
        setServerResponseError(true);
        setIsLoading(false);
      }
    } else {
      setServerResponseError(false);
    }
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  return (
    <Stack direction="column" spacing={1.5} sx={{ width: { xs: '100%', sm: '40ch' } }}>
      <Box
        component="img"
        sx={{
          marginLeft: '-1%',
          width: { xs: '50%', sm: '55%' },
          minWidth: '100px',
          minHeight: '30px',
          height: { xs: '40%', sm: '45%' },
          marginBottom: -0.5,
        }}
        src={logoUrl}
        alt={`${orgName} full logo`}
      />
      <Typography sx={{ fontSize: { xs: 24, sm: 30 }, fontWeight: 500 }}>{title}</Typography>
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{byline}</Typography>
      <UncontrolledUsernameInput
        ref={usernameInputRef}
        autoFocus="true"
        orgName={orgName}
        usernameType={usernameType}
        validSchema={usernameValidSchema}
        onKeyDown={handleKeyDown}
      />
      <Button
        variant="contained"
        disabled={isLoading}
        onClick={handleSubmit}
        sx={{ fontSize: { xs: 17, sm: 19 }, fontWeight: 600, padding: '11.5px 12px', lineHeight: 1 }}
      >
        {isLoading ? <CircularProgress size={isXs ? 15 : 19} /> : buttonText}
      </Button>
      {serverResponseError && (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
          An unexpected error occured. Please contact technical support.
        </Typography>
      )}
      <Link
        component="button"
        onClick={navigateCallback}
        sx={{ textAlign: 'left', width: 'fit-content', fontSize: { xs: 11, sm: 12 } }}
      >
        {linkBelowButtonText}
      </Link>
    </Stack>
  );
}

export { ForgotPassword };
