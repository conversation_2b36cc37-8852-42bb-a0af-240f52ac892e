import React from 'react';
import { TextField } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';

function DateTimeQuestion(props) {
  const { question, handleQuestionResponse } = props;
  const isReadOnly = handleQuestionResponse === undefined;

  const extractExistingAnswer = () => {
    let answer = '';
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      answer = question.answer.valueDateTime;
    }
    return answer;
  };

  const [value, setValue] = React.useState(extractExistingAnswer() ? new Date(extractExistingAnswer()) : null);

  const handleChange = (newValue) => {
    if (!isReadOnly) {
      setValue(newValue);
      if (newValue instanceof Date && !isNaN(newValue)) {
        question.answer = {
          valueDateTime: newValue.toISOString(),
        };
        handleQuestionResponse(question);
      } else if (newValue === null) {
        question.answer = {
          valueDateTime: null,
        };
        handleQuestionResponse(question);
      }
    }
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DateTimePicker
          clearable
          value={value}
          format="yyyy-MM-dd hh:mm a"
          onChange={(newValue) => {
            handleChange(newValue);
          }}
          disableOpenPicker={isReadOnly}
          slots={{
            textField: TextField,
          }}
          slotProps={{
            textField: {
              size: 'small',
              InputProps: {
                placeholder: isReadOnly ? '' : 'YYYY-MM-DD hh:mm aa',
              },
            },
          }}
        />
      </LocalizationProvider>
      <Explanation question={question} />
    </>
  );
}

export default DateTimeQuestion;
