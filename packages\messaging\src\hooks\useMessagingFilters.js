import { useState, useEffect, useMemo, useRef } from 'react';
import dayjs from 'dayjs';

/**
 * Hook for managing messaging filters including search, date ranges, and sorting
 * @param {Object} options Configuration options
 * @param {string} options.type Message type ('inbox' or 'sent')
 * @param {Object} options.initialFilters Initial filter values
 * @param {string} options.initialFilters.searchText Initial search text
 * @param {string} options.initialFilters.dateRange Initial date range preset name
 * @param {Object} options.initialFilters.sortModel Initial sort model
 * @returns {Object} Filter state and handlers
 */
export const useMessagingFilters = (options = {}) => {
  const { type = 'inbox', initialFilters = {} } = options;

  // Initialize state with defaults or provided values
  const [searchText, setSearchText] = useState(initialFilters.searchText || '');
  const [dateRange, setDateRange] = useState(initialFilters.dateRange || 'Last 7 Days');

  // Initialize with defaults based on "Last 7 Days"
  const [startDate, setStartDate] = useState(dayjs().subtract(7, 'day'));
  const [endDate, setEndDate] = useState(dayjs());

  // Flag to track if this is the first render
  const isInitialMount = useRef(true);

  // Default sort field based on message type
  const defaultSortField = type === 'inbox' ? 'received' : 'sent';
  const [sortModel, setSortModel] = useState(initialFilters.sortModel || [{ field: defaultSortField, sort: 'desc' }]);

  // Common date range presets - don't depend on startDate/endDate
  const dateRangePresets = useMemo(
    () => [
      {
        label: 'Last 7 Days',
        getValue: () => {
          const today = dayjs();
          return [today.subtract(7, 'day'), today];
        },
      },
      {
        label: 'Last 14 Days',
        getValue: () => {
          const today = dayjs();
          return [today.subtract(14, 'day'), today];
        },
      },
      {
        label: 'Last 21 Days',
        getValue: () => {
          const today = dayjs();
          return [today.subtract(21, 'day'), today];
        },
      },
      {
        label: 'Last Calendar Month',
        getValue: () => {
          const today = dayjs();
          const startOfLastMonth = today.subtract(1, 'month').startOf('month');
          return [startOfLastMonth, today];
        },
      },
      {
        label: 'Last Three Calendar Months',
        getValue: () => {
          const today = dayjs();
          const startOfThreeMonthsAgo = today.subtract(3, 'month').startOf('month');
          return [startOfThreeMonthsAgo, today];
        },
      },
      {
        label: 'Last Twelve Calendar Months',
        getValue: () => {
          const today = dayjs();
          const startOfLastYear = today.subtract(12, 'month').startOf('month');
          return [startOfLastYear, today];
        },
      },
      {
        label: 'Current Calendar Month',
        getValue: () => {
          const today = dayjs();
          return [today.startOf('month'), today];
        },
      },
      {
        label: 'Custom',
        getValue: () => {
          // Return null values for Custom; we'll handle this separately
          return [null, null];
        },
      },
    ],
    [],
  ); // No dependencies for dateRangePresets

  // Set initial date range only on mount
  useEffect(() => {
    // Skip this effect if not the first render or if dateRange is 'Custom'
    if (!isInitialMount.current || dateRange === 'Custom') {
      return;
    }

    isInitialMount.current = false;

    // Set dates based on initial dateRange
    const preset = dateRangePresets.find((preset) => preset.label === dateRange);
    if (preset?.getValue) {
      const [start, end] = preset.getValue();
      // Make sure we don't override valid initial values with nulls
      if (start) setStartDate(start);
      if (end) setEndDate(end);
    }
  }, []); // Run only on mount

  // Update dates when date range changes (after initial mount)
  useEffect(() => {
    // Skip this effect on the initial mount
    if (isInitialMount.current) {
      return;
    }

    // Don't update dates when switching to 'Custom'
    if (dateRange === 'Custom') {
      return;
    }

    const preset = dateRangePresets.find((preset) => preset.label === dateRange);
    if (preset?.getValue) {
      const [start, end] = preset.getValue();
      if (start) setStartDate(start);
      if (end) setEndDate(end);
    }
  }, [dateRange, dateRangePresets]);

  // Handler for search text changes
  const handleSearchChange = (event) => {
    setSearchText(event.target.value);
  };

  // Handler to clear search text
  const handleSearchClear = () => {
    setSearchText('');
  };

  // Handler for date range preset selection
  const handleDateRangeChange = (event, newValue) => {
    if (newValue) {
      setDateRange(newValue.label);

      // Dates will be updated by the useEffect when dateRange changes
      // so we don't need to update them here (prevents duplicate state updates)
    }
  };

  // Handler for start date changes with validation
  const handleStartDateChange = (newValue) => {
    if (endDate && newValue && newValue.isAfter(endDate)) {
      alert('Start Date cannot be after End Date.');
      return;
    }
    setStartDate(newValue);
    setDateRange('Custom');
  };

  // Handler for end date changes with validation
  const handleEndDateChange = (newValue) => {
    if (startDate && newValue && newValue.isBefore(startDate)) {
      alert('End Date cannot be before Start Date.');
      return;
    }
    setEndDate(newValue);
    setDateRange('Custom');
  };

  // Handler for sort model changes
  const handleSortModelChange = (newModel) => {
    // If the user tries to clear sorting (newModel is empty),
    // maintain the last sort but toggle the direction
    if (newModel.length === 0) {
      const lastField = sortModel[0]?.field || defaultSortField;
      const lastSort = sortModel[0]?.sort || 'desc';
      // Toggle between asc and desc only
      const newSort = lastSort === 'asc' ? 'desc' : 'asc';
      setSortModel([{ field: lastField, sort: newSort }]);
    } else {
      setSortModel(newModel);
    }
  };

  // Find the currently selected date range option
  const selectedDateRangeOption = useMemo(() => {
    return dateRangePresets.find((option) => option.label === dateRange) || null;
  }, [dateRange, dateRangePresets]);

  // Computed filters for API queries
  const filters = useMemo(
    () => ({
      searchText,
      startDate,
      endDate,
      sortModel,
    }),
    [searchText, startDate, endDate, sortModel],
  );

  return {
    // State
    searchText,
    dateRange,
    startDate,
    endDate,
    sortModel,
    dateRangePresets,
    selectedDateRangeOption,

    // Computed values
    filters,

    // Handlers
    handleSearchChange,
    handleSearchClear,
    handleDateRangeChange,
    handleStartDateChange,
    handleEndDateChange,
    handleSortModelChange,

    // Setters for direct state updates if needed
    setSearchText,
    setDateRange,
    setStartDate,
    setEndDate,
    setSortModel,
  };
};
