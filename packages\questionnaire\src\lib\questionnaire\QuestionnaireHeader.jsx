import React from 'react';
import { Stack, Typography } from '@mui/material';

function QuestionnaireHeader(props) {
  const { questionnaireJSON, questionnaireTitle, completionDate } = props;

  let title = 'Unknown';
  if (questionnaireJSON !== undefined) {
    if (questionnaireJSON.title !== undefined && questionnaireJSON.title !== 'cannot locate string') {
      title = questionnaireJSON.title;
    } else {
      title = questionnaireJSON.name;
    }
  } else if (questionnaireTitle !== undefined) {
    title = questionnaireTitle;
  }

  return (
    <Stack direction="column" alignItems="center" sx={{ width: '100%', mb: '20px' }}>
      <Typography variant="h5">{title}</Typography>
      <Typography variant="h7">Completion Date: {completionDate}</Typography>
    </Stack>
  );
}

export { QuestionnaireHeader };
