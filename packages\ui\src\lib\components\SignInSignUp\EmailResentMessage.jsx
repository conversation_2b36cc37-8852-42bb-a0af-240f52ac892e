import React from 'react';
import { Typography, Box } from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';

function EmailResentMessage({ message }) {
  if (!message) {
    return <></>;
  }

  return (
    <Typography component="span" variant="inherit" sx={{ color: 'green', display: 'block', mb: 1.5 }}>
      <Box display="flex" alignItems="center">
        <CheckCircleIcon sx={{ fontSize: '1rem', color: 'green' }} />
        <span style={{ marginLeft: '0.2rem' }}>{message}</span>
      </Box>
    </Typography>
  );
}

export { EmailResentMessage };
