import React from 'react';
import { <PERSON>, <PERSON>, Grid, <PERSON>po<PERSON>, IconButton, Collapse, Checkbox, FormControlLabel } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { ThemeProvider } from '@mui/material/styles';
import { CambianTheme } from '@cambianrepo/ui';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import EditIcon from '@mui/icons-material/Edit';
import PhoneIcon from '@mui/icons-material/Phone';
import MailIcon from '@mui/icons-material/Mail';
import PlaceIcon from '@mui/icons-material/Place';
import RedeemIcon from '@mui/icons-material/Redeem';
import PersonIcon from '@mui/icons-material/Person';
import StarIcon from '@mui/icons-material/Star';
import BadgeIcon from '@mui/icons-material/Badge';
import moment from 'moment';

const useStyles = (theme) => ({
  input: {
    height: 40,
  },
  accordionClass: {
    border: `0.5px solid ${theme.palette.divider}`,
    background: '#F0F0F0',
    cursor: 'pointer',
  },
  accordionOpenClass: {
    borderRight: `0.5px solid ${theme.palette.divider}`,
    borderLeft: `0.5px solid ${theme.palette.divider}`,
    borderBottom: `0.5px solid ${theme.palette.divider}`,
    background: `${theme.palette.common.white}`,
  },
  wrapIcon: {
    verticalAlign: 'middle',
    display: 'inline-flex',
  },
  icon: {
    marginRight: '16px',
    color: '#0000008a',
  },
});

const calcAge = (dob) => {
  const age = moment().diff(moment(dob), 'years');

  if (isNaN(age)) {
    return ' - ';
  }

  return age;
};

export function CoordinatorDetailsReadOnlyAndEditMode(props) {
  const {
    title,
    firstNameConfigs,
    middleNameConfigs,
    lastNameConfigs,
    genderConfigs,
    dateOfBirthConfigs,
    preferredContactMechanismConfigs,
    subscribeToNotificationsConfigs,
    emailConfigs,
    primaryEmailConfigs,
    phoneNumberConfigs,
    primaryPhoneNumberConfigs,
    addressConfigs,
    primaryAddressLineOneConfigs,
    primaryAddressLineTwoConfigs,
    primarySelectedCountryConfigs,
    primaryProvinceConfigs,
    primarySelectedCityConfigs,
    primaryPostalCodeConfigs,
    idConfigs,
    nonPrimaryEmailConfigs,
    nonPrimaryPhoneNumberConfigs,
    nonPrimaryAddressConfigs,
    nonPrimaryIdConfigs,
    clientId,
    editMode,
    setEditMode,
    clientPage,
    CreateAndEditClientComponent,
  } = props;

  const theme = useTheme();
  const styles = useStyles(theme);
  const [expand, setExpand] = React.useState(false);
  const [showMoreAddresses, setShowMoreAddresses] = React.useState(false);
  const toggleAccordion = () => {
    setExpand(!expand);
  };

  const handleFormEdit = () => {
    setEditMode(true);
  };

  const closeEditMode = () => {
    setEditMode(false);
  };

  return (
    <ThemeProvider theme={CambianTheme}>
      <Box>
        <Box mt={1}>
          <Grid
            container
            direction="row"
            justifycontent="space-between"
            sx={{ paddingRight: 2, paddingLeft: 2, paddingBottom: 1, paddingTop: 2, ...styles.accordionClass }}
            onClick={toggleAccordion}
          >
            <Grid item xs={11}>
              <Box display="flex" justifyContent="flex-start" sx={{ paddingLeft: 1, fontWeight: 'bold' }}>
                <Typography
                  variant="h2"
                  sx={{ color: 'black' }}
                >{`${firstNameConfigs?.value} ${middleNameConfigs?.allowed && middleNameConfigs?.value && middleNameConfigs?.value !== '' ? middleNameConfigs?.value + ' ' : ''}${lastNameConfigs?.value}`}</Typography>
              </Box>
            </Grid>
            <Grid item xs={1}>
              <Box display="flex" justifyContent="flex-end">
                <IconButton aria-label="expand row" size="small">
                  {expand ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                </IconButton>
              </Box>
            </Grid>
          </Grid>
          <Collapse
            in={expand}
            timeout="auto"
            unmountOnExit
            sx={{ paddingRight: 2, paddingLeft: 2, paddingBottom: 1, paddingTop: 2, ...styles.accordionOpenClass }}
          >
            {!editMode && (
              <Box
                sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: '16px',
                  flexDirection: { xs: 'column', sm: 'row' },
                  position: 'relative',
                  minHeight: '30px',
                }}
              >
                {!!clientPage && (
                  <Typography
                    sx={{
                      position: 'absolute',
                      right: '8px',
                      top: '0',
                      cursor: 'pointer',
                      ...styles.wrapIcon,
                    }}
                  >
                    <EditIcon size="small" color="primary" onClick={handleFormEdit} />
                  </Typography>
                )}

                {(dateOfBirthConfigs?.allowed || genderConfigs?.allowed || idConfigs?.allowed) && (
                  <div style={{ flex: '1 1 auto', display: 'flex', flexDirection: 'column', wordWrap: 'break-word' }}>
                    {dateOfBirthConfigs?.allowed && (
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
                        <RedeemIcon sx={{ ...styles.icon }} />
                        {dateOfBirthConfigs?.value && (
                          <Typography>
                            {dateOfBirthConfigs?.value} ({calcAge(dateOfBirthConfigs?.value)} years)
                          </Typography>
                        )}
                      </div>
                    )}
                    {genderConfigs?.allowed && (
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>
                        <PersonIcon sx={{ ...styles.icon }} />
                        <Typography>{genderConfigs?.value}</Typography>
                      </div>
                    )}

                    {idConfigs?.allowed && (
                      <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                        <div>
                          <BadgeIcon sx={{ ...styles.icon }} />
                        </div>
                        <div style={{ display: 'grid', gridTemplateColumns: 'auto 1fr', gap: '1px' }}>
                          {idConfigs[0]?.type && idConfigs[0]?.issuer && idConfigs[0]?.value && (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <StarIcon fontSize="small" color="primary" />
                            </div>
                          )}
                          <Typography>
                            {idConfigs[0]?.type} {idConfigs[0]?.issuer} {idConfigs[0]?.value}
                          </Typography>
                          {idConfigs?.multiple &&
                            nonPrimaryIdConfigs?.map((id, index) => (
                              <React.Fragment key={index}>
                                <div style={{ marginLeft: '24px' }}></div>
                                <Typography>
                                  {id.type} {id.issuer} {id.value}
                                </Typography>
                              </React.Fragment>
                            ))}
                        </div>
                      </div>
                    )}
                  </div>
                )}

                {(emailConfigs?.allowed ||
                  phoneNumberConfigs?.allowed ||
                  preferredContactMechanismConfigs?.allowed ||
                  subscribeToNotificationsConfigs?.allowed) && (
                  <div style={{ flex: '1 1 auto', display: 'flex', flexDirection: 'column', wordWrap: 'break-word' }}>
                    {emailConfigs?.allowed && (
                      <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                        <div>
                          <MailIcon sx={{ ...styles.icon }} />
                        </div>
                        <div
                          style={{ display: 'grid', gridTemplateColumns: 'auto 1fr', gap: '1px', marginBottom: '1rem' }}
                        >
                          {primaryEmailConfigs?.value && (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <StarIcon fontSize="small" color="primary" />
                            </div>
                          )}
                          <Typography>{primaryEmailConfigs?.value}</Typography>
                          {emailConfigs?.multiple &&
                            nonPrimaryEmailConfigs?.map((email, index) => (
                              <React.Fragment key={index}>
                                <div style={{ marginLeft: '24px' }}></div>
                                <Typography>{email}</Typography>
                              </React.Fragment>
                            ))}
                        </div>
                      </div>
                    )}

                    {phoneNumberConfigs?.allowed && (
                      <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                        <div>
                          <PhoneIcon sx={{ ...styles.icon }} />
                        </div>
                        <div
                          style={{ display: 'grid', gridTemplateColumns: 'auto 1fr', gap: '1px', marginBottom: '1rem' }}
                        >
                          {primaryPhoneNumberConfigs?.value && (
                            <div style={{ display: 'flex', alignItems: 'center' }}>
                              <StarIcon fontSize="small" color="primary" />
                            </div>
                          )}
                          <Typography>{primaryPhoneNumberConfigs?.value}</Typography>
                          {phoneNumberConfigs?.multiple &&
                            nonPrimaryPhoneNumberConfigs?.map((phone, index) => (
                              <React.Fragment key={index}>
                                <div style={{ marginLeft: '24px' }}></div>
                                <Typography>{phone}</Typography>
                              </React.Fragment>
                            ))}
                        </div>
                      </div>
                    )}

                    {preferredContactMechanismConfigs?.allowed && (
                      <div style={{ display: 'flex', alignItems: 'flex-start', paddingLeft: '50px' }}>
                        <div>Preferred Contact Method: {preferredContactMechanismConfigs?.value}</div>
                      </div>
                    )}

                    {subscribeToNotificationsConfigs?.allowed && (
                      <div style={{ display: 'flex', alignItems: 'center', paddingLeft: '50px' }}>
                        <FormControlLabel
                          control={
                            <Checkbox
                              checked={subscribeToNotificationsConfigs?.value}
                              disabled
                              size="small"
                              sx={{ marginRight: '-16px' }}
                            />
                          }
                        />
                        Notifications
                      </div>
                    )}
                  </div>
                )}

                {addressConfigs?.allowed && (
                  <div style={{ flex: '1 1 auto', display: 'flex', flexDirection: 'column', wordWrap: 'break-word' }}>
                    <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                      <div>
                        <PlaceIcon sx={{ ...styles.icon }} />
                      </div>
                      {(primaryAddressLineOneConfigs?.value ||
                        primaryAddressLineTwoConfigs?.value ||
                        primarySelectedCityConfigs?.value ||
                        primaryProvinceConfigs?.value ||
                        primaryPostalCodeConfigs?.value ||
                        primarySelectedCountryConfigs?.value) && (
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <StarIcon fontSize="small" color="primary" />
                        </div>
                      )}
                      <div style={{ display: 'grid', gridTemplateColumns: 'auto 1fr', gap: '1px' }}>
                        <div style={{ display: 'flex', flexDirection: 'column' }}>
                          <div style={{ marginBottom: '1rem' }}>
                            <Typography>
                              {primaryAddressLineOneConfigs?.value}
                              {primaryAddressLineTwoConfigs?.value && `, ${primaryAddressLineTwoConfigs?.value}`}
                            </Typography>
                            <Typography>
                              {primarySelectedCityConfigs?.value}
                              {primarySelectedCityConfigs?.value &&
                                primaryProvinceConfigs?.value &&
                                `, ${primaryProvinceConfigs?.value}`}
                              {(primarySelectedCityConfigs?.value || primaryProvinceConfigs?.value) &&
                                primaryPostalCodeConfigs?.value &&
                                `, ${primaryPostalCodeConfigs?.value}`}
                            </Typography>
                            <Typography>{primarySelectedCountryConfigs?.value}</Typography>
                          </div>
                          {addressConfigs?.multiple && nonPrimaryAddressConfigs?.length > 0 && (
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                              {nonPrimaryAddressConfigs
                                .slice(0, showMoreAddresses ? nonPrimaryAddressConfigs?.length : 1)
                                .map((addressConfig, index) => (
                                  <Box key={index}>
                                    <Typography>
                                      {addressConfig.address1}
                                      {addressConfig.address2 && `, ${addressConfig.address2}`}
                                    </Typography>
                                    <Typography>
                                      {addressConfig.city}
                                      {addressConfig.city && addressConfig.province && `, ${addressConfig.province}`}
                                      {(addressConfig.city || addressConfig.province) &&
                                        addressConfig.postalCode &&
                                        `, ${addressConfig.postalCode}`}
                                    </Typography>
                                    <Typography>{addressConfig.country}</Typography>
                                  </Box>
                                ))}
                              {nonPrimaryAddressConfigs?.length > 1 && (
                                <Link
                                  onClick={() => setShowMoreAddresses(!showMoreAddresses)}
                                  sx={{ cursor: 'pointer', color: 'primary.main', textDecoration: 'underline' }}
                                >
                                  {showMoreAddresses ? 'Show Less Addresses' : 'Show More Addresses'}
                                </Link>
                              )}
                            </Box>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </Box>
            )}
            {editMode && <CreateAndEditClientComponent id={clientId} closeEditMode={closeEditMode} />}
          </Collapse>
        </Box>
      </Box>
    </ThemeProvider>
  );
}
