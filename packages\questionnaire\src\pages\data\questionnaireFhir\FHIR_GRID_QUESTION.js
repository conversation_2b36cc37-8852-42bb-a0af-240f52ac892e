export const FHIR_GRID_QUESTION = {
  resourceType: 'Questionnaire',
  id: 'c12d9eb7-bf40-43e4-9b59-d021b95b8280',
  extension: [
    {
      url: 'Questionnaire/display-dial',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-description',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-score',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-score-category',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-title',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'Questionnaire/question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/trendable',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: 'c12d9eb7-bf40-43e4-9b59-d021b95b8280',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 0,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'default',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'default-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'X',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  identifier: [
    {
      system: 'urn:uuid',
      value: 'c12d9eb7-bf40-43e4-9b59-d021b95b8280',
    },
  ],
  name: 'Multipage grid question',
  title: 'Multipage grid question title',
  status: 'active',
  date: '2022-11-24T03:42:49-06:00',
  publisher: 'cambian',
  description: 'Multipage grid Question description',
  item: [
    {
      id: 'group-639893',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      linkId: '1647245802',
      text: 'Page 1',
      type: 'group',
      item: [
        {
          id: 'complex-639894',
          extension: [
            {
              url: 'Questionnaire/Item/complex-data-type',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/complex-value-type-name',
              valueString: 'Complex Type Hlywg4c',
            },
            {
              url: 'Questionnaire/Item/complex-value-type-id',
              valueInteger: 639867,
            },
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 639874,
            },
            {
              url: 'Questionnaire/Item/default-number-rows',
              valueInteger: 5,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '1647262497',
          text: 'Q1. Data grid question',
          type: 'group',
          required: false,
          repeats: true,
          item: [
            {
              id: '639867',
              linkId: '1648855996',
              type: 'group',
              item: [
                {
                  id: '639868',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                  ],
                  linkId: '1648912470',
                  text: 'Text',
                  type: 'text',
                  required: false,
                },
                {
                  id: '639870',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1656107663',
                  text: 'Numeric',
                  type: 'decimal',
                  required: false,
                },
                {
                  id: '639872',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 2,
                    },
                  ],
                  linkId: '1656511950',
                  text: 'Date',
                  type: 'dateTime',
                  required: false,
                },
              ],
            },
          ],
        },
        {
          id: '639896',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5520,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: '1684335538',
          text: 'Q2. What is your name?',
          type: 'text',
          required: false,
        },
      ],
    },
    {
      id: 'group-639899',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 2,
        },
      ],
      linkId: '1685544383',
      text: 'Page 2',
      type: 'group',
      item: [
        {
          id: '639900',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          linkId: '1685555732',
          text: 'Q1. How are you feeling today?',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '639901',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'Good',
              },
            },
            {
              valueCoding: {
                id: '639903',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'Not good not bad',
              },
            },
            {
              valueCoding: {
                id: '639905',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
                code: '3',
                display: 'Bad',
              },
            },
          ],
        },
        {
          id: 'complex-639909',
          extension: [
            {
              url: 'Questionnaire/Item/complex-data-type',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/complex-value-type-name',
              valueString: 'Complex Type 2jm6ZRI',
            },
            {
              url: 'Questionnaire/Item/complex-value-type-id',
              valueInteger: 639875,
            },
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 639882,
            },
            {
              url: 'Questionnaire/Item/default-number-rows',
              valueInteger: 5,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          linkId: '1686595975',
          text: 'Q2. Anyone in your family has been diagnosed with cancer',
          type: 'group',
          required: false,
          repeats: true,
          item: [
            {
              id: '639875',
              linkId: '1686620356',
              type: 'group',
              item: [
                {
                  id: '639876',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                  ],
                  linkId: '1686628461',
                  text: 'Name',
                  type: 'text',
                  required: false,
                },
                {
                  id: '639878',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 1,
                    },
                  ],
                  linkId: '1686687723',
                  text: 'Age',
                  type: 'decimal',
                  required: false,
                },
                {
                  id: '639880',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                  ],
                  linkId: '1686709513',
                  text: 'Treatment Recieved',
                  type: 'text',
                  required: false,
                },
              ],
            },
          ],
        },
      ],
    },
    {
      id: 'group-639912',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 3,
        },
      ],
      linkId: '1691453525',
      text: 'Page 3',
      type: 'group',
      item: [
        {
          id: '639913',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 3,
            },
          ],
          linkId: '1691461940',
          text: 'Q1. Tick all that apply.',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '639914',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'I am on medication',
              },
            },
            {
              valueCoding: {
                id: '639916',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'I do regular excersie',
              },
            },
            {
              valueCoding: {
                id: '639918',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
                code: '3',
                display: 'I have a chronic disease',
              },
            },
          ],
        },
        {
          id: '639922',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 3,
            },
          ],
          linkId: '1692459093',
          text: 'Q2. Have you ever thought to harm yourself?',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '639923',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'Yes',
              },
            },
            {
              valueCoding: {
                id: '639925',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'No',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-639930',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 4,
        },
      ],
      linkId: '1692528568',
      text: 'Page 4',
      type: 'group',
      item: [
        {
          id: 'complex-639931',
          extension: [
            {
              url: 'Questionnaire/Item/complex-data-type',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/complex-value-type-name',
              valueString: 'Complex Type akKPELC',
            },
            {
              url: 'Questionnaire/Item/complex-value-type-id',
              valueInteger: 639883,
            },
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 639888,
            },
            {
              url: 'Questionnaire/Item/default-number-rows',
              valueInteger: 5,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 4,
            },
          ],
          linkId: '1692535201',
          text: 'Q1. Please list all the major treatments you have received.',
          type: 'group',
          required: false,
          repeats: true,
          item: [
            {
              id: '639883',
              linkId: '1692557319',
              type: 'group',
              item: [
                {
                  id: '639884',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                  ],
                  linkId: '1692564615',
                  text: 'Treatment name',
                  type: 'text',
                  required: false,
                },
                {
                  id: '639886',
                  extension: [
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Questionnaire/Item/complex-value-attribute-type',
                      valueInteger: 3,
                    },
                  ],
                  linkId: '1692604591',
                  text: 'Age at which treatment received',
                  type: 'text',
                  required: false,
                },
              ],
            },
          ],
        },
      ],
    },
  ],
};
