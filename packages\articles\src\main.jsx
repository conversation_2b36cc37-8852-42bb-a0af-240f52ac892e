import React from 'react';
import { CambianTheme } from '@cambianrepo/ui';
import { ThemeProvider } from '@mui/material/styles';
import { SnackbarProvider } from 'notistack';
import { CssBaseline, createTheme } from '@mui/material';
import ReactDOM from 'react-dom/client';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { ErrorOutline, InfoOutlined, TaskAlt, WarningAmberOutlined } from '@mui/icons-material';

const root = ReactDOM.createRoot(document.getElementById('root'));
const extendedTheme = createTheme(CambianTheme, {
  typography: {
    fontFamily: '"__Inter_d65c78", "Inter", "Helvetica", "Arial", sans-serif',
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          fontFamily: '"__Inter_d65c78", "Inter", "Helvetica", "Arial", sans-serif',
        },
      },
    },
  },
});
const snackbarIconVariants = {
  success: <TaskAlt fontSize="small" />,
  error: <ErrorOutline fontSize="small" />,
  info: <InfoOutlined fontSize="small" />,
  warning: <WarningAmberOutlined fontSize="small" />,
};

root.render(
  <React.StrictMode>
    <ThemeProvider theme={extendedTheme}>
      <SnackbarProvider iconVariant={snackbarIconVariants} maxSnack={3}>
        <CssBaseline />
        <App />
      </SnackbarProvider>
    </ThemeProvider>
  </React.StrictMode>,
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
