import React from 'react';
import { styled } from '@mui/material/styles';
import Tooltip, { tooltipClasses } from '@mui/material/Tooltip';

const ReusableLightTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} classes={{ popper: className }} arrow placement="right" />
))(({ theme }) => ({
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: theme.palette.common.white,
    color: 'rgba(0, 0, 0, 0.87)',
    boxShadow: theme.shadows[1],
    fontSize: 11,
  },
  [`& .${tooltipClasses.arrow}`]: {
    '&:before': {
      border: `1px solid ${theme.palette.common.white}`,
      backgroundColor: 'white',
      boxShadow: '2px 2px 4px rgba(0, 0, 0, 0.1)',
    },
  },
}));

export const LightTooltip = ({ title, children }) => {
  return <ReusableLightTooltip title={title}>{children}</ReusableLightTooltip>;
};
