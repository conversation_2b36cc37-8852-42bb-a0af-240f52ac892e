import React from 'react';
import { Route, Routes, useLocation, useNavigate } from 'react-router-dom';
import NoMatch from './NoMatch';
import { Button, Stack } from '@mui/material';
import { QuestionnaireFhirTestPage } from './pages/QuestionnaireFhirTestPage';
import { QuestionnaireFhirReportTestPage } from './pages/QuestionnaireFhirReportTestPage';

function ComponentPlayground() {
  const navigate = useNavigate();
  const location = useLocation();

  if (location.pathname === '/') {
    navigate('/questionnaireFhir');
  }

  return (
    <>
      <Stack direction="row" alignItems="left" useFlexGap flexWrap="wrap" sx={{ gap: 1 }}>
        <Button variant="contained" onClick={() => navigate('questionnaireFhir')}>
          Questionnaire Fhir
        </Button>
        <Button variant="contained" onClick={() => navigate('questionnaireFhirReports')}>
          Questionnaire Fhir Reports
        </Button>
      </Stack>
      <Routes>
        <Route exact path="/questionnaireFhir" element={<QuestionnaireFhirTestPage />} />
        <Route exact path="/questionnaireFhirReports" element={<QuestionnaireFhirReportTestPage />} />
        <Route path="*" element={<NoMatch />} />
      </Routes>
    </>
  );
}

export default ComponentPlayground;
