export const FHIR_MULTIPLE_CHOICE_OTHER = {
  resourceType: 'Questionnaire',
  id: '8e103584-2ab6-4929-8ada-92c24a8189e9',
  extension: [
    {
      url: 'Questionnaire/display-dial',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-description',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-score',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-score-category',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-title',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'Questionnaire/question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/trendable',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/result-page',
      valueString:
        '{"sections":[{"type":"Label","displayName":"<p>Report of New Questionnaire</p>","showInReport":true,"htmlText":"","fields":[],"variables":[]},{"name":"DateTime","displayName":"","type":"Date","showInReport":false,"htmlText":"","fields":[{"format":"YYYY-MM-DD HH:mm:ss","name":"","displayName":"","sequence":1,"showInReport":true}],"variables":[]},{"type":"Questions & Answers","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[]},{"type":"Demographics","displayName":"","showInReport":true,"htmlText":"","fields":[{"name":"firstName","displayName":"First Name","sequence":1,"showInReport":true,"code":"FIRST_NAME","format":""},{"name":"lastName","displayName":"Last Name","sequence":2,"showInReport":true,"code":"LAST_NAME","format":""},{"name":"phn","displayName":"PHN","sequence":3,"showInReport":true,"code":"PHN","format":""},{"name":"dateOfBirth","displayName":"Date of Birth","sequence":4,"showInReport":true,"code":"DOB","format":""},{"name":"gender","displayName":"Gender","sequence":5,"showInReport":true,"code":"GENDER","format":""},{"name":"email","displayName":"Email","sequence":6,"showInReport":true,"code":"EMAIL","format":""},{"name":"participantId","displayName":"Participant ID","sequence":7,"showInReport":true,"code":"PARTICIPANT_ID","format":""}],"variables":[]}]}',
    },
    {
      url: 'Questionnaire/htmltemplate-base64',
      valueString:
        "<h1>{Questionnaire.title}</h1>\r\n<p> Date: {QuestionnaireResponse.completionDate:format(YYYY-MM-DD HH:mm:ss)} </p>\r\n<p> This is a default variable: {QuestionnaireResponse.variable.default} </p>\r\n<p> This is a list of items and responses </p>\r\n{QuestionnaireResponse.itemsAndResponses}\r\n<p style='color:gray; font-size:11px;'>{Questionnaire.description}</p>",
    },
    {
      url: 'Questionnaire/question-identifier-prefix',
      valueString: 'Item',
    },
    {
      url: 'Questionnaire/question-identifier-next-sequence',
      valueInteger: 7,
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '8e103584-2ab6-4929-8ada-92c24a8189e9',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 0,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'default',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'default-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'X',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  identifier: [
    {
      use: 'official',
      system: 'http://www.cambian.com/questionnaire/identifier',
      value: '7d97b794-4b68-4d1c-b46b-cf69682dddc3',
      period: {
        start: '2023-06-05T07:04:27-05:00',
      },
    },
    {
      use: 'usual',
      system: 'urn:uuid',
      value: '8e103584-2ab6-4929-8ada-92c24a8189e9',
      period: {
        start: '2023-06-05T07:04:27-05:00',
      },
    },
  ],
  name: 'New Questionnaire',
  title: 'Title of The New Questionnaire ',
  status: 'active',
  date: '2023-08-04T00:35:11-05:00',
  publisher: 'cambian',
  description:
    'Canadian EQ-5D-5L Time Trade-off-derived value set. This assessment uses the Canadian preference-weighted index score using self-reported EQ-5D data.',
  item: [
    {
      id: 'group-732899',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      linkId: '8813692709',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '732900',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'Item1',
          text: 'MOBILITY \nPlease select the ONE button that best describes your health TODAY',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '732901',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'I have no problems in walking about',
              },
            },
            {
              valueCoding: {
                id: '732903',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'I am unable to walk about',
              },
            },
            {
              valueCoding: {
                id: '732905',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
                code: '3',
                display: 'I have moderate problems in walking about',
              },
            },
            {
              valueCoding: {
                id: '732907',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 4,
                  },
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:732907,question:if others Please specify',
                  },
                ],
                code: '4',
                display: 'if others Please specify',
              },
            },
          ],
          item: [
            {
              id: 'additional-text-732907',
              type: 'text',
            },
          ],
        },
        {
          id: '742860',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'Item2',
          text: 'radio',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '743781',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: '1',
              },
            },
            {
              valueCoding: {
                id: '743783',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: '2',
              },
            },
            {
              valueCoding: {
                id: '743785',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
                code: '3',
                display: '3',
              },
            },
            {
              valueCoding: {
                id: '743787',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 4,
                  },
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743787,question:4',
                  },
                ],
                code: '4',
                display: '4',
              },
            },
          ],
          item: [
            {
              id: 'additional-text-743787',
              type: 'text',
            },
          ],
        },
        {
          id: '743794',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'Item3',
          text: 'sdge',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '743795',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: '1',
              },
            },
            {
              valueCoding: {
                id: '743797',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: '2',
              },
            },
            {
              valueCoding: {
                id: '743799',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743799,question:3',
                  },
                ],
                code: '3',
                display: '3',
              },
            },
          ],
          item: [
            {
              id: 'additional-text-743799',
              type: 'text',
            },
          ],
        },
      ],
    },
    {
      id: 'group-743805',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 2,
        },
      ],
      linkId: '392620866',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '743808',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          linkId: 'Item4',
          text: 'MOBILITY \nPlease select the ONE button that best describes your health TODAY',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '743811',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: 'I have no problems in walking about',
              },
            },
            {
              valueCoding: {
                id: '743813',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: 'I am unable to walk about',
              },
            },
            {
              valueCoding: {
                id: '743815',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
                code: '3',
                display: 'I have moderate problems in walking about',
              },
            },
            {
              valueCoding: {
                id: '743817',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 4,
                  },
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743817,question:if others Please specify',
                  },
                ],
                code: '4',
                display: 'if others Please specify',
              },
            },
          ],
          item: [
            {
              id: 'additional-text-743817',
              type: 'text',
            },
          ],
        },
        {
          id: '743821',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          linkId: 'Item5',
          text: 'radio',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '743824',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: '1',
              },
            },
            {
              valueCoding: {
                id: '743826',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: '2',
              },
            },
            {
              valueCoding: {
                id: '743828',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                ],
                code: '3',
                display: '3',
              },
            },
            {
              valueCoding: {
                id: '743830',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 4,
                  },
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743830,question:4',
                  },
                ],
                code: '4',
                display: '4',
              },
            },
          ],
          item: [
            {
              id: 'additional-text-743830',
              type: 'text',
            },
          ],
        },
        {
          id: '743834',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5514,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 2,
            },
          ],
          linkId: 'Item6',
          text: 'sdge',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '743837',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 1,
                  },
                ],
                code: '1',
                display: '1',
              },
            },
            {
              valueCoding: {
                id: '743839',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 2,
                  },
                ],
                code: '2',
                display: '2',
              },
            },
            {
              valueCoding: {
                id: '743841',
                extension: [
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/sequence-value',
                    valueInteger: 3,
                  },
                  {
                    url: 'Questionnaire/Item/AnswerOption/ValueCoding/other-option',
                    valueString: 'id:743841,question:3',
                  },
                ],
                code: '3',
                display: '3',
              },
            },
          ],
          item: [
            {
              id: 'additional-text-743841',
              type: 'text',
            },
          ],
        },
      ],
    },
  ],
};
