export const HTML_TEMPLATE_REPORT = {
  resourceType: 'Questionnaire',
  id: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
  extension: [
    {
      url: 'Questionnaire/display-dial',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-description',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-score',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-score-category',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-title',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'Questionnaire/question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/trendable',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/result-page',
      valueString:
        '{"sections":[{"type":"Label","displayName":"<p style=\\"text-align: center; \\"><span style=\\"font-size: 36px;\\"><b>Cardiovascular Risk Assessment</b></span></p>","showInReport":true,"htmlText":"","fields":[],"variables":[]},{"name":"DateTime","displayName":"<p style=\\"text-align: center;\\"><span style=\\"font-size: 18px;\\">﻿</span><span style=\\"font-size: 18px;\\">﻿</span><span style=\\"font-size: 18px;\\">Date:</span></p>","type":"Date","showInReport":true,"htmlText":"<p style=\\"text-align: center;\\"><span style=\\"font-size: 18px;\\">﻿</span><span style=\\"font-size: 18px;\\">﻿</span><span style=\\"font-size: 18px;\\">Date:</span><span id=\\"dateString\\" style=\\"font-size: 18px; color: rgb(57, 132, 198);\\">[COMPLETION_DATE]</span></p>","fields":[{"format":"YYYY-MM-DD HH:mm","name":"","displayName":"<span id=\\"dateString\\" style=\\"font-size: 18px; color: rgb(57, 132, 198);\\">[COMPLETION_DATE]</span>","sequence":1,"showInReport":true}],"variables":[]},{"type":"Variables","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[{"variableName":"Cardiovascular Risk (10-years)","variable":"cv_percentage","showInReport":true,"mode":"view","variableDetail":{"displayName":""},"formats":[{"formatName":"currency","formatValue":"$","showInReport":false},{"formatName":"percent","formatValue":"%","showInReport":true},{"formatName":"decimalPlace","formatValue":"0","showInReport":true}],"sequence":0},{"variableName":"Heart Age","variable":"heart_validation","showInReport":true,"mode":"view","variableDetail":{"displayName":""},"formats":[{"formatName":"currency","formatValue":"$","showInReport":false},{"formatName":"percent","formatValue":"%","showInReport":false},{"formatName":"decimalPlace","formatValue":0,"showInReport":true}],"sequence":0}]},{"type":"Questions & Answers","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[]}]}',
    },
    {
      url: 'Questionnaire/htmltemplate-base64',
      valueString:
        '<p></p>\n<h1><b>{Questionnaire.title}</b></h1>\n<p> Date: {QuestionnaireResponse.completionDate:format(YYYY-MM-DD hh:mm:ss a)}</p>\n\n<table width="100%">\n  <tbody>\n    <tr>\n      <td style="text-align: center;">\n        <img src="data:image/gif;base64,R0lGODlhMwAsAFUAACH5BAkAACkALAAAAAAzACwAhf78+PLhuPXoxvPju/PkwOTAaOPAZeK9YPLgs/z58evQjeXDcOjKgOvRkPDbqPXoye7YoO3UmPny4ejIe+rOiObFdOfIeu3WnPz47fHesOK6Wvjt1PDdrfnw2/v16PDap+/aqPju1ufHePfr0Pjv2Pbqy+nMhebEc+S/Z////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAb/QIBwKPQQIhSRaNLIbIhQoieAVDIzpKh2KIkUDmDDF1xgELaA7gJMBmvKDzQR8S0wPw4HCLk+UCRRGXUTERAZeXxgCoBbCQ0HBhQOBJSVlFQnBwsjQ44HZRmWlgENCwYLHlsKnxCirgQIDJ9xCRSsr5UCsAwLjFARmg66lwIBxcWWFGIEygvClcYBxNLFAR1RD5+T08fR3QSrXwsIl9ze5woXnRMoDbrn3fGVFAULofHw57ALDEMBBxOkEYA3oGDBbgOKfQg1EB++hNEQxBGyKkI8gwcTGvxGwCFBiMgqJRBSoAA5bweNQQSpDyXGYilhHoOwIM4pjzMZINA4IN/F/4sxBUbQEAHAAQY4AwSoYMBdAJBBNz5l6ZDABwP9sAooqNIbBAMGJuDMmPRYT2QOsALQgPSjAAZiTHpkCfXl1GME0mYV65NDSVMQzMaCQJXg1sIEIBxoAOCUUoS6FBgw0cCAgoYDJFfIF/Rp1QYaPgCwUCAwSgEIFhQAcTWgMQQVSl4wu3VrNKUuBwyccCAOBA0UugVAQMqAheGqOQgQ8LWA8YjEhT+dGhOEgRMYAHQoKawYadUGLnScEN6YiTJMIcCOzcDz8BAhop+TBWEIsOOoSce2gEBXZaR0mNRAARaQVxIDBj01EmoxXXCKLxiYogB0CPRnjAP1BOCgBfuAVf8BB8RBFAAjEihVUF7OBQDFCM5NSNttsTkAlzsEVFbAbDz1R8QDj4HgXFFRBACWAjidx0sBysHCBEpKZdeJUn4thoYALfrk4IFJKfUEAA4A2UEAaTUwEhpCWhYUaiWFl6WKACRQgAapJDCQA3IMQaVl+ZBXwU4qpdSTNUK4eQCJ0tTpz5BmGSOZGUBVM1GbX6QiRAkIZGEoAAR84WJECnDQ0Dl/OgnpoE8GIGqdBAzpk0vFILBloF+MKUQIATxqaKZ4JiViALJOKkAUsPhiaJlEusXViJemUWiyAFB5gItSCWfrEB1wgo2rzGKKaJ9dNanFm70GqlS4qFZ5kVIhbIFzAAhbkCBRtpi2eC6b8GKKbLYCGPCsN8RtkAAGCQQs8MAEC7yBMfWmapl0Aw2E28MQQ4yAtfA6u2lPCORRKwEPdORxxyB7LEAJ5CarsAK6CeDACQUwVu/LdspLU1Mlw8yswrxIafPOzTq3WM08M0ulyzwHAQA7" data-filename="CV-risk.gif" style="width: 51px;"><br>\n        <br>{QuestionnaireResponse.variable.cv_percentage:round(2)} %\n        <br>CV Risk\n      </td>\n      <td style="text-align: center;">\n        <img src="data:image/gif;base64,R0lGODlhOAAwAGYAACH5BAkAAFUALAAAAAA4ADAAhvHesP38+PPgsf7wyv/zzvrouf/z0Pnmtvz48PPiufjlsPnw2NiYAPry4fju09ihGt6qJvTmw9meDdSYAvXoyProtvDaqPvrwOnNie7QgP/66/jv2OG4U/DQgNifEv7vxvDapevRkNqoK/jiqfr16Pbgo+O5T+TBaOCxPOfEct+uN9+uNNuqMeCfAPPZk//44dmjIffr0PXnxtWZCfTame7Qf9ysNtaeEvvsv/vuy/DShe3Pf/Xdnu7YouTAZ+a8WOnFaNqhFujGbujCYvLXkPDUjfbfofDcq+3Wn+3VmezKc+vKc+nNhvjlr+a/XNqmLNylHdOWAOC0SOCyRt6sMf///wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAf/gAGCg4SFhoeIiYqLjI2Oj4MIC5MLDZCFDZQLCJeDGhQJAgWjBQcCEQaOCBsJAAqkBQIJOZyqFKICAgC6ALu4MYsOCQUKvL25AgcJEbWKGwexx73T06LMhwjDxtLUogmWiQ6xud3U1AoHJIUkB6bm79UFDoji5NXw3d+DDQkH+Pi5CiwwhICcrm3b3glQMHCBu3//cqkjdMEdQojVDlDwhxHigQuEGhQwd7Ejx44QC4ALEMFYSZQwMSqQIUiDvXs4Y+okmYCTyHI5dwqdpjLAhgoKgQ4VemAgBQVKdy2dqpHlyaAJp6Ks6uDqS606D8xbMJJkVLAdiza4+RUtSgXg/0J9zeoWnqxBBspKDVo3ZSpBBef2hbiwWYCu3BIPxifWUEWsQ0Gk4ACDgeUHUlIgeXeAJsEExc7CBHFCBAMPD1Kn9jABBhALACxYaGeYULarijtiYBHFAwcaCoIrqADihwcJU1LYeECkdiEEEU7ShYjhCQMTRi4MMDCgu3cQKiaIlxCiUQS9e3VXBqKd+/b3BLp3CGJZQhJHOUZOhwfCBoMhF3An4AAjKFFCfDyoYBkDEvTwSAzR7EfNCR6oMMJ7A+oAQQ0DEJCBCKqJYAEk9XQkgAUszNBBdwPGlwEEGXT3QQEVEPMXidEQFsINLFw4oAHxabhDd/F5N1AnDoRmV/8vGFxXQIcEEMCdizBCKeV2N16ywU0KNWlCBSx2uB0BHUAwpJjxcdfJPrIkJQATLXwZ5ZzxlYCCBCvOKWCWnbCD2zEYTIDChfANQAMVLaxQApRo8tmJBv0A1YMIN7hA5wBFQJDogfFdqsGaz4FGkgU/zMDBk9vxAMEEHDRBJKPxObfmbfcIEAIEeHrnAgpL4EBnp1K+ACo20JTjgwQPuCDmBx+8Cux2wyJCKy8CHHEsFDU0a+WcUBoga7SCRADVXhacEMQEK2TQbKeNfgpuIhsdlEsFIXAwwwRO+PqrsO8uAuFBBVAgiAwcCKFdmi98228hDlSw0AYLL0wWxBEv7O4DwoEAADs=" data-filename="HeartAge.gif" style="width: 56px;"><br>\n        <br>{QuestionnaireResponse.variable.heart_validation:round(3)} years<br>Heart age\n      </td>\n    </tr>\n  </tbody>\n</table>\n<br>\n<p></p>\n<p><br>\n</p>\n<table width="100%">\n  <tbody>\n    <tr data-visible="{QuestionnaireResponse.variable.zero:boolean()}">\n      <td>Age:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS2} years</td>\n    </tr>\n    <tr>\n      <td>Sex:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1}</td>\n    </tr>\n    <tr>\n      <td>Male</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1.score:compare(eq,3)}</td>\n    </tr>\n    <tr>\n      <td>Female:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS1:equal(Female)}</td>\n    </tr>\n    <tr>\n      <td>Systolic blood pressure (weekly average):</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS3} mmHg</td>\n    </tr>\n    <tr>\n      <td>Blood pressure treated with medication:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS4}</td>\n    </tr>\n    <tr>\n      <td>Smoker:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS5}</td>\n    </tr>\n    <tr>\n      <td>Diabetes:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS6}</td>\n    </tr>\n    <tr>\n      <td>Assessment type:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS7}</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS8.display}">\n      <td>HDL:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS8} mmol/L</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS9.display}">\n      <td>Total cholesterol:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS9} mmol/L</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS10.display}">\n      <td>Height:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS10} cm</td>\n    </tr>\n    <tr data-visible="{QuestionnaireResponse.item.QUS1QGS11.display}">\n      <td>Weight:</td>\n      <td>{QuestionnaireResponse.item.QUS1QGS11} kg</td>\n    </tr>\n  </tbody>\n</table>\n\n<p style="color:gray; font-size:11px;"><br></p><p style="color:gray; font-size:11px;"><br></p>\n<p style="color:gray; font-size:11px;">{Questionnaire.description}</p>\n\n\n<p style="text-align: left;"><br></p><p style="text-align: left;"><br></p><p style="text-align: left;"><br></p><p style="text-align: left;">{QuestionnaireResponse.itemsAndResponses}<br></p>',
    },
    {
      url: 'Questionnaire/html-report-active',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/question-identifier-prefix',
      valueString: 'Item',
    },
    {
      url: 'Questionnaire/question-identifier-next-sequence',
      valueInteger: 1,
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 0,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'bl_treated',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_treated-F4',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1.93303',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS4}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_treated-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '2.76157',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS4}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_treated-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1.99881',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS4}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_treated-F3',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '2.82263',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS4}==1',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 2,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'bl_smoker',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_smoker-F3',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0.65451',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_smoker-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS5}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_smoker-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0.52873',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==1',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 3,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'bl_diabetes',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_diabetes-F3',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0.57367',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_diabetes-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS6}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bl_diabetes-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0.69154',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==1',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 4,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'bB_treated',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_treated-F3',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '2.88267',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS4}==1 && #{QUS1QGS1}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_treated-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1.92672',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS4}==1 && #{QUS1QGS1}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_treated-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1.85508',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS4}==2 && #{QUS1QGS1}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_treated-F4',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '2.81291',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS4}==2 && #{QUS1QGS1}==1',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 5,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'bB_smoker',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_smoker-F3',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0.70953',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_smoker-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0.61868',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS5}==1 && #{QUS1QGS1}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_smoker-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS5}==2',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 6,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'bB_diabetes',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_diabetes-F3',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS6}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_diabetes-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0.77763',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bB_diabetes-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0.53160',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS6}==1 && #{QUS1QGS1}==2',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 7,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'bmi',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'bmi-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '#{QUS1QGS11} / ((#{QUS1QGS10}/100) * (#{QUS1QGS10}/100))',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS7}==2',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 8,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'sum_lipid',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'sum_lipid-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString:
                    '2.32888 * log(#{QUS1QGS2}) + 1.20904 * log(#{QUS1QGS9}*38.67) - 0.70833 * log(#{QUS1QGS8}*38.67) + #{bl_treated} * log(#{QUS1QGS3}) + #{bl_smoker} + #{bl_diabetes}',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'sum_lipid-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString:
                    '3.06117 * log(#{QUS1QGS2}) + 1.12370 * log(#{QUS1QGS9}*38.67) - 0.93263 * log(#{QUS1QGS8}*38.67) + #{bl_treated} * log(#{QUS1QGS3}) + #{bl_smoker} + #{bl_diabetes}',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 9,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'sum_bmi',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'sum_bmi-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString:
                    '3.11296 * log(#{QUS1QGS2}) + 0.79277 * log(#{bmi}) + #{bB_treated} * log(#{QUS1QGS3}) + #{bB_smoker} + #{bB_diabetes}',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==2  && #{QUS1QGS7}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'sum_bmi-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString:
                    '2.72107 * log(#{QUS1QGS2}) + 0.51125 * log(#{bmi}) + #{bB_treated} * log(#{QUS1QGS3}) + #{bB_smoker} + #{bB_diabetes}',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 10,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'cv',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'cv-F3',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1 - pow(0.95012,(exp(#{sum_lipid} - 26.1931)))',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'cv-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1 - pow(0.88936,(exp(#{sum_lipid} - 23.9802)))',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'cv-F4',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1 - pow(0.94833,(exp(#{sum_bmi} - 26.0145)))',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'cv-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '1 - pow(0.88431,(exp(#{sum_bmi} - 23.9388)))',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==2',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 11,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'cv_percentage',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'cv_percentage-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '#{cv}*100',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: '1XXXXXXXXXX',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 12,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'heart_age',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'heart_age-F3',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: 'pow(-log(1-#{cv}),0.3267) * 114.2579',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'heart_age-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: 'pow(-log(1-#{cv}),0.3675) * 158.1102',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==2',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'heart_age-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: 'pow(-log(1-#{cv}),0.4294) * 192.4820',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==1 && #{QUS1QGS7}==1',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'heart_age-F5',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: 'pow(-log(1-#{cv}),0.3212) * 109.1966',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{QUS1QGS1}==2 && #{QUS1QGS7}==2',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 13,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'heart_validation',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'heart_validation-F2',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '86',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{heart_age}>85',
                },
              ],
            },
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'heart_validation-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '#{heart_age}',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'VAR_COMPARE$#{heart_age}<=85',
                },
              ],
            },
          ],
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 14,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'zero',
        },
        {
          url: 'Questionnaire/list-of-formula-definitions',
          extension: [
            {
              url: 'Questionnaire/set-of-api-formula',
              extension: [
                {
                  url: 'Questionnaire/formula-name',
                  valueString: 'NewVariable-F1',
                },
                {
                  url: 'Questionnaire/mathematical-expression',
                  valueString: '0',
                },
                {
                  url: 'Questionnaire/selection-rule',
                  valueString: 'XXXXXXXXXXX',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  identifier: [
    {
      system: 'urn:uuid',
      value: '87eb1fb8-edcd-468a-8a1f-767d85a0e37f',
    },
  ],
  name: 'CV_risk_2023 15 March',
  title: 'Cardiovascular Risk Assessment',
  status: 'active',
  date: '2023-03-15T14:41:44+00:00',
  publisher: 'cambian',
  description:
    "The cardiovascular assessment estimates your cardiovascular (CV) risk and heart age. The CV risk is the chance of having a heart attack or stroke in the next 10 years. The heart age gives you an idea of how healthy your heart is compared to your age.  If you don't know your cholesterol levels you can use the body mass index (BMI) which is based on weight and height.",
  item: [
    {
      id: 'group-567175',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
        {
          url: 'Questionnaire/Item/question-group-sequence',
          valueInteger: 1,
        },
      ],
      linkId: 'QUS1QGS0',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '567177',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS1',
          text: 'Sex assigned at birth',
          type: 'choice',
          required: true,
          answerOption: [
            {
              valueCoding: {
                id: '567181',
                code: '1',
                display: 'Female',
              },
            },
            {
              valueCoding: {
                id: '567183',
                code: '2',
                display: 'Male',
              },
            },
          ],
        },
        {
          id: '567187',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 2,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS2',
          text: 'Age (years)',
          type: 'decimal',
          required: true,
        },
        {
          id: '567193',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 3,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS3',
          text: 'Systolic blood pressure weekly average (mmHg)',
          type: 'decimal',
          required: true,
        },
        {
          id: '567199',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 4,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS4',
          text: 'Blood pressure treated with medication',
          type: 'choice',
          required: true,
          answerOption: [
            {
              valueCoding: {
                id: '567203',
                code: '1',
                display: 'Yes',
              },
            },
            {
              valueCoding: {
                id: '567205',
                code: '2',
                display: 'No',
              },
            },
          ],
        },
        {
          id: '567209',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 5,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS5',
          text: 'Smoker',
          type: 'choice',
          required: true,
          answerOption: [
            {
              valueCoding: {
                id: '567213',
                code: '1',
                display: 'Yes',
              },
            },
            {
              valueCoding: {
                id: '567215',
                code: '2',
                display: 'No',
              },
            },
          ],
        },
        {
          id: '567219',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 6,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS6',
          text: 'Diabetes',
          type: 'choice',
          required: true,
          answerOption: [
            {
              valueCoding: {
                id: '567223',
                code: '1',
                display: 'Yes',
              },
            },
            {
              valueCoding: {
                id: '567225',
                code: '2',
                display: 'No',
              },
            },
          ],
        },
        {
          id: '567229',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5515,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 7,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS7',
          text: 'Assessment type',
          type: 'choice',
          required: true,
          answerOption: [
            {
              valueCoding: {
                id: '567233',
                code: '1',
                display: 'Lipids profile',
              },
            },
            {
              valueCoding: {
                id: '567235',
                code: '2',
                display: 'Body mass index (BMI)',
              },
            },
          ],
        },
        {
          id: '567239',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 8,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS8',
          text: 'HDL (mmol/L) ',
          type: 'decimal',
          enableWhen: [
            {
              question: 'QUS1QGS7',
              operator: '=',
              answerString: '1',
            },
          ],
          required: false,
        },
        {
          id: '567246',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 9,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS9',
          text: 'Total cholesterol (mmol/L) ',
          type: 'decimal',
          enableWhen: [
            {
              question: 'QUS1QGS7',
              operator: '=',
              answerString: '1',
            },
          ],
          required: false,
        },
        {
          id: '567253',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 10,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS10',
          text: 'Height (cm)',
          type: 'decimal',
          enableWhen: [
            {
              question: 'QUS1QGS7',
              operator: '=',
              answerString: '2',
            },
          ],
          required: false,
        },
        {
          id: '567260',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-type-id',
              valueInteger: 5513,
            },
            {
              url: 'Questionnaire/Item/min-value',
              valueDecimal: 0,
            },
            {
              url: 'Questionnaire/Item/min-exclusion',
              valueBoolean: true,
            },
            {
              url: 'Questionnaire/Item/max-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/question-in-group-sequence',
              valueInteger: 11,
            },
            {
              url: 'Questionnaire/Item/question-group-sequence',
              valueInteger: 1,
            },
          ],
          linkId: 'QUS1QGS11',
          text: 'Weight (kg)',
          type: 'decimal',
          enableWhen: [
            {
              question: 'QUS1QGS7',
              operator: '=',
              answerString: '2',
            },
          ],
          required: false,
        },
      ],
    },
  ],
};
