{"name": "@cambianrepo/articles-editor", "version": "0.0.17", "publishConfig": {"registry": "https://npm.pkg.github.com/cambianrepo"}, "engines": {"node": ">=20.0.0", "npm": "please use YARN", "yarn": ">= 1.22.18"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "lint": "eslint src/**/*.js", "lint-fix": "eslint src/**/*.js --fix", "rollup": "rollup -c"}, "dependencies": {"@cambianrepo/cambianreact": "2.0.166", "@mui/styles": "^6.2.1", "@mui/utils": "^6.2.1", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.8.3", "react-is": "^19.0.0", "react-qr-code": "^2.0.15", "short-uuid": "^4.2.2", "strip-ansi": "6.0.1", "uuid": "^11.0.3", "vite": "^6.2.1", "web-vitals": "^4.2.4", "yup": "^1.3.3"}, "peerDependencies": {"@date-io/date-fns": "^3.0.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.2.5", "@mui/lab": "^5.0.0-alpha.66", "@mui/material": "^5.15.15", "date-fns": "^3.2.0", "notistack": "^2.0.3", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0"}, "devDependencies": {"@cambianrepo/ui": "0.0.55", "@babel/core": "^7.23.6", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@babel/preset-env": "^7.23.6", "@babel/preset-react": "^7.23.3", "@date-io/date-fns": "^3.0.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^6.2.1", "@mui/lab": "^5.0.0-alpha.66", "@mui/material": "^5.15.15", "@rollup/plugin-alias": "^5.1.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-image": "^3.0.3", "@rollup/plugin-node-resolve": "^15.2.3", "@types/scheduler": "^0.16.8", "date-fns": "^3.2.0", "eslint": "^7.32.0 || ^8.2.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.28.0", "eslint-plugin-react-hooks": "^4.3.0", "husky": "^8.0.3", "lint-staged": "^12.3.4", "notistack": "^2.0.3", "prettier": "3.1.0", "react": "^18.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "rollup": "^2.79.1"}, "lint-staged": {"/src/**/*.{js,jsx}": ["yarn lint --fix", "yarn format"]}, "husky": {"hooks": {"pre-commit": "lint-staged --staged"}}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"], "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "browser": {"crypto": false, "stream": false}}