import React from 'react';
import { Box, Grid } from '@mui/material';

function TwoColumnPage(props) {
  return (
    <Grid container direction="row">
      <Box
        component={Grid}
        item
        xs={0}
        sm={0}
        md={0}
        lg={1}
        xl={1}
        display={{ xs: 'none', sm: 'none', md: 'none', lg: 'none' }}
      />

      <Box
        component={Grid}
        item
        xs={12}
        sm={12}
        md={3}
        lg={3}
        xl={3}
        display={{ xs: 'block' }}
        sx={{
          paddingRight: { md: 1 },
          marginBottom: { xs: 0.5, sm: 1, md: 0 },
        }}
      >
        {props.leftColumn}
      </Box>

      <Box
        component={Grid}
        item
        xs={12}
        sm={12}
        md={9}
        lg={9}
        xl={9}
        display={{ xs: 'block' }}
        sx={{
          paddingLeft: { md: 1 },
          marginTop: { xs: 0.5, sm: 1, md: 0 },
        }}
      >
        {props.rightColumn}
      </Box>

      <Box
        component={Grid}
        item
        xs={0}
        sm={0}
        md={0}
        lg={1}
        xl={1}
        display={{ xs: 'none', sm: 'none', md: 'none', lg: 'block' }}
      />
    </Grid>
  );
}

export default TwoColumnPage;
