import React, { Fragment } from 'react';
import * as QuestionnaireUtility from '../utility/questionnaireUtility';
import { Box, Grid, Alert, Typography } from '@mui/material';
import { CANNOT_LOCATE_STRING } from '../../../Common/constants';
import dataValidation from '../../../Common/Utils/dataValidation';

function Explanation(props) {
  const { question } = props || {};

  // TODO: need to optimise extractExtension function
  let explanationExt =
    (question.question &&
      question.question.extension &&
      question.question.extension.find((ext) => ext.url === 'Item/explanation')) ||
    [];

  let explanationFlagExt = QuestionnaireUtility.extractExtension(question.question.extension, 'Item/explanation-flag');

  let explanation = explanationExt !== null ? explanationExt.valueString : '';

  let explanationFlag = explanationFlagExt !== null ? explanationFlagExt.valueString : '';

  let severity = explanationFlag === 'ERROR' ? 'error' : explanationFlag === 'WARNING' ? 'warning' : 'info';
  // let infoStyle = explanationFlag === 'INFO' ? {backgroundColor:'white', color: 'black', border: 0, pl: 0} : {};

  return (
    <Fragment>
      {explanation && explanation !== CANNOT_LOCATE_STRING && !dataValidation.isDataEmpty(explanation) && (
        <Box sx={{ mt: 1.5, width: '100%' }}>
          <Alert icon={false} severity={severity} sx={{ py: 0 }}>
            <Typography variant="body2" sx={{ py: 0 }} dangerouslySetInnerHTML={{ __html: explanation }} />
          </Alert>
        </Box>
      )}
    </Fragment>
  );
}

export { Explanation };
