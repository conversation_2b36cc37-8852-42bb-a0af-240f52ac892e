{"name": "@cambianrepo/change-password", "publishConfig": {"registry": "https://npm.pkg.github.com/cambianrepo"}, "version": "0.0.1", "type": "module", "engines": {"node": ">=20.0.0", "npm": "please use YARN", "yarn": ">= 1.22.18"}, "scripts": {"lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "rollup": "rollup -c", "format": "prettier --write \"./src/**/*.{js,jsx,css,md,json}\" --config ./.prettierrc"}, "peerDependencies": {"@mui/material": "^5.15.15", "next": "^14.2.4", "react": "^18.2.0"}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"]}