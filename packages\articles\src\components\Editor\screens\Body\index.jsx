import React, { useState, useEffect } from 'react';
import { TextField, Typography, Paper, IconButton, Stack, CircularProgress, Box, FormHelperText } from '@mui/material';
import { Code } from '@mui/icons-material';
import { Loader } from '@/components';
import { modes } from '../../../../containers/CommonConstants';
import { strings } from '../../../../utility/strings';
import { CambianTooltip } from '@cambianrepo/ui';
import { isEmpty, hasMinLength } from '../../../../utility/validation';

export const Body = ({ existingArticleData, body, onBodyChange, formErrors = {} }) => {
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState(modes.editor);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchArticleBody = async () => {
      if (!body && existingArticleData?.bodyPresignedUrl) {
        setLoading(true);
        try {
          const response = await fetch(existingArticleData.bodyPresignedUrl);
          if (!response.ok) {
            throw new Error('Failed to fetch article body');
          }
          const htmlContent = await response.text();
          onBodyChange(htmlContent);
        } catch (error) {
          console.error('Error fetching article body:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchArticleBody();
  }, [existingArticleData, body, onBodyChange]);

  useEffect(() => {
    if (formErrors && Object.keys(formErrors).length > 0 && formErrors.body) {
      setError(formErrors.body);
    }
  }, [formErrors]);

  const handleBodyChange = (value) => {
    onBodyChange(value);
    if (error && value.trim() !== '') {
      setError(null);
    } else {
      validateBody(value);
    }
  };

  const validateBody = (bodyValue) => {
    let bodyError = null;

    if (isEmpty(bodyValue)) {
      bodyError = 'Body is required';
    } else if (!hasMinLength(bodyValue)) {
      bodyError = 'Must be at least 2 characters';
    }

    setError(bodyError);
    return bodyError === null;
  };

  return (
    <>
      <Loader active={loading} message="Loading article content..." />
      <Stack direction="row" justifyContent="space-between" mb={2}>
        <Stack direction="row" alignItems="center">
          <CambianTooltip title={strings.enterYourArticleDataHere}>
            <Typography variant="h3">{strings.body}</Typography>
          </CambianTooltip>
        </Stack>
        <CambianTooltip title={mode === modes.editor ? strings.switchToPreview : strings.switchToEditor}>
          <IconButton onClick={() => setMode(mode === modes.editor ? modes.preview : modes.editor)}>
            <Code
              fontSize="large"
              sx={{
                background: mode === modes.editor ? '#4d76a933' : 'disabled',
                color: mode === modes.editor ? 'primary' : 'disabled',
                border: '1px solid #4D76A9',
                borderRadius: '4px',
                height: '30px',
                width: '40px',
              }}
            />
          </IconButton>
        </CambianTooltip>
      </Stack>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 10 }}>
          <CircularProgress />
        </Box>
      ) : mode === modes.editor ? (
        <Box>
          <TextField
            value={body || ''}
            placeholder={strings.body}
            onChange={(e) => handleBodyChange(e.target.value)}
            multiline
            fullWidth
            rows={20}
            sx={{
              maxWidth: '100%',
              '& .MuiOutlinedInput-root': {
                maxWidth: '100% ',
              },
            }}
            error={Boolean(error)}
          />
          {error && <FormHelperText error>{error}</FormHelperText>}
        </Box>
      ) : (
        <Paper sx={{ p: 2, minHeight: 500 }}>
          <Typography dangerouslySetInnerHTML={{ __html: body || '' }}></Typography>
        </Paper>
      )}
    </>
  );
};
