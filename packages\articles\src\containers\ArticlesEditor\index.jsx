import React, { useState, useEffect } from 'react';
import { Box } from '@mui/material';
import { SideMenu } from '../../components/SideMenu';
import { Editor } from '../../components/Editor';
import { editorScreens } from '../CommonConstants';
import { ArticleControls } from './ArticleControls';
import { TwoColumnPage, DoublePanelBorder, HeaderStyle } from '@cambianrepo/ui';

export const ArticlesEditor = (props) => {
  const {
    existingArticleData,
    setExistingArticleData,
    onSaveDraftCallback,
    onPublishCallback,
    onDuplicateCallback,
    onDeleteCallback,
    onExportCallback,
    handleNavigation,
    publishedRepository,
  } = props;

  const [currentScreen, setCurrentScreen] = useState(editorScreens.PROPERTIES);
  const [articleData, setArticleData] = useState({
    title: existingArticleData?.title || '',
    name: existingArticleData?.name || '',
    description: existingArticleData?.description || '',
    body: existingArticleData?.body || '',
    thumbnail: existingArticleData?.thumbnail || null,
  });
  const [formErrors, setFormErrors] = useState({});

  useEffect(() => {
    if (existingArticleData) {
      setArticleData({
        title: existingArticleData.title || '',
        name: existingArticleData.name || '',
        description: existingArticleData.description || '',
        body: existingArticleData.body || '',
        thumbnail: existingArticleData.thumbnail || null,
      });
    }
  }, [existingArticleData]);

  const handleEditorScreenNavigation = (screen) => {
    if (Object.keys(formErrors).length === 0) {
      setFormErrors({});
    }
    setCurrentScreen(screen);
  };

  const handleTitleChange = (value) => {
    setArticleData((prev) => ({ ...prev, title: value }));
  };

  const handleNameChange = (value) => {
    setArticleData((prev) => ({ ...prev, name: value }));
  };

  const handleDescriptionChange = (value) => {
    setArticleData((prev) => ({ ...prev, description: value }));
  };

  const handleBodyChange = (value) => {
    setArticleData((prev) => ({ ...prev, body: value }));
  };

  const handleThumbnailChange = (value) => {
    setArticleData((prev) => ({ ...prev, thumbnail: value }));
  };

  return (
    <>
      <HeaderStyle>
        <ArticleControls
          handleNavigation={handleNavigation}
          onSaveDraftCallback={onSaveDraftCallback}
          onPublishCallback={onPublishCallback}
          onDuplicateCallback={onDuplicateCallback}
          onDeleteCallback={onDeleteCallback}
          onExportCallback={onExportCallback}
          existingArticleData={existingArticleData}
          setExistingArticleData={setExistingArticleData}
          title={articleData.title}
          name={articleData.name}
          description={articleData.description}
          body={articleData.body}
          thumbnail={articleData.thumbnail}
          publishedRepository={publishedRepository}
          setFormErrors={setFormErrors}
        />
      </HeaderStyle>
      <Box>
        <TwoColumnPage
          leftColumn={
            <DoublePanelBorder>
              <SideMenu handleEditorScreenNavigation={handleEditorScreenNavigation} />
            </DoublePanelBorder>
          }
          rightColumn={
            <DoublePanelBorder>
              <Editor
                currentScreen={currentScreen}
                articleData={articleData}
                onTitleChange={handleTitleChange}
                onNameChange={handleNameChange}
                onDescriptionChange={handleDescriptionChange}
                onBodyChange={handleBodyChange}
                onThumbnailChange={handleThumbnailChange}
                existingArticleData={existingArticleData}
                formErrors={formErrors}
              />
            </DoublePanelBorder>
          }
        />
      </Box>
    </>
  );
};
