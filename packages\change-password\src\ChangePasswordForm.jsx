'use client';
import React, { useState } from 'react';
import { Grid, Button, Typography } from '@mui/material';
import { FormContainer } from 'react-hook-form-mui';
import PasswordField from './PasswordField';

const ChangePasswordForm = ({ min, max, formContext, handleSubmit, renderErrorMessages }) => {
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleToggleCurrentPasswordVisibility = () => {
    setShowCurrentPassword(!showCurrentPassword);
  };

  const handleToggleNewPasswordVisibility = () => {
    setShowNewPassword(!showNewPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <FormContainer onSuccess={handleSubmit} formContext={formContext}>
      <Typography sx={{ m: 2 }}>
        Enter your new password from {min} to {max} characters in length
      </Typography>

      {/* Current Password Field */}
      <PasswordField
        label="Current Password"
        name="currentPassword"
        formContext={formContext}
        showPassword={showCurrentPassword}
        handleTogglePasswordVisibility={handleToggleCurrentPasswordVisibility}
        renderErrorMessages={renderErrorMessages}
      />

      {/* New Password Field */}
      <PasswordField
        label="New Password"
        name="newPassword"
        formContext={formContext}
        showPassword={showNewPassword}
        handleTogglePasswordVisibility={handleToggleNewPasswordVisibility}
        renderErrorMessages={renderErrorMessages}
      />

      {/* Confirm New Password Field */}
      <PasswordField
        label="Confirm New Password"
        name="confirmPassword"
        formContext={formContext}
        showPassword={showConfirmPassword}
        handleTogglePasswordVisibility={handleToggleConfirmPasswordVisibility}
        renderErrorMessages={renderErrorMessages}
      />

      {/* Submit Button */}
      <Grid item>
        <Button variant="contained" type="submit" disabled={!formContext.formState.isDirty} sx={{ ml: 2, mb: 2 }}>
          Save
        </Button>
      </Grid>
    </FormContainer>
  );
};

export { ChangePasswordForm };
