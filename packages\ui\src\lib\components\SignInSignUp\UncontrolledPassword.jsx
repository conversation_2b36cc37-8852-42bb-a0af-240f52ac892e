import React, { useState, useEffect } from 'react';
import {
  FormControl,
  FormHelperText,
  IconButton,
  InputAdornment,
  InputLabel,
  OutlinedInput,
  useMediaQuery,
  Box,
} from '@mui/material';
import { Visibility, VisibilityOff } from '@mui/icons-material';

const UncontrolledPassword = React.forwardRef((props, ref) => {
  const {
    id,
    text,
    autoFocus,
    matchingPassword,
    setWidth,
    isRequired,
    min,
    max,
    labelFontSize,
    inputFontSize,
    inputBoxSize = 'medium',
    validSchema = {},
    onKeyDown,
  } = props;
  const [value, setValue] = useState('');
  const [focused, setFocused] = useState(false);
  const [isAutofilled, setIsAutofilled] = useState(false);
  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const [showPassword, setShowPassword] = React.useState(false);
  const isInvalid = Object.values(validSchema).some((v) => v === false);

  // Handle autofill detection
  useEffect(() => {
    const inputElement = document.getElementById(id);
    if (!inputElement) return;

    const checkAutofill = () => setIsAutofilled(inputElement.matches(':-webkit-autofill'));
    const observer = new MutationObserver(checkAutofill);
    observer.observe(inputElement, { attributes: true, attributeFilter: ['value'] });

    checkAutofill();
    return () => observer.disconnect();
  }, [id]);

  // Input field styling and shrink conditions
  const shrinkLabel = focused || isAutofilled || value;

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  return (
    <>
      <FormControl
        onKeyDown={onKeyDown}
        sx={{ m: 0, width: setWidth }}
        size={inputBoxSize}
        variant="outlined"
        required={isRequired}
      >
        <InputLabel
          htmlFor={id}
          color={isInvalid ? 'error' : 'primary'}
          shrink={shrinkLabel}
          sx={{
            fontSize: labelFontSize,
            marginTop: isXs ? '2px' : undefined,
            color: isInvalid ? 'error.main' : undefined,
            backgroundColor: shrinkLabel ? 'white' : 'transparent',
            padding: shrinkLabel ? '0 10px' : undefined,
            transform: shrinkLabel ? 'translate(8px, -6px) scale(0.75)' : undefined,
          }}
        >
          {text}
        </InputLabel>
        <OutlinedInput
          id={id}
          error={isInvalid}
          autoFocus={autoFocus}
          type={showPassword ? 'text' : 'password'}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          endAdornment={
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle password visibility"
                color={isInvalid ? 'error' : 'primary'}
                onClick={handleClickShowPassword}
                onMouseDown={handleMouseDownPassword}
                edge="end"
              >
                {showPassword ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </InputAdornment>
          }
          inputProps={{
            style: {
              fontSize: isXs ? inputFontSize.xs : inputFontSize.sm,
              ...(!showPassword && {
                fontFamily: 'Verdana',
                letterSpacing: '0.125em',
              }),
              WebkitTextSizeAdjust: '100%',
            },
          }}
          sx={{
            '& input:-webkit-autofill, & input:-webkit-autofill:focus, & input:-webkit-autofill:hover, & input:-webkit-autofill:first-line':
              {
                WebkitBoxShadow: '0 0 0 1000px white inset',
                WebkitTextFillColor: 'black',
                fontSize: inputFontSize,
              },
          }}
          inputRef={ref}
          defaultValue=""
          label={isXs ? text.slice(0, text.length - 1) : text}
        />
      </FormControl>
      <PasswordInvalidMessage validSchema={validSchema} min={min} max={max} matchingPassword={matchingPassword} />
    </>
  );
});

function PasswordInvalidMessage(props) {
  const { validSchema, matchingPassword, min, max } = props;

  // validSchema's keys are optional and thus we need to specifically check if they are false.
  // If we check all falsy value, the keys that are undefined will also show error message which we do not want.
  if (min === 1 && validSchema.isMinLengthValid === false) {
    return (
      <Box sx={{ marginTop: '0.2px !important' }}>
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 } }}>
          Please enter a password
        </FormHelperText>
      </Box>
    );
  }
  return (
    <Box sx={{ marginTop: '0.2px !important' }}>
      {((min > 1 && validSchema.isMinLengthValid === false) || validSchema.isMaxLengthValid === false) && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '0.2px !important' }}>
          Please enter a stronger password{' '}
          {max ? `from ${min} to ${max} characters` : `with at least ${min} characters`} in length
        </FormHelperText>
      )}
      {validSchema.containsLowercase === false && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 } }}>
          The password requires lowercase character(s)
        </FormHelperText>
      )}
      {validSchema.containsUppercase === false && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 } }}>
          The password requires uppercase character(s)
        </FormHelperText>
      )}
      {validSchema.containsNumber === false && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 } }}>
          The password requires numeric character(s)
        </FormHelperText>
      )}
      {validSchema.containsSpecial === false && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 } }}>
          The password requires special (!@#$%^&*) character(s)
        </FormHelperText>
      )}
      {matchingPassword === false && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 } }}>
          Passwords must match
        </FormHelperText>
      )}
    </Box>
  );
}

export { UncontrolledPassword };
