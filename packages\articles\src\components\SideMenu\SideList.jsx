import React, { useState } from 'react';
import { List, ListItemButton, ListItemText, ListItemIcon } from '@mui/material';
import { Settings, Description } from '@mui/icons-material';
import { editorScreens } from '../../containers/CommonConstants';
import { strings } from '../../utility/strings';

export const SideList = ({ handleEditorScreenNavigation }) => {
  const [selectedScreen, setSelectedScreen] = useState(editorScreens.PROPERTIES);

  const handleLinkClick = (screen) => {
    setSelectedScreen(screen);
    handleEditorScreenNavigation(screen);
  };

  return (
    <List disablePadding>
      <ListItemButton
        selected={selectedScreen === editorScreens.PROPERTIES}
        onClick={() => handleLinkClick(editorScreens.PROPERTIES)}
      >
        <ListItemIcon>
          <Settings sx={{ fontSize: '20px' }} />
        </ListItemIcon>
        <ListItemText primary={strings.properties} sx={{ ml: -2 }} />
      </ListItemButton>
      <ListItemButton
        selected={selectedScreen === editorScreens.BODY}
        onClick={() => handleLinkClick(editorScreens.BODY)}
      >
        <ListItemIcon>
          <Description sx={{ fontSize: '20px' }} />
        </ListItemIcon>
        <ListItemText primary={strings.body} sx={{ ml: -2 }} />
      </ListItemButton>
    </List>
  );
};
