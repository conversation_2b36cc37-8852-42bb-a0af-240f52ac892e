// Set of Coding Systems that are supported
// IMPORTANT: These must all be in lower case

import { format } from 'date-fns';

//
const supportedCodingSet = new Set(['http://loinc.org']);

//
// Create Answer
//
// Creates a structure that contains both the question JSON structure and the question (if any) to date
// The structure contains :
//
//  {
//      question: {},           <== full question JSON from questionnaire
//      type: "choice",         <== choice field from questionnaire question structure
//      answer: ???             <== small questionnaire response structure.  Could be array or structure but
//                                  is specific to each question type.  For example, for a "choice" question type
//                                  such as a radio box, this structure would be a JSON structure that can be found
//                                  as a "answerOption" in the questionnaire question.  That is, something like:
//                                    {
//                                      "valueCoding":{
//                                      "id":"573487",
//                                      "code":"1",
//                                      "display":"I have no problems in walking about"
//                                    }
//
//                                  if this were a checkbox question instead, it would be an array of such structures
//  }
//
//

export function createAnswer(question, answer) {
  let structure = {};

  if (question.type === 'choice') {
    structure = {
      ...structure,
      question: {
        ...question,
        answerOption: question.answerOption.sort((a, b) =>
          sortBySequenceExtension(a.valueCoding, b.valueCoding, 'Item/AnswerOption/ValueCoding/sequence-value'),
        ),
      },
      type: question.type,
      isVisible: question.enableWhen && question.enableWhen.length ? false : true,
      answer: answer,
    };
  } else {
    structure = {
      ...structure,
      question: question,
      type: question.type,
      isVisible: question.enableWhen && question.enableWhen.length ? false : true,
      answer: answer,
    };
  }

  return structure;
}

export function sortBySequenceExtension(a, b, extensionUrl) {
  let sequenceExtensionA = extractExtension(a.extension, extensionUrl);
  let sequenceExtensionB = extractExtension(b.extension, extensionUrl);

  let sequenceA = sequenceExtensionA && sequenceExtensionA.valueInteger;
  let sequenceB = sequenceExtensionB && sequenceExtensionB.valueInteger;

  if (sequenceA < sequenceB) {
    return -1;
  }
  if (sequenceA > sequenceB) {
    return 1;
  }
  return 0;
}

export function createGridQuestionAnswer(complexQuestion, codingSystemMap, questionnaireResponses) {
  let subQuestions = [];

  for (let question of complexQuestion.item) {
    if (question.type === 'group') {
      subQuestions = [
        ...subQuestions,
        ...question.item.sort((a, b) => sortBySequenceExtension(a, b, 'Item/complex-value-attribute-sequence')),
      ];

      question.item
        .sort((a, b) => sortBySequenceExtension(a, b, 'Item/complex-value-attribute-sequence'))
        .forEach((subQuestion, index) => {
          let answer = findGridQuestionResponse(
            complexQuestion,
            subQuestion,
            codingSystemMap,
            questionnaireResponses.item,
          );
          subQuestion.answer = answer.get(subQuestion.linkId) || [];
        });
    } else {
      subQuestions = [...complexQuestion.item];
    }
  }

  const structure = {
    ...complexQuestion,
    type: 'complex',
    item: subQuestions,
    isVisible: complexQuestion.enableWhen && complexQuestion.enableWhen.length ? false : true,
  };

  return structure;
}

//
// Build Question List
//
// Produces a list of questions from the questionnaire in the original order from the questionnaire itself.  It
// contains both the questions and (any) question responses already performed.   For example, the structure of the list
// might look like the following.   Each entry of this array would be the output of the "createAnswer" function above.
//
//  [
//     {
//      question: {},           <== full question JSON from questionnaire
//      type: "choice",
//      answer: {
//                 "valueCoding":{
//                 "id":"573487",
//                 "code":"1",
//                 "display":"I have no problems in walking about"
//              }
//     },
//     ...
//     ...
//     ...
//  ]
//
export function buildQuestionList(questionnaireQuestions, questionnaireResponses, codingSystemMap, isFlattened) {
  let questionList = [];
  if (questionnaireQuestions !== undefined) {
    questionnaireQuestions.map((question, index) => {
      if (question.type === 'group') {
        if (isFlattened) {
          buildQuestionList(question.item, questionnaireResponses, codingSystemMap, false).forEach((groupQuestion) => {
            if (groupQuestion.type === 'complex') {
              questionList.push(createGridQuestionAnswer(groupQuestion, codingSystemMap, questionnaireResponses));
            } else {
              questionList.push(
                createAnswer(
                  groupQuestion.question,
                  findQuestionResponse(groupQuestion.question, codingSystemMap, questionnaireResponses),
                ),
              );
            }
          });
        } else if (question.id.includes('complex')) {
          questionList.push(createGridQuestionAnswer(question, codingSystemMap, questionnaireResponses));
        } else {
          questionList.push({
            id: question.id,
            type: 'group',
            item: buildQuestionList(question.item, questionnaireResponses, codingSystemMap, false),
          });
        }
      } else {
        questionList.push(
          createAnswer(question, findQuestionResponse(question, codingSystemMap, questionnaireResponses)),
        );
      }

      return true;
    });
  }

  return questionList;
}

const getQuestionnaireStartPageNumber = (
  questionList,
  isSaveForLaterActive,
  fhirResponse,
  maxQuestionnairePages,
  fhirQuestionnaire,
  questionMap,
) => {
  let pageNumber = 1;
  let lastUnansweredQuestionId;
  let lastUnansweredQuestionType;
  let displayTypeQuestionPosition;
  let lastAnsweredQuestionPosition;

  let tempQuestionList = [...questionList];
  let reversedQuestionList = tempQuestionList.reverse();

  if (isSaveForLaterActive && fhirResponse && fhirResponse.item && fhirResponse.item.length) {
    for (let questionOrGroupIndex in reversedQuestionList) {
      let questionOrGroup = reversedQuestionList[questionOrGroupIndex];

      if (questionOrGroup.type === 'group') {
        let tempQuestionOrGroupList = [...questionOrGroup.item];
        let reversedQuestionOrGroupList = tempQuestionOrGroupList.reverse();

        for (let questionIndex in reversedQuestionOrGroupList) {
          let question = reversedQuestionOrGroupList[questionIndex];
          if (question.isVisible || (question?.question?.enableWhen && question?.question?.enableWhen.length)) {
            if (question.type !== 'complex') {
              if (lastAnsweredQuestionPosition === undefined) {
                if (
                  !isQuestionAnswered(question) &&
                  isQuestionVisible(question.question, fhirQuestionnaire, questionMap)
                ) {
                  lastUnansweredQuestionId = question.question.id;
                  pageNumber = questionList.length - questionOrGroupIndex;
                  lastUnansweredQuestionType = question.question.type;

                  if (question.type === 'display') {
                    displayTypeQuestionPosition = {
                      pageIndex: Number(pageNumber) - 1,
                      questionIndex: Number(questionIndex),
                    };
                  }
                } else if (isQuestionAnswered(question)) {
                  lastAnsweredQuestionPosition = {
                    questionIndex: Number(questionIndex),
                    pageIndex: questionList.length - Number(questionOrGroupIndex),
                  };
                }
              } else {
                if (isRequired(question)) {
                  if (
                    !isQuestionAnswered(question) &&
                    isQuestionVisible(question.question, fhirQuestionnaire, questionMap)
                  ) {
                    lastUnansweredQuestionId = question.question.id;
                    pageNumber = questionList.length - questionOrGroupIndex;
                    lastUnansweredQuestionType = question.question.type;

                    if (question.type === 'display') {
                      displayTypeQuestionPosition = {
                        pageIndex: Number(pageNumber) - 1,
                        questionIndex: Number(questionIndex),
                      };
                    }
                  }
                }
              }
            } else {
              if (lastAnsweredQuestionPosition === undefined) {
                if (!isGridQuestionAnswered(question)) {
                  lastUnansweredQuestionId = question.id;
                  pageNumber = questionList.length - questionOrGroupIndex;
                } else {
                  lastAnsweredQuestionPosition = {
                    questionIndex: Number(questionIndex),
                    pageIndex: questionList.length - Number(questionOrGroupIndex),
                  };
                }
              } else {
                if (isRequired(question)) {
                  if (!isGridQuestionAnswered(question)) {
                    lastUnansweredQuestionId = question.id;
                    pageNumber = questionList.length - questionOrGroupIndex;
                  }
                }
              }
            }
          }
        }
      } else {
        if (
          questionOrGroup.isVisible ||
          (questionOrGroup?.questionOrGroup?.enableWhen && questionOrGroup?.questionOrGroup?.enableWhen.length)
        ) {
          if (questionOrGroup.type !== 'complex') {
            if (lastAnsweredQuestionPosition === undefined) {
              if (
                !isQuestionAnswered(questionOrGroup) &&
                isQuestionVisible(questionOrGroup.question, fhirQuestionnaire, questionMap)
              ) {
                lastUnansweredQuestionId = questionOrGroup.question.id;
                pageNumber = questionList.length - questionOrGroupIndex;
                lastUnansweredQuestionType = questionOrGroup.question.type;

                if (questionOrGroup.type === 'display') {
                  displayTypeQuestionPosition = {
                    pageIndex: questionList.length - Number(questionOrGroupIndex),
                    questionIndex: Number(questionOrGroupIndex),
                  };
                }
              } else if (isQuestionAnswered(questionOrGroup)) {
                lastAnsweredQuestionPosition = {
                  questionIndex: Number(questionOrGroupIndex),
                  pageIndex: questionList.length - Number(questionOrGroupIndex),
                };
                // return (lastUnansweredQuestionId === undefined || pageNumber > maxQuestionnairePages) ? maxQuestionnairePages: pageNumber;
              }
            } else {
              if (isRequired(questionOrGroup)) {
                if (
                  !isQuestionAnswered(questionOrGroup) &&
                  isQuestionVisible(questionOrGroup.question, fhirQuestionnaire, questionMap)
                ) {
                  lastUnansweredQuestionId = questionOrGroup.question.id;
                  pageNumber = questionList.length - questionOrGroupIndex;
                  lastUnansweredQuestionType = questionOrGroup.question.type;

                  if (questionOrGroup.type === 'display') {
                    displayTypeQuestionPosition = {
                      pageIndex: questionList.length - Number(questionOrGroupIndex),
                      questionIndex: Number(questionOrGroupIndex),
                    };
                  }
                }
              }
            }
          } else {
            if (lastAnsweredQuestionPosition === undefined) {
              if (!isGridQuestionAnswered(questionOrGroup)) {
                lastUnansweredQuestionId = questionOrGroup.id;
                pageNumber = questionList.length - questionOrGroupIndex;
              } else {
                return lastUnansweredQuestionId === undefined || pageNumber > maxQuestionnairePages
                  ? maxQuestionnairePages
                  : pageNumber;
              }
            } else {
              if (!isGridQuestionAnswered(questionOrGroup)) {
                lastUnansweredQuestionId = questionOrGroup.id;
                pageNumber = questionList.length - questionOrGroupIndex;
              }
            }
          }
        }
      }
    }
    if (lastUnansweredQuestionId === undefined || pageNumber > maxQuestionnairePages) {
      pageNumber = maxQuestionnairePages;
    }

    if (
      lastUnansweredQuestionType === 'display' &&
      displayTypeQuestionPosition &&
      lastAnsweredQuestionPosition &&
      displayTypeQuestionPosition?.pageIndex < lastAnsweredQuestionPosition.pageIndex
    ) {
      pageNumber = lastAnsweredQuestionPosition.pageIndex;
    }
  }

  return pageNumber;
};

//
// buildSystemCode
//
// Returns a coding system URI (most likely a URL) that is composed of the FHIR "system" attribute and the FHIR "code" attribute
// ie "http://loinc.org/45394-4"
//
export function buildSystemCode(system, code) {
  if (system !== null && system !== undefined && code !== null && code !== undefined) {
    if (system.slice(-1) === '/') {
      return system + code;
    } else {
      return system + '/' + code;
    }
  }
  return null;
}

//
// extractCodingSystem
//
// Extracts all coding systems that have been assigned to the question.  This function returns an array of coding system
// URI/URLs that can be then looked up in the set of codes that the application layer may have provided.
// ie ["http://loinc.org/97848-6", "http://loinc.org/45392-8", "http://loinc.org/97847-8", "http://loinc.org/45394-4"]
//
export function extractCodingSystem(question) {
  let systemCodeList = [];
  if (question.code !== undefined) {
    question.code.forEach((code) => {
      if (code.system !== undefined && code.code !== undefined) {
        if (supportedCodingSet.has(code.system.toLowerCase())) {
          systemCodeList.push(buildSystemCode(code.system, code.code));
        }
      }
    });
  }
  return systemCodeList;
}

//
// findSystemCodeAnswer
//
// This function returns the value the application layer has provided that can be used as the answer to the supplied question.
// Each question has an array (0..*) mapping system identifiers that may be used to answer the question.  This function will
// retrieve all identifiers assigned to the question, then will check what the application layer has provided to find the
// *first* match.  The order that the match is made is random.
//
// In the near term this function will return a simple value such as a valueBoolean, valueDecimal, valueDate, valueDateTime, valueInteger or valueString
//
export function findSystemCodeAnswer(codingSystemMap, question) {
  let questionAnswer = null;
  if (codingSystemMap !== undefined && codingSystemMap.size > 0) {
    let systemCodeList = extractCodingSystem(question);
    systemCodeList.forEach((systemUri) => {
      let value = codingSystemMap.get(systemUri);
      if (value !== null) {
        questionAnswer = value;
        return true;
      }
    });
  }
  return questionAnswer;
}

export function buildCodingSystemMap(questionnaireQuestions, codingSystemCallback) {
  let codingMap = new Map();
  if (codingSystemCallback !== undefined && questionnaireQuestions !== undefined) {
    let codeList = [];
    questionnaireQuestions.map((question, index) => {
      if (question.type === 'group') {
        buildCodingSystemMap(question.item).forEach((groupQuestion) => {
          let questionCodeList = extractCodingSystem(groupQuestion.question);
          if (questionCodeList !== null) {
            codeList = codeList.concat(questionCodeList);
          }
        });
      } else {
        let questionCodeList = extractCodingSystem(question);
        if (questionCodeList !== null) {
          codeList = codeList.concat(questionCodeList);
        }
      }
    });

    codeList.forEach((codingUri) => {
      let codeValue = codingSystemCallback(codingUri);
      if (codeValue != null) {
        codingMap.set(codingUri, codeValue);
      }
    });
  }

  return codingMap;
}

export function buildOutputSystemCodeMap(questionMap) {
  let outputMap = new Map();
  if (questionMap !== undefined && questionMap !== null) {
    for (let [key, question] of questionMap) {
      if (question.answer !== undefined) {
        let systemCodeList = extractCodingSystem(question.question ? question.question : question);
        if (systemCodeList.length > 0) {
          let questionType = getQuestionType(question.question);
          let questionValue;
          if (questionType === 'integer') {
            questionValue = question.answer.valueInteger;
          } else if (questionType === 'text') {
            questionValue = question.answer.valueString;
          } else if (questionType === 'date') {
            questionValue = question.answer.valueDate;
          } else if (questionType === 'dateTime') {
            questionValue = question.answer.valueDateTime;
          } else if (questionType === 'decimal') {
            questionValue = question.answer.valueDecimal;
          }

          // will only be undefined for those supported question types
          if (questionValue !== undefined) {
            systemCodeList.forEach((codeUri) => {
              outputMap.set(codeUri, questionValue);
            });
          }
        }
      }
    }
  }
  return outputMap;
}

export function buildQuestionSequenceList(questionnaireQuestions) {
  let questionList = [];
  questionnaireQuestions.map((question, index) => {
    if (question.type === 'group') {
      questionList.push({
        id: question.id,
        type: 'group',
        items: buildQuestionSequenceList(question.items),
      });
    } else {
      questionList.push({
        id: question.id,
        type: 'question',
      });
    }
    return true;
  });

  return questionList;
}

//
// Find Question Response
//
// Returns the questionnaire response for a particular question.   The return value of this is the structure injected
// in the "createAnswer" function above
//

function findGridQuestionResponse(complexQuestion, subQuestion, codingSystemMap, questionnaireResponses) {
  let answerMap = new Map();

  if (questionnaireResponses !== undefined && questionnaireResponses !== null) {
    let apiResponse = [];
    questionnaireResponses.forEach((questionnaireResponse) => {
      questionnaireResponse.item.forEach((responses) => {
        if (responses.id.includes('complex')) {
          let columnAnswers = {};
          // we can get the row sequence from any of the columns, a.items is all columns answer for a particular row
          // a.item[0] means answer for the first column of a row
          responses.item
            .sort((a, b) => sortBySequenceExtension(a.item[0], b.item[0], 'Item/row-sequence'))
            .forEach((response) => {
              response.item.forEach((row) => {
                if (row.answer) {
                  let rowAnswer = columnAnswers[row.linkId]
                    ? [...columnAnswers[row.linkId], ...row.answer]
                    : [...row.answer];
                  columnAnswers = {
                    ...columnAnswers,
                    [row.linkId]: rowAnswer,
                  };
                } else {
                  const gridAnswerKeyMap = {
                    text: 'valueString',
                    decimal: 'valueInteger',
                    dateTime: 'valueDate',
                  };
                  const rowTypeExtension = extractExtension(row.extension, 'Item/column-type');
                  const rowType = rowTypeExtension ? rowTypeExtension.valueString : 'text'; // if no type found consider that as text

                  const gridNullAnswer = { [gridAnswerKeyMap[rowType]]: null }; // Creating null response for the cells that were not answered
                  let rowAnswer = columnAnswers[row.linkId]
                    ? [...columnAnswers[row.linkId], gridNullAnswer]
                    : [gridNullAnswer];

                  columnAnswers = {
                    ...columnAnswers,
                    [row.linkId]: rowAnswer,
                  };
                }
              });
            });
          apiResponse.push(columnAnswers);
        }
      });
    });

    if (apiResponse.length > 0) {
      apiResponse.forEach((responses) => {
        Object.keys(responses).forEach((responseKey) => answerMap.set(responseKey, responses[responseKey]));
      });
    }
  } else {
    answerMap.set(subQuestion.linkId, buildCodingSystemQuestionResponse(subQuestion, codingSystemMap));
  }

  return answerMap;
}

export function buildQuestionResponseMap(questionList, isFlattened) {
  let questionMap = new Map();
  if (questionList !== undefined) {
    questionList.map((question, index) => {
      let questionWithResponse = { ...question };
      if (questionWithResponse.type === undefined) {
        let questionTypeExtension = extractExtension(question.extension, 'Item/question-type');
        if (questionTypeExtension?.valueString) {
          questionWithResponse.type = questionTypeExtension && questionTypeExtension.valueString;
        } else if (question.item?.length || question?.id?.includes('group')) {
          questionWithResponse.type = 'group';
        }
      }
      if (questionWithResponse.type === 'group' && questionWithResponse.id.includes('complex')) {
        questionMap.set(questionWithResponse.id, questionWithResponse);
        question.item.forEach((subQuestion) => subQuestion.item.forEach((item) => questionMap.set(item.id, item)));
      } else if (questionWithResponse.type === 'group' && !isFlattened) {
        let innerQuestionMap = buildQuestionResponseMap(question.item, false);
        questionMap = new Map([...questionMap.entries(), ...innerQuestionMap.entries()]);
      } else {
        questionMap.set(questionWithResponse.id, questionWithResponse);
      }
      return true;
    });
  }

  return questionMap;
}

export function findQuestionResponse(question, codingSystemMap, questionnaireResponses) {
  let questionResponseArray = [];
  let questionType = getQuestionType(question);

  if (questionnaireResponses !== undefined && questionnaireResponses !== null) {
    const responseMap = buildQuestionResponseMap(questionnaireResponses.item);
    let apiResponse = [];

    if (responseMap.size && responseMap.get(question.id) && responseMap.get(question.id).answer) {
      responseMap.get(question.id).answer.forEach((answer) => apiResponse.push(answer));
    }
    if (apiResponse.length > 0) {
      questionResponseArray = buildQuestionResponse(question, apiResponse);
    }
  } else {
    questionResponseArray = buildCodingSystemQuestionResponse(question, codingSystemMap);
  }

  let answer = [];
  if (questionResponseArray.length > 0) {
    switch (questionType) {
      case 'multiple-choice':
        answer = questionResponseArray;
        break;

      case 'single-choice':
        answer = questionResponseArray.pop();
        break;

      case 'integer':
        answer = {
          valueInteger: questionResponseArray.pop(),
        };
        break;

      case 'text':
        answer = {
          valueString: questionResponseArray.pop(),
        };
        break;

      case 'date':
        answer = {
          valueDate: questionResponseArray.pop(),
        };
        break;

      case 'dateTime':
        answer = {
          valueDateTime: questionResponseArray.pop(),
        };
        break;

      case 'decimal':
        answer = {
          valueDecimal: questionResponseArray.pop(),
        };
        break;

      default:
        answer = [];
        break;
    }
  }

  return answer;
}

export function getQuestionType(question) {
  let questionType = '';
  if (question.type === 'choice') {
    const multipleChoiceExtension = extractExtension(question.extension, 'Item/multiple-answer-choice');
    if (multipleChoiceExtension) {
      if (multipleChoiceExtension.valueBoolean) {
        questionType = 'multiple-choice';
      } else {
        questionType = 'single-choice';
      }
    } else {
      console.log('handle extension type');
    }
  } else if (
    question.type === 'integer' ||
    question.type === 'text' ||
    question.type === 'date' ||
    question.type === 'dateTime' ||
    question.type === 'decimal'
  ) {
    questionType = question.type;
  } else {
    console.log('getQuestionType - MUST HANDLE QUESTION TYPE: ' + question.type);
  }

  return questionType;
}

//
// buildCodingSystemQuestionResponse
//
// This function returns a FHIR snippet to answer a simple answer.
//
export function buildCodingSystemQuestionResponse(question, codingSystemMap) {
  let responseArray = [];
  let questionType = getQuestionType(question);
  let providedResponse = findSystemCodeAnswer(codingSystemMap, question);

  if (questionType === 'single-choice' || questionType === 'multiple-choice') {
    // Coding system support for these question types are not yet supported
  } else if (
    questionType === 'integer' ||
    questionType === 'text' ||
    questionType === 'date' ||
    questionType === 'dateTime' ||
    questionType === 'decimal'
  ) {
    responseArray.push(providedResponse);
  } else {
    // Handle other question types
    console.log('handle ' + getQuestionType(question));
  }

  return responseArray;
}

export function buildQuestionResponse(question, responseValues) {
  let responseArray = [];
  let questionType = getQuestionType(question);
  let otherOptionExtensionUrl = 'Item/AnswerOption/ValueCoding/other-option';

  if (questionType === 'single-choice' || questionType === 'multiple-choice') {
    question.answerOption.forEach((option) => {
      responseValues.forEach((response) => {
        if (response.valueCoding.id == option.valueCoding.id) {
          if (option.valueCoding && option.valueCoding.extension) {
            option.valueCoding.extension.forEach((extension) => {
              if (extension.url === otherOptionExtensionUrl) {
                const additionalTextValue = extractExtension(
                  response.valueCoding.extension,
                  otherOptionExtensionUrl,
                ).valueString;
                extension.valueString = additionalTextValue;
              }
            });
          }
          responseArray.push(option);
        }
      });
    });
  } else if (
    questionType === 'integer' ||
    questionType === 'text' ||
    questionType === 'date' ||
    questionType === 'dateTime' ||
    questionType === 'decimal'
  ) {
    responseValues.forEach((response) => {
      const keys = Object.keys(response);
      if (keys && keys.length && response[keys[0]] !== null) {
        responseArray.push(response[keys[0]]);
      }
    });
  } else {
    // Handle other question types
    console.log('handle ' + getQuestionType(question));
  }

  return responseArray;
}

//
// Build Question Map
//
// This function simple returns a Map stucture where the key is the Question id, and the value is the result created
// by the "createAnswer" function above
//
export function buildQuestionMap(questionList, isFlattened) {
  let questionMap = new Map();
  if (questionList !== undefined) {
    questionList.map((question, index) => {
      if (question.type === 'group' && !isFlattened) {
        let innerQuestionMap = buildQuestionMap(question.item, false);
        questionMap = new Map([...questionMap.entries(), ...innerQuestionMap.entries()]);
      } else if (question.type === 'complex') {
        questionMap.set(question.id, question);
        // used linkId for grid columns as currently their id are of type integer and not unique when multiple grid questions present
        question.item.forEach((subQuestion) => questionMap.set(subQuestion.linkId, subQuestion));
      } else {
        questionMap.set(question.question.id, question);
      }
      return true;
    });
  }

  return questionMap;
}

//
// Build Questionnaire Page List
//
// This function returns the set of questions that are needed for the current questionnaire page.  This particular
// implementation is most simplistic possible ; only one question on each page of the questionnaire.
//
export function buildQuestionnairePageList(questionnairePage, questionList, questionMap, fhirQuestionnaire) {
  let pageQuestions = JSON.parse(JSON.stringify(questionList));

  let questionnairePageQuestions = pageQuestions.slice(questionnairePage - 1, questionnairePage);
  if (questionnairePageQuestions.length) {
    let questionOrGroup = questionnairePageQuestions[0];
    if (questionOrGroup.type === 'group') {
      // TBD - here not considering nested group. need to discuss how to handle this scenario.
      questionnairePageQuestions = questionOrGroup.item;
      questionnairePageQuestions.forEach((question) => {
        let questionContext =
          question.type === 'complex' ? questionMap.get(question.id) : questionMap.get(question.question.id);
        question.isVisible = isQuestionVisible(
          question.question ? question.question : question,
          fhirQuestionnaire,
          questionMap,
        );
        questionContext.isVisible = question.isVisible;
        if (question.isVisible) {
          if (question.type === 'complex') {
            for (let index = 0; index < question.item.length; index++) {
              question.item[index].answer = questionContext.item[index].answer;
            }
          } else {
            question.answer = questionContext.answer;
          }
        } else {
          if (question.type === 'complex') {
            for (let index = 0; index < question.item.length; index++) {
              question.item[index].answer = [];
              questionContext.item[index].answer = [];
            }
          } else {
            if (question.answer.constructor === Array) {
              question.answer = [];
              questionContext.answer = [];
            } else {
              question.answer = {};
              questionContext.answer = {};
            }
          }
        }
      });
    } else {
      let questionContext = questionMap.get(questionOrGroup.question.id);
      questionOrGroup.isVisible = isQuestionVisible(questionOrGroup.question, fhirQuestionnaire, questionMap);
      questionContext.isVisible = questionOrGroup.isVisible;
      if (questionOrGroup.isVisible) {
        questionOrGroup.answer = questionContext.answer;
      } else {
        if (questionOrGroup.answer.constructor === Array) {
          questionOrGroup.answer = [];
          questionContext.answer = [];
        } else {
          questionOrGroup.answer = {};
          questionContext.answer = {};
        }
      }
    }
  }

  return questionnairePageQuestions;
}

//
// Calculate Questionnaire Page Length
//
// Simplistic calculation of number of questionnaire pages in a questionnaire.  This implementation only allows one
// question per questionnaire page, so the number of pages is the same as the number of questions
//
export function calculateQuestionnairePageLength(questionList) {
  if (questionList === undefined) {
    return 0;
  }
  return questionList.length;
}

export function getCalculatedScores(questionnaireResponse) {
  const calculatedScoresExtension = extractExtension(questionnaireResponse.extension, 'calculated-scores');
  const calculatedScores = calculatedScoresExtension ? calculatedScoresExtension.extension : [];

  let scoresExtension;
  if (calculatedScores && calculatedScores.length) {
    scoresExtension = extractAllMatchingExtension(calculatedScores, 'score');
  }

  let instrumentScores = [];
  if (scoresExtension && scoresExtension.length) {
    scoresExtension.forEach((score) => {
      let variableScore = score.valueString;
      if (variableScore) {
        variableScore = variableScore.split(':');
        instrumentScores.push({
          scoreDefinitionName: variableScore[0],
          score: variableScore[1],
        });
      }
    });
  }

  return instrumentScores;
}

//
// Calculate Progress
//
// Simplistic calculation of progress through a questionnaire.  This is based only upon the page number of the questionnaire.
// The returned value should range from 0 to 100
//
export function calculateProgress(questionMap) {
  let completionCount = 0;
  let totalQuestions = 0;
  let completionCountProgress = 0;

  if (questionMap !== undefined) {
    for (const [key, value] of questionMap.entries()) {
      if (value.type && value.type !== 'display' && value.isVisible) {
        totalQuestions = totalQuestions + 1;
        if (value.type === 'complex') {
          completionCount = completionCount + (isGridQuestionAnswered(value) ? 1 : 0);
        } else {
          completionCount = completionCount + (isQuestionAnswered(value) ? 1 : 0);
        }
      }
    }

    completionCountProgress = (completionCount / totalQuestions) * 100;
  }

  return completionCountProgress;
}

//
// Extract Extension
//
// Extracts the extension from the extension array if it exists.
// Please see http://www.hl7.org/fhir/questionnaire.html for the "item".  The "extension" is an "Element" which is a
// superclass.  That is, "item" is a "BackboneElement" and the superclas of that is "Element"
//
//
export function extractExtension(extensionArray, url) {
  let matchingExtension = null;
  if (extensionArray !== undefined && url !== undefined) {
    if (extensionArray !== undefined && url !== undefined && url !== null) {
      extensionArray.forEach((ext) => {
        if (ext.url.toUpperCase() === url.toUpperCase() || ext.url.toUpperCase().includes(url.toUpperCase())) {
          matchingExtension = ext;
        }
      });
    }
  }
  return matchingExtension;
}

export function extractAllMatchingExtension(extensionArray, url) {
  let result;
  if (extensionArray !== undefined) {
    result = extensionArray.filter((extension) => url === extension.url || extension.url.includes(url));
  }
  return result;
}

export function isQuestionAnswered(question) {
  let questionAnswered = false;
  if (question.answer !== undefined) {
    if (question.answer.constructor === Array) {
      questionAnswered = question.answer.length > 0;
    } else if (typeof question.answer === 'object') {
      if (
        question.answer.valueCoding ||
        (question.answer.valueString !== undefined &&
          question.answer.valueString !== null &&
          question.answer.valueString.trim() !== '') ||
        (question.answer.valueInteger !== undefined &&
          question.answer.valueInteger !== null &&
          question.answer.valueInteger.toString().trim() !== '') ||
        (question.answer.valueDate !== undefined &&
          question.answer.valueDate !== null &&
          question.answer.valueDate !== '') ||
        (question.answer.valueDateTime !== undefined &&
          question.answer.valueDateTime !== null &&
          question.answer.valueDateTime !== '') ||
        (question.answer.valueDecimal !== undefined &&
          question.answer.valueDecimal !== null &&
          question.answer.valueDecimal.toString().trim() !== '')
      ) {
        questionAnswered = true;
      }
    } else {
      questionAnswered =
        question.answer !== undefined &&
        question.answer !== null &&
        !(question.answer.constructor === String && question.answer.trim().length === 0);
    }
  }
  return questionAnswered;
}

export const isGridQuestionAnswered = (complexQuestion) => {
  let isGridAnswered = false;

  for (let question of complexQuestion.item) {
    if (question.answer && question.answer.length) {
      for (let answer of question.answer) {
        if (answer) {
          if (typeof answer === 'object') {
            if (answer.valueString || !isNaN(answer.valueDecimal) || answer.valueDate) {
              return true;
            }
          }
        }
      }
    }
  }
  return isGridAnswered;
};

export function isQuestionComplete(question) {
  let isComplete = true;
  if (isRequired(question) && question.type === 'complex') {
    for (let subQuestion of question.item) {
      isComplete = isQuestionAnswered(subQuestion);
      if (isComplete) {
        return isComplete;
      }
    }
  } else if (isRequired(question) && !isQuestionAnswered(question)) {
    isComplete = false;
  }
  return isComplete;
}

export function isRequired(question) {
  let isQuestionRequired = false;
  if (question.type !== 'complex' && question.question.required !== undefined) {
    isQuestionRequired = question.question.required;
  } else if (question.required !== undefined) {
    isQuestionRequired = question.required;
  }
  return isQuestionRequired;
}

export function convertDateToLocaleString(dateValue) {
  const d = new Date(dateValue);

  const reconstructedDate = d.getMonth() + 1 + '-' + d.getDate() + '-' + d.getFullYear();

  const reconstructedTime = d.toLocaleString('en-US', {
    hour: '2-digit',
    minute: 'numeric',
    hour12: true,
  });

  const result = reconstructedDate + ' ' + reconstructedTime;

  return result;
}

export function isQuestionVisible(question, fhirQuestionnaire, questionMap) {
  let isVisible = true;
  if (question && question.enableWhen && question.enableWhen.length) {
    question.enableWhen.forEach((rule) => {
      let questionLinkId = rule.question;

      if (questionLinkId) {
        let score = getScoreOfAnswers(questionLinkId, fhirQuestionnaire, questionMap);
        let operator = rule.operator;
        let answer = rule.answerInteger || rule.answerString;

        isVisible = isVisible && (score == null ? false : isComponentShown(score, answer, operator));
      }
    });
  }
  return isVisible;
}

const getScoreOfAnswers = (questionLinkId, fhirQuestionnaire, questionMap) => {
  let score = null;
  let questionId = getQuestionId(questionLinkId, fhirQuestionnaire);
  if (questionId) {
    let questionContext = questionMap.get(questionId);
    if (questionContext) {
      let answer = questionContext.answer;

      if (answer && answer.constructor === Array) {
        answer.forEach((item) => {
          if (item.valueCoding && item.valueCoding.code) {
            const currentAnswerScore = Number(item.valueCoding.code);
            score = score !== null || score !== undefined ? score + currentAnswerScore : currentAnswerScore;
          }
        });
      } else {
        if (answer.valueCoding && answer.valueCoding.code) {
          const currentAnswerScore = Number(answer.valueCoding.code);
          score = score !== null || score !== undefined ? score + currentAnswerScore : currentAnswerScore;
        } else if (answer.valueDecimal) {
          const currentAnswerScore = Number(answer.valueDecimal);
          score = score !== null || score !== undefined ? score + currentAnswerScore : currentAnswerScore;
        } else if (answer.valueInteger) {
          const currentAnswerScore = Number(answer.valueInteger);
          score = score !== null || score !== undefined ? score + currentAnswerScore : currentAnswerScore;
        }
      }
    }
  }
  return score;
};

const getQuestionId = (questionLinkId, fhirQuestionnaire) => {
  let questionId = null;
  fhirQuestionnaire.item.forEach((questionOrPage) => {
    if (questionOrPage.type === 'display') {
      return questionId;
    }
    if (questionOrPage.linkId == questionLinkId && questionOrPage.type !== 'display') {
      questionId = questionOrPage.id;
    } else if (questionOrPage.type === 'group') {
      questionOrPage.item.forEach((question) => {
        if (question.linkId === questionLinkId) {
          questionId = question.id;
        }
      });
    }
  });
  return questionId;
};

const isComponentShown = (_operand1, _operand2, operator) => {
  let isShown = false;
  let operand1 = parseInt(_operand1);
  let operand2 = parseInt(_operand2);

  switch (operator) {
    case '<':
      isShown = operand1 < operand2;
      break;
    case '<=':
      isShown = operand1 <= operand2;
      break;
    case '=':
      isShown = operand1 === operand2;
      break;
    case '>':
      isShown = operand1 > operand2;
      break;
    case '>=':
      isShown = operand1 >= operand2;
      break;
    case '!=':
      isShown = operand1 !== operand2;
      break;
    default:
      break;
  }
  return isShown;
};

export function hasVisibleComponent(pageList) {
  let hasVisibleComponent = false;
  pageList.map((question) => {
    if (question.isVisible === true) {
      hasVisibleComponent = true;
    }
  });
  return hasVisibleComponent;
}

export function extractResultPageHtml(questionnaireJSON, extensionUrl, sectionType) {
  let headerHtml;
  if (questionnaireJSON !== undefined && questionnaireJSON.extension !== undefined) {
    let extension = extractExtension(questionnaireJSON.extension, extensionUrl);
    if (
      extension !== undefined &&
      extension !== null &&
      extension.valueString !== undefined &&
      extension.valueString !== null
    ) {
      let headerDefinition = JSON.parse(extension.valueString);

      headerDefinition.sections.some(function (section) {
        if (section.type === sectionType && section.showInReport === true) {
          if (section.displayName !== undefined && section.displayName !== null && section.displayName.length > 0) {
            headerHtml = section;
          }
        }
      });
    }
  }
  return headerHtml;
}

export function extractResultPageVariableName(section, variableCode) {
  let variableName;
  section.variables.forEach((variable) => {
    if (variable.variable === variableCode && variable.showInReport === true) {
      variableName = variable.variableName;
    }
  });
  return variableName;
}

export function extractResultPageSection(questionnaireJSON, extensionUrl, sectionType) {
  let sectionBlock = null;
  if (questionnaireJSON !== undefined && questionnaireJSON.extension !== undefined) {
    let extension = extractExtension(questionnaireJSON.extension, extensionUrl);
    if (
      extension !== undefined &&
      extension !== null &&
      extension.valueString !== undefined &&
      extension.valueString !== null
    ) {
      let headerDefinition = JSON.parse(extension.valueString);
      headerDefinition.sections.forEach((section) => {
        if (section.type === sectionType && section.showInReport === true) {
          sectionBlock = section;
        }
      });
    }
  }
  return sectionBlock;
}

export function extractHTMLResult(questionnaireJSON, extensionUrl) {
  let htmlResult = null;
  if (questionnaireJSON !== undefined && questionnaireJSON.extension !== undefined) {
    let extension = extractExtension(questionnaireJSON.extension, extensionUrl);
    if (
      extension !== undefined &&
      extension !== null &&
      extension.valueString !== undefined &&
      extension.valueString !== null
    ) {
      htmlResult = extension.valueString;
    }
  }
  return htmlResult;
}

export function getSequencedInstrumentScores(variables, instrumentScores) {
  let sequencedInstrumentScores = instrumentScores;

  if (variables && variables.length && instrumentScores && instrumentScores.length) {
    // transform variables into object with key value page { variable: sequence }
    var variablesObject = Object.fromEntries(variables.map(({ variable, sequence }) => [variable, sequence]));

    sequencedInstrumentScores = structuredClone(instrumentScores).sort(
      ({ scoreDefinitionName: scoreDefinitionNameA }, { scoreDefinitionName: scoreDefinitionNameB }) => {
        if (scoreDefinitionNameA in variablesObject && scoreDefinitionNameB in variablesObject) {
          return variablesObject[scoreDefinitionNameA] - variablesObject[scoreDefinitionNameB];
        } else if (scoreDefinitionNameA in variablesObject) {
          return -1;
        } else if (scoreDefinitionNameB in variablesObject) {
          return 1;
        } else return scoreDefinitionNameA < scoreDefinitionNameB ? -1 : 1;
      },
    );

    return sequencedInstrumentScores;
  } else {
    return sequencedInstrumentScores;
  }
}

export function isResultPagePresent(questionnaire) {
  let isResultPagePresent = false;
  let extension = extractExtension(questionnaire.extension, 'result-page');
  if (extension) {
    let extensionObj = JSON.parse(extension.valueString);
    if (extensionObj && extensionObj.sections && extensionObj.sections.length) {
      isResultPagePresent = true;
    }
  }
  return isResultPagePresent;
}

export function isDisplayQuestionSection(questionnaire) {
  let isDisplayQuestionSection = true;
  let extension = extractExtension(questionnaire.extension, 'result-page');
  if (extension) {
    let extensionObj = JSON.parse(extension.valueString);
    if (extensionObj && extensionObj.sections && extensionObj.sections.length) {
      for (let section of extensionObj.sections) {
        if (section.type === 'Questions & Answers' && section.showInReport === true) {
          isDisplayQuestionSection = true;
          break;
        } else {
          isDisplayQuestionSection = false;
        }
      }
    } else {
      isDisplayQuestionSection = true;
    }
  }
  return isDisplayQuestionSection;
}

function getQuestionAnswerValue(question) {
  let answerValue = '';
  if (question && question.type !== 'complex') {
    let questionType = question.question.type;

    if (Array.isArray(question.answer)) {
      if (!question.answer.length) {
        answerValue = '';
      } else {
        question.answer.forEach((answer, index) => {
          if (answer && answer?.valueCoding && answer?.valueCoding?.display) {
            let displayText = answer.valueCoding.display;
            const otherOptionExtension = answer.valueCoding.extension?.find(
              (ext) => ext.url === 'Item/AnswerOption/ValueCoding/other-option',
            );
            if (otherOptionExtension && otherOptionExtension.valueString) {
              const answerText = otherOptionExtension.valueString.split('answer:')[1];
              displayText = `${displayText}:${answerText}`;
            }
            answerValue += displayText + (question.answer.length === index + 1 ? '' : ', ');
          }
        });
      }
    } else if (questionType === 'decimal') {
      answerValue = question.answer.valueDecimal;
    } else if (questionType === 'integer') {
      answerValue = question.answer.valueInteger;
    } else if (questionType === 'text') {
      answerValue = question.answer.valueString;
    } else if (questionType === 'date') {
      answerValue = format(new Date(question.answer.valueDate), 'yyyy-MM-dd');
    } else if (questionType === 'dateTime') {
      answerValue = format(new Date(question.answer.valueDateTime), 'yyyy-MM-dd HH:mm:ss');
    } else if (question.type === 'choice') {
      let displayText = question.answer.valueCoding.display;
      const otherOptionExtension = question.answer.valueCoding.extension?.find(
        (ext) => ext.url === 'Item/AnswerOption/ValueCoding/other-option',
      );
      if (otherOptionExtension && otherOptionExtension.valueString) {
        const answerText = otherOptionExtension.valueString.split('answer:')[1];
        displayText = `${displayText}:${answerText}`;
      }
      answerValue = displayText;
    }
  }
  return answerValue;
}

export function getQuestionnaireCompletionDate(questionnaireResponse) {
  let date = new Date();
  let completionDateTime = questionnaireResponse.completionDate || questionnaireResponse.authored;

  if (completionDateTime) date = completionDateTime;

  date = new Date(date);
  return date;
}

function getReportDateFormat(dateSection) {
  let format = 'YYYY-MM-DD HH:mm:ss';
  if (dateSection && dateSection.fields && dateSection.fields[0] && dateSection.fields[0].format) {
    format = dateSection.fields[0].format;
  }

  return format.replace('YYYY', 'yyyy').replace('DD', 'dd');
}

let functionExport = {
  createAnswer,
  createGridQuestionAnswer,
  buildCodingSystemMap,
  buildQuestionList,
  buildQuestionSequenceList,
  findQuestionResponse,
  buildQuestionMap,
  buildQuestionnairePageList,
  calculateQuestionnairePageLength,
  getCalculatedScores,
  calculateProgress,
  extractExtension,
  extractAllMatchingExtension,
  isQuestionAnswered,
  isQuestionComplete,
  isRequired,
  getQuestionType,
  convertDateToLocaleString,
  isQuestionVisible,
  hasVisibleComponent,
  extractResultPageHtml,
  isResultPagePresent,
  isDisplayQuestionSection,
  isGridQuestionAnswered,
  getQuestionnaireStartPageNumber,
  getQuestionnaireCompletionDate,
  extractResultPageSection,
  getReportDateFormat,
  extractHTMLResult,
  getQuestionId,
  getQuestionAnswerValue,
  getScoreOfAnswers,
};

export default functionExport;
