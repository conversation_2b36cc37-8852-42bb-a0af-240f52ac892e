export const FHIR_QUESTIONNAIRE_EQ5D5L_WITH_HEADER = {
  resourceType: 'Questionnaire',
  id: 'e73d02bb-0bae-4300-8da5-e9630c138f2f',
  extension: [
    {
      url: 'Questionnaire/display-dial',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-description',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-large-buttons',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-progress-bar',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/display-score',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-score-category',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/display-title',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/questionnaire-type',
      valueCode: 'Instrument',
    },
    {
      url: 'Questionnaire/question-unit-per-page',
      valueBoolean: true,
    },
    {
      url: 'Questionnaire/trendable',
      valueBoolean: false,
    },
    {
      url: 'Questionnaire/result-page',
      valueString:
        '{"sections":[{"type":"Label","displayName":"<p style=\\"text-align: center; \\"><span style=\\"font-size: 18px;\\"><b>EQ-5D-5L Result</b></span><br></p>","showInReport":true,"htmlText":"","fields":[],"variables":[]},{"name":"DateTime","displayName":"<p style=\\"text-align: center; \\">Date On:&nbsp;</p>","type":"Date","showInReport":true,"htmlText":"<p style=\\"text-align: center; \\">Date On:&nbsp;<span id=\\"dateString\\" style=\\"font-size: 14px; color: rgb(57, 132, 198);\\">[COMPLETION_DATE]</span></p>","fields":[{"format":"MMMM Do YYYY, HH:mm","name":"","displayName":"<span id=\\"dateString\\" style=\\"font-size: 14px; color: rgb(57, 132, 198);\\">[COMPLETION_DATE]</span>","sequence":1,"showInReport":true}],"variables":[]},{"type":"Demographics","displayName":"","showInReport":true,"htmlText":"","fields":[{"name":"firstName","displayName":"First Name","sequence":1,"showInReport":true,"code":"FIRST_NAME","format":""},{"name":"lastName","displayName":"Last Name","sequence":2,"showInReport":true,"code":"LAST_NAME","format":""},{"name":"phn","displayName":"PHN","sequence":3,"showInReport":true,"code":"PHN","format":""},{"name":"dateOfBirth","displayName":"Date of Birth","sequence":4,"showInReport":true,"code":"DOB","format":""},{"name":"gender","displayName":"Gender","sequence":5,"showInReport":true,"code":"GENDER","format":""},{"name":"email","displayName":"Email","sequence":6,"showInReport":true,"code":"EMAIL","format":""},{"name":"participantId","displayName":"Participant ID","sequence":7,"showInReport":true,"code":"PARTICIPANT_ID","format":""}],"variables":[]},{"type":"Variables","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[{"variableName":"VAR1","variable":"KFT","showInReport":true,"mode":"view","sequence":1,"variableDetail":{"displayName":""},"formats":[{"formatName":"currency","formatValue":"$","showInReport":true},{"formatName":"percent","formatValue":"%","showInReport":false},{"formatName":"decimalPlace","formatValue":"2","showInReport":true}]},{"variableName":"VAR2","variable":"LFT","showInReport":true,"mode":"view","sequence":2,"variableDetail":{"displayName":""},"formats":[{"formatName":"currency","formatValue":"$","showInReport":false},{"formatName":"percent","formatValue":"%","showInReport":true},{"formatName":"decimalPlace","formatValue":"2","showInReport":true}]},{"variableName":"VAR3","variable":"Lipid","showInReport":true,"mode":"view","sequence":3,"variableDetail":{"displayName":""},"formats":[{"formatName":"currency","formatValue":"$","showInReport":false},{"formatName":"percent","formatValue":"%","showInReport":false},{"formatName":"decimalPlace","formatValue":"2","showInReport":true}]},{"variableName":"VAR4","variable":"Score_","showInReport":true,"mode":"view","sequence":4,"variableDetail":{"displayName":""},"formats":[{"formatName":"currency","formatValue":"$","showInReport":false},{"formatName":"percent","formatValue":"%","showInReport":false},{"formatName":"decimalPlace","formatValue":"0","showInReport":false}]}]},{"type":"Questions & Answers","displayName":"","showInReport":true,"htmlText":"","fields":[],"variables":[]}]}',
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: 'e73d02bb-0bae-4300-8da5-e9630c138f2f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 0,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'dummy',
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: 'e73d02bb-0bae-4300-8da5-e9630c138f2f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 2,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'KFT',
        },
      ],
    },
    {
      url: 'Questionnaire/list-of-score-definitions',
      extension: [
        {
          url: 'Questionnaire/score-id',
          valueCode: 'e73d02bb-0bae-4300-8da5-e9630c138f2f',
        },
        {
          url: 'Questionnaire/score-sequence',
          valueInteger: 3,
        },
        {
          url: 'Questionnaire/score-name',
          valueString: 'LFT',
        },
      ],
    },
  ],
  identifier: [
    {
      system: 'urn:uuid',
      value: 'e73d02bb-0bae-4300-8da5-e9630c138f2f',
    },
  ],
  name: 'EQ-5D-5L',
  title: 'EQ-5D-5L-Result',
  status: 'active',
  date: '2022-04-11T03:06:24-05:00',
  publisher: 'cambian',
  description:
    'EQ-5D-5L Health Questionnaire\nEnglish version for Canada\n\nCopyright© EuroQol Research Foundation. EQ-5DTM is a trade mark of the EuroQol Research Foundation',
  item: [
    {
      id: 'group-559217',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      linkId: '1',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '559220',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          linkId: '1',
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">MOBILITY</span><br></p>',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '559224',
                code: '0',
                display: 'I am unable to walk about',
              },
            },
            {
              valueCoding: {
                id: '559226',
                code: '25',
                display: 'I have severe problems in walking about',
              },
            },
            {
              valueCoding: {
                id: '559228',
                code: '50',
                display: 'I have moderate problems in walking about',
              },
            },
            {
              valueCoding: {
                id: '559230',
                code: '75',
                display: 'I have slight problems in walking about',
              },
            },
            {
              valueCoding: {
                id: '559232',
                code: '100',
                display: 'I have no problems in walking about',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-559236',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      linkId: '2',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '559239',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          linkId: '1',
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">SELF-CARE</span><br></p>',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '559243',
                code: '0',
                display: 'I am unable to wash or dress myself',
              },
            },
            {
              valueCoding: {
                id: '559245',
                code: '25',
                display: 'I have severe problems washing or dressing myself',
              },
            },
            {
              valueCoding: {
                id: '559247',
                code: '50',
                display: 'I have moderate problems washing or dressing myself',
              },
            },
            {
              valueCoding: {
                id: '559249',
                code: '75',
                display: 'I have slight problems washing or dressing myself',
              },
            },
            {
              valueCoding: {
                id: '559251',
                code: '100',
                display: 'I have no problems washing or dressing myself',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-559255',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      linkId: '3',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '559258',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          linkId: '1',
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">USUAL ACTIVITIES (</span><i style="color: rgb(4, 4, 4); font-size: 15.4px;">e.g. work, study, housework, family or leisure activities</i><span style="color: rgb(4, 4, 4); font-size: 15.4px;">)</span><br></p>',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '559262',
                code: '0',
                display: 'I am unable to do my usual activities',
              },
            },
            {
              valueCoding: {
                id: '559264',
                code: '25',
                display: 'I have severe problems doing my usual activities',
              },
            },
            {
              valueCoding: {
                id: '559266',
                code: '50',
                display: 'I have moderate problems doing my usual activities',
              },
            },
            {
              valueCoding: {
                id: '559268',
                code: '75',
                display: 'I have slight problems doing my usual activities',
              },
            },
            {
              valueCoding: {
                id: '559270',
                code: '100',
                display: 'I have no problems doing my usual activities',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-559274',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      linkId: '4',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '559277',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          linkId: '1',
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">PAIN / DISCOMFORT</span><br></p>',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '559281',
                code: '0',
                display: 'I have extreme pain or discomfort',
              },
            },
            {
              valueCoding: {
                id: '559283',
                code: '25',
                display: 'I have severe pain or discomfort',
              },
            },
            {
              valueCoding: {
                id: '559285',
                code: '50',
                display: 'I have moderate pain or discomfort',
              },
            },
            {
              valueCoding: {
                id: '559287',
                code: '75',
                display: 'I have slight pain or discomfort',
              },
            },
            {
              valueCoding: {
                id: '559289',
                code: '100',
                display: 'I have no pain or discomfort',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-559293',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      linkId: '5',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '559296',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'Please select the ONE button that best describes your health TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/multiple-answer-choice',
              valueBoolean: false,
            },
          ],
          linkId: '1',
          text: '<p><span style="color: rgb(4, 4, 4); font-size: 15.4px;">ANXIETY / DEPRESSION</span><br></p>',
          type: 'choice',
          required: false,
          answerOption: [
            {
              valueCoding: {
                id: '559300',
                code: '0',
                display: 'I am extremely anxious or depressed',
              },
            },
            {
              valueCoding: {
                id: '559302',
                code: '25',
                display: 'I am severely anxious or depressed',
              },
            },
            {
              valueCoding: {
                id: '559304',
                code: '50',
                display: 'I am moderately anxious or depressed',
              },
            },
            {
              valueCoding: {
                id: '559306',
                code: '75',
                display: 'I am slightly anxious or depressed',
              },
            },
            {
              valueCoding: {
                id: '559308',
                code: '100',
                display: 'I am not anxious or depressed',
              },
            },
          ],
        },
      ],
    },
    {
      id: 'group-559312',
      extension: [
        {
          url: 'Questionnaire/Item/description',
          valueString: 'cannot locate string',
        },
      ],
      linkId: '6',
      text: 'cannot locate string',
      type: 'group',
      item: [
        {
          id: '559315',
          extension: [
            {
              url: 'Questionnaire/Item/description',
              valueString: 'YOUR HEALTH TODAY',
            },
            {
              url: 'Questionnaire/Item/explanation',
              valueString: 'cannot locate string',
            },
            {
              url: 'Questionnaire/Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/display-type',
              valueString: 'numeric-slider',
            },
            {
              url: 'Questionnaire/Item/slider-min-value',
              valueDecimal: 0.0,
            },
            {
              url: 'Questionnaire/Item/slider-max-value',
              valueDecimal: 100.0,
            },
            {
              url: 'Questionnaire/Item/slider-min-label',
              valueString: 'The worst health you can imagine',
            },
            {
              url: 'Questionnaire/Item/slider-max-label',
              valueString: 'The best health you can imagine',
            },
            {
              url: 'Questionnaire/Item/slider-min-exclusion',
              valueBoolean: false,
            },
            {
              url: 'Questionnaire/Item/slider-max-exclusion',
              valueBoolean: false,
            },
          ],
          linkId: '1',
          text: '<ul style="color: rgb(4, 4, 4); font-size: 15.4px;"><li>We would like to know how good or bad your health is TODAY.</li><li>This scale is numbered from 0 to 100.</li><li>100 means the&nbsp;<u>best</u>&nbsp;health you can imagine. 0 means the&nbsp;<u>worst</u>&nbsp;health you can imagine.</li><li>Please click on the scale to indicate how your health is TODAY.</li></ul>',
          type: 'integer',
          required: false,
        },
      ],
    },
  ],
  instrumentScores: [
    {
      scoreDefinitionName: '_mo45',
      score: 0.0,
      scoreDefinitionId: {
        objectType: 'ScoreDefinition',
        idValue: '573564',
        globalId: null,
        version: 0,
      },
    },
    {
      scoreDefinitionName: '_sc45',
      score: 0.0,
      scoreDefinitionId: {
        objectType: 'ScoreDefinition',
        idValue: '573568',
        globalId: null,
        version: 0,
      },
    },
    {
      scoreDefinitionName: '_ua45',
      score: 0.0,
      scoreDefinitionId: {
        objectType: 'ScoreDefinition',
        idValue: '573572',
        globalId: null,
        version: 0,
      },
    },
    {
      scoreDefinitionName: 'KFT',
      score: 1.0,
      scoreDefinitionId: {
        objectType: 'ScoreDefinition',
        idValue: '573576',
        globalId: null,
        version: 0,
      },
    },
    {
      scoreDefinitionName: 'LFT',
      score: 1.0,
      scoreDefinitionId: {
        objectType: 'ScoreDefinition',
        idValue: '573580',
        globalId: null,
        version: 0,
      },
    },
    {
      scoreDefinitionName: 'dummy',
      score: 1.0,
      scoreDefinitionId: {
        objectType: 'ScoreDefinition',
        idValue: '573584',
        globalId: null,
        version: 0,
      },
    },
    {
      scoreDefinitionName: 'Score',
      score: 0.32,
      scoreDefinitionId: {
        objectType: 'ScoreDefinition',
        idValue: '573588',
        globalId: null,
        version: 0,
      },
    },
  ],
};
