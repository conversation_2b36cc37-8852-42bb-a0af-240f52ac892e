export const FHIR_SIMPLE_GRID_QUESTION_RESPONSE = {
  resourceType: 'QuestionnaireResponse',
  identifier: [],
  questionnaire: 'Questionnaire/c565a6d1-a3ad-45b2-9418-6a03f36dc8ff',
  status: 'completed',
  authored: '2024-12-15T16:10:34.947Z',
  extension: [
    {
      url: 'questionnaire-response-type',
      valueCode: 'instrument-response',
    },
    {
      url: 'questionnaire-name',
      valueString: 'data grid html_report',
    },
    {
      url: 'questionnaire-title',
      valueString: 'data grid html_report',
    },
    {
      url: 'calculated-scores',
      extension: [],
    },
  ],
  item: [
    {
      id: 'group-2vNPGB9fuz9fwt8sfPQcQ6',
      linkId: 'Group1',
      extension: [
        {
          url: 'Item/question-group-sequence',
          valueInteger: 1,
        },
        {
          url: 'Item/question-type',
          valueString: 'group',
        },
      ],
      item: [
        {
          id: 'complex-s3mvyCojANqV2L597ukfK6',
          linkId: 'Item1',
          extension: [
            {
              url: 'Item/description',
              valueString: null,
            },
            {
              url: 'Item/explanation',
              valueString: null,
            },
            {
              url: 'Item/explanation-flag',
              valueString: null,
            },
            {
              url: 'Item/trendable',
              valueBoolean: false,
            },
            {
              url: 'Item/horizontal-orientation',
              valueBoolean: false,
            },
            {
              url: 'Item/hide-question',
              valueBoolean: false,
            },
            {
              url: 'Item/question-type-id',
              valueInteger: 5529,
            },
            {
              url: 'Item/question-in-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-group-sequence',
              valueInteger: 1,
            },
            {
              url: 'Item/question-type',
              valueString: 'group',
            },
          ],
          text: 'Data grid 1',
          item: [
            {
              item: [
                {
                  id: 0,
                  linkId: 'cU72iQ4KvsxVSoLUBppd3F',
                  text: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'text',
                    },
                  ],
                  answer: [
                    {
                      valueString: 'r1',
                    },
                  ],
                },
                {
                  id: 1,
                  linkId: '8ae8CMsxi3NSUjyLDGzszC',
                  text: 'num',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'decimal',
                    },
                  ],
                  answer: [
                    {
                      valueInteger: '1',
                    },
                  ],
                },
                {
                  id: 2,
                  linkId: 'x3THmYgnHKupN47YEY4Lc9',
                  text: 'date',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'dateTime',
                    },
                  ],
                  answer: [
                    {
                      valueDate: '2024-12-08',
                    },
                  ],
                },
              ],
            },
            {
              item: [
                {
                  id: 0,
                  linkId: 'cU72iQ4KvsxVSoLUBppd3F',
                  text: 'text',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 1,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'text',
                    },
                  ],
                  answer: [
                    {
                      valueString: 'r2',
                    },
                  ],
                },
                {
                  id: 1,
                  linkId: '8ae8CMsxi3NSUjyLDGzszC',
                  text: 'num',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'decimal',
                    },
                  ],
                  answer: [
                    {
                      valueInteger: '2',
                    },
                  ],
                },
                {
                  id: 2,
                  linkId: 'x3THmYgnHKupN47YEY4Lc9',
                  text: 'date',
                  extension: [
                    {
                      url: 'Item/complex-value-attribute-sequence',
                      valueInteger: 3,
                    },
                    {
                      url: 'Item/row-sequence',
                      valueInteger: 2,
                    },
                    {
                      url: 'Item/question-type',
                      valueString: 'dateTime',
                    },
                  ],
                  answer: [
                    {
                      valueDate: '2024-12-09',
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
  ],
  text: {
    status: 'generated',
    div: 'root <html xmlns="http://www.w3.org/1999/xhtml"><head><style>[data-visible=\'false\'] {  display: none;}</style></head><body><h1>data grid html_report</h1><p>Date: 2024-12-15 21:40:34</p><p>Data Grid: {QuestionnaireResponse.items.Item1}</p><p style="color:gray; font-size:11px;">data grid html_report</p></body></html>',
  },
};
