import React from 'react';
import { Box, Typography, Paper, Grid, IconButton, Link, Chip, Avatar } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import EmailIcon from '@mui/icons-material/Email';
import SmsIcon from '@mui/icons-material/Sms';
import CloudQueueIcon from '@mui/icons-material/CloudQueue';
import dayjs from 'dayjs';

// Format name based on delivery mechanism
const formatName = (name, deliveryMechanism, iconUrl) => {
  if (!name) return null;

  // For HIE messages, show the organization icon before the name
  if (deliveryMechanism === 'HIE') {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        <Avatar
          src={iconUrl || process.env.NEXT_PUBLIC_ICON_NAME}
          alt="Organization Icon"
          sx={{ width: 24, height: 24, mr: 1 }}
        />
        <Typography component="span">{name}</Typography>
      </Box>
    );
  }

  // For EMAIL or SMS, make the name portion bold
  // Extract name from format like "Name <email or phone>"
  const nameMatch = name.match(/(.*)\s*<(.*)>/);
  if (nameMatch && nameMatch.length >= 3) {
    const [_, nameText, contact] = nameMatch;
    return (
      <Typography component="span">
        <strong>{nameText.trim()}</strong> &lt;{contact}&gt;
      </Typography>
    );
  }

  // Default fallback if no match
  return <Typography component="span">{name}</Typography>;
};

export const MessageDetails = ({ message, onClose, onDelete, onMarkAsRead, type }) => {
  if (!message) {
    return (
      <Box sx={{ p: 3, height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography variant="body1">Select a message to view details</Typography>
      </Box>
    );
  }

  const receivedDayjs = dayjs(message.timestamp);
  const isToday = receivedDayjs.isSame(dayjs(), 'day');
  const displayReceived = isToday ? `Today ${receivedDayjs.format('h:mm A')}` : receivedDayjs.format('YYYY-MM-DD');

  // Extract message content based on message structure
  let messageContent = '';
  if (message.content) {
    messageContent = message.content;
  }

  // Determine delivery mechanism icon
  const getDeliveryIcon = () => {
    switch (message.deliveryMechanism) {
      case 'EMAIL':
        return <EmailIcon fontSize="small" sx={{ mr: 0.5 }} />;
      case 'SMS':
        return <SmsIcon fontSize="small" sx={{ mr: 0.5 }} />;
      case 'HIE':
        return <CloudQueueIcon fontSize="small" sx={{ mr: 0.5 }} />;
      default:
        return null;
    }
  };

  // Determine if we're showing from (inbox) or to (sent) details
  const isSent = type === 'sent';

  return (
    <Paper
      elevation={0}
      sx={{
        height: '100%',
        overflow: 'auto',
        border: { xs: 'none', sm: 'none', md: '1px solid #e0e0e0' },
        position: 'relative',
        p: 0,
      }}
    >
      {/* Close button */}
      <IconButton
        onClick={onClose}
        sx={{
          position: 'absolute',
          top: 8,
          right: 8,
          color: 'text.secondary',
          zIndex: 1,
        }}
      >
        <CloseIcon />
      </IconButton>

      {/* Header with sender/recipient and timestamp */}
      <Box
        sx={{
          mb: 3,
          backgroundColor: '#f5f5f5',
          borderRadius: { xs: 0, sm: 0, md: '0 0 4px 4px' },
          pt: 3,
          pb: 2,
          px: { xs: 2, sm: 2, md: 3 },
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={4} sm={3} md={3} lg={2}>
            <Typography variant="body1" color="text.secondary" sx={{ color: '#757575' }}>
              {isSent ? 'To' : 'From'}
            </Typography>
          </Grid>
          <Grid item xs={8} sm={9} md={9} lg={10}>
            <Box sx={{ fontWeight: 600 }}>
              {formatName(
                isSent ? message.recipient || '' : message.sender || '',
                message.deliveryMechanism,
                message.iconUrl,
              )}
            </Box>
          </Grid>

          <Grid item xs={4} sm={3} md={3} lg={2}>
            <Typography variant="body1" color="text.secondary" sx={{ color: '#757575' }}>
              {isSent ? 'Sent' : 'Received'}
            </Typography>
          </Grid>
          <Grid item xs={8} sm={9} md={9} lg={10}>
            <Typography variant="body1" sx={{ fontWeight: 600 }}>
              {displayReceived}
            </Typography>
          </Grid>
        </Grid>
      </Box>

      {/* Message content */}
      <Box sx={{ mb: 2, px: { xs: 2, sm: 2, md: 3 } }}>
        <Typography variant="body1" paragraph>
          <span dangerouslySetInnerHTML={{ __html: messageContent }} />
        </Typography>
      </Box>
    </Paper>
  );
};
