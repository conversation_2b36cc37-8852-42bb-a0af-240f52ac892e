import React from 'react';
import { DateCalendar } from '@mui/x-date-pickers';
import { PickersDay } from '@mui/x-date-pickers';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';

function CambianCalendar(props) {
  const { minDate, date, value, onChange, slots, slotProps, ...rest } = props;

  const CustomDay = ({ selectedDay, ...other }) => {
    return (
      <PickersDay
        {...other}
        sx={{
          [`&&.MuiPickersDay-today`]: {
            border: 'none',
            borderColor: '#000',
          },
          [`&&.Mui-selected`]: {
            backgroundColor: 'cambianCommon.lightGray',
            border: '1px solid',
            borderColor: '#000',
            color: 'text.primary',
          },
        }}
      />
    );
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DateCalendar
        minDate={minDate}
        value={value || date}
        onChange={(newDate) => onChange(newDate)}
        slots={{ day: CustomDay, ...(slots || {}) }}
        slotProps={{
          day: { selectedDay: value || date },
          ...(slotProps || {}),
        }}
        {...rest}
      />
    </LocalizationProvider>
  );
}

export { CambianCalendar };
