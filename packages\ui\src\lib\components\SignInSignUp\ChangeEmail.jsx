import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Link, CircularProgress, Box, useMediaQuery } from '@mui/material';
import { UncontrolledUsernameInput } from './UncontrolledUsernameInput';
import { UncontrolledPassword } from './UncontrolledPassword';
import { validateUsername } from './Regex';
import cambian<PERSON>ogo from './cambian-logo.png';
import { DEFAULT_ERROR_MSG } from './constant';

function ChangeEmail(props) {
  const {
    changeEmailCallback,
    navigateCallback,
    logoUrl = cambianLogo,
    title = 'Change email',
    byline = 'Enter new email and the password you created when joining',
    buttonText = 'Confirm',
    linkBelowButtonText = 'Back',
    orgName = 'Cambian',
    usernameType = 'email',
    currentEmail,
  } = props;

  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const [serverResponseErrorMsg, setServerResponseErrorMsg] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const usernameInputRef = React.useRef();
  const passwordInputRef = React.useRef();
  const [usernameValidSchema, setUsernameValidSchema] = React.useState({});
  const [passwordValidSchema, setPasswordValidSchema] = React.useState({});

  const handleSubmit = async () => {
    const usernameValidationResult = validateUsername({
      min: 1,
      requireEmailFormat: usernameType === 'email',
      value: usernameInputRef.current.value,
    });
    const passwordValidationResult = { isMinLengthValid: passwordInputRef.current.value.length > 0 };
    setUsernameValidSchema(usernameValidationResult);
    setPasswordValidSchema(passwordValidationResult);
    const isUsernameValid = !Object.values(usernameValidationResult).some((v) => v === false);
    const isPasswordValid = passwordValidationResult.isMinLengthValid;

    if (!isUsernameValid) {
      usernameInputRef.current.focus();
    } else if (!isPasswordValid) {
      passwordInputRef.current.focus();
    }

    if (isUsernameValid && isPasswordValid) {
      setIsLoading(true);
      const { success, errorMsg } = await changeEmailCallback({
        newUsername: usernameInputRef.current.value,
        password: passwordInputRef.current.value,
      });
      if (!success) {
        setServerResponseErrorMsg(errorMsg || DEFAULT_ERROR_MSG);
        setIsLoading(false);
      }
    } else {
      setServerResponseErrorMsg(null);
    }
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  return (
    <Stack direction="column" spacing={1.5} sx={{ width: { xs: '100%', sm: '40ch' } }}>
      <Box
        component="img"
        sx={{
          marginLeft: '-1%',
          width: { xs: '50%', sm: '55%' },
          minWidth: '100px',
          minHeight: '30px',
          height: { xs: '40%', sm: '45%' },
          marginBottom: -0.5,
        }}
        src={logoUrl}
        alt={`${orgName} full logo`}
      />
      <Typography sx={{ fontSize: { xs: 24, sm: 30 }, fontWeight: 500 }}>{title}</Typography>
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{byline}</Typography>
      <UncontrolledUsernameInput
        ref={null}
        autoFocus="true"
        orgName={orgName}
        usernameType={usernameType}
        defaultValue={currentEmail}
        labelName={'Current Email'}
        isDisabled={true}
        onKeyDown={handleKeyDown}
      />
      <UncontrolledUsernameInput
        ref={usernameInputRef}
        autoFocus="true"
        orgName={orgName}
        usernameType={usernameType}
        labelName={'New Email'}
        isRequired={true}
        validSchema={usernameValidSchema}
        onKeyDown={handleKeyDown}
      />
      <UncontrolledPassword
        id="password"
        text="Password"
        type="text"
        isRequired={true}
        labelFontSize={{ xs: 14, sm: 16 }}
        inputFontSize={{ xs: 15, sm: 17 }}
        inputBoxSize="small"
        min={1}
        ref={passwordInputRef}
        validSchema={passwordValidSchema}
        onKeyDown={handleKeyDown}
      />
      <Button
        variant="contained"
        disabled={isLoading}
        onClick={handleSubmit}
        sx={{ fontSize: { xs: 17, sm: 19 }, fontWeight: 600, padding: '11.5px 12px', lineHeight: 1 }}
      >
        {isLoading ? <CircularProgress size={isXs ? 15 : 19} /> : buttonText}
      </Button>
      {serverResponseErrorMsg && (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
          {serverResponseErrorMsg}
        </Typography>
      )}
      <Link
        component="button"
        onClick={navigateCallback}
        sx={{ textAlign: 'left', width: 'fit-content', fontSize: { xs: 11, sm: 12 } }}
      >
        {linkBelowButtonText}
      </Link>
    </Stack>
  );
}

export { ChangeEmail };
