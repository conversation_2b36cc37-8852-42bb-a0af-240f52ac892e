import React from 'react';
import { FormControl, TextField, Grid } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import { extractExtension } from '../../utility/questionnaireUtility';

function TextQuestion(props) {
  console.log('TRACE: TextQuestion');
  const { question, handleQuestionResponse } = props;

  const textMinLengthExtension = extractExtension(question.question.extension, 'Item/min-length');
  const textMinLength = textMinLengthExtension ? textMinLengthExtension.valueInteger : undefined;
  const textMaxLengthExtension = extractExtension(question.question.extension, 'Item/max-length');
  const textMaxLength = textMaxLengthExtension ? textMaxLengthExtension.valueInteger : undefined;
  const DEFAULT_TEXT_MAX_LENGTH = 255;

  const [isReadOnly] = React.useState(() => handleQuestionResponse === undefined);

  const extractExistingAnswer = () => {
    let answer = '';
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      answer = question.answer.valueString;
    }
    return answer === null || answer === undefined ? '' : answer;
  };

  const [value, setValue] = React.useState(() => extractExistingAnswer());

  const handleChange = (event) => {
    if (!isReadOnly) {
      let characterLimit = textMaxLength !== undefined ? textMaxLength : DEFAULT_TEXT_MAX_LENGTH;
      setValue(event.target.value.substring(0, characterLimit));
      question.answer = {
        valueString: event.target.value.substring(0, characterLimit),
      };
      handleQuestionResponse(question);
    }
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />
      <Grid container>
        <Grid item xs={12} sm={7}>
          <FormControl variant="outlined" fullWidth sx={{ m: 1, ml: 0, mb: 0 }}>
            {textMaxLength > 255 ? (
              <TextField
                size="small"
                multiline
                minRows={2}
                maxRows={5}
                autoComplete="off"
                value={value}
                onChange={(event) => handleChange(event)}
                placeholder={!isReadOnly ? 'Please enter a text value' : ''}
              />
            ) : (
              <TextField
                id="outlined-adornment-text"
                size="small"
                multiline
                autoComplete="off"
                value={value}
                onChange={(event) => handleChange(event)}
                placeholder={!isReadOnly ? 'Please enter a text value' : ''}
              />
            )}
          </FormControl>
        </Grid>
      </Grid>
      <Explanation question={question} />
    </>
  );
}

export default TextQuestion;
