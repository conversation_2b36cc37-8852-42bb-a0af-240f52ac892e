import React from 'react';
import { TextField } from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { PickersDay } from '@mui/x-date-pickers';

function CambianDatePicker(props) {
  const {
    label,
    required,
    value,
    isReadOnly,
    format,
    mask,
    onChange,
    placeholder,
    error,
    disableFuture,
    fullWidth,
    requiredFieldText,
    invalidFieldText,
    slots,
    slotProps,
    size,
    ...rest
  } = props;

  const [defaultError, setDefaultError] = React.useState(false);

  const getTodayDateStyle = () => {
    if (value === null) {
      return '1px solid';
    } else {
      return 'none';
    }
  };

  const CustomDay = ({ ...other }) => (
    <PickersDay
      {...other}
      sx={{
        [`&&.MuiPickersDay-today`]: {
          border: getTodayDateStyle(),
          borderColor: '#000',
        },
        [`&&.Mui-selected`]: {
          backgroundColor: 'cambianCommon.lightGray',
          border: '1px solid',
          borderColor: '#000',
          color: 'text.primary',
        },
      }}
    />
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DatePicker
        label={label}
        format={format}
        size={size}
        mask={mask}
        fullWidth={fullWidth || false}
        disableFuture={disableFuture}
        value={value}
        onChange={(newValue) => onChange(newValue)}
        readOnly={isReadOnly || false}
        PopperProps={{ disablePortal: true, keepMounted: true }}
        slots={{
          day: CustomDay,
          textField: TextField,
          ...(slots || {}),
        }}
        onError={(err) => setDefaultError(!!err)}
        slotProps={{
          day: { selected_day: value },
          textField: (props) => ({
            error: defaultError || (required && error),
            helperText: defaultError ? (!props.value && required ? requiredFieldText : invalidFieldText) : '',
            placeholder: placeholder || '',
            autoComplete: 'off',
            required: required || false,
            size: size,
          }),
          ...(slotProps || {}),
        }}
        {...rest}
      />
    </LocalizationProvider>
  );
}

export { CambianDatePicker };
