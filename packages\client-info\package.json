{"name": "@cambianrepo/client-info", "publishConfig": {"registry": "https://npm.pkg.github.com/cambianrepo"}, "version": "0.0.2", "type": "module", "engines": {"node": ">=20.0.0", "npm": "please use YARN", "yarn": ">= 1.22.18"}, "scripts": {"lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "rollup": "rollup -c", "format": "prettier --write \"./src/**/*.{js,jsx,css,md,json}\" --config ./.prettierrc"}, "peerDependencies": {"@mui/material": "^5.15.15", "@mui/icons-material": "^5.15.15", "moment": "^2.30.1", "react": "^18.2.0"}, "dependencies": {"@cambianrepo/ui": "0.0.45"}, "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "files": ["dist"]}