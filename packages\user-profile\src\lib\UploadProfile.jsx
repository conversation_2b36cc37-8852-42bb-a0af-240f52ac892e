import React from 'react';
import { Stack } from '@mui/material';
import { useForm, FormProvider } from 'react-hook-form';
import IconInput from './IconInput';

function UploadProfile(props) {
  const { photoImageUrl, changeCallback } = props;
  const methods = useForm();

  return (
    <Stack direction="row" alignItems="center">
      <label htmlFor="contained-button-file">
        <FormProvider {...methods}>
          <IconInput
            accept="image/*"
            maxWidth="300px"
            inputName="logoSrcImg"
            control={methods.control}
            changeCallback={changeCallback}
            photoImageUrl={photoImageUrl}
          />
        </FormProvider>
      </label>
    </Stack>
  );
}

export { UploadProfile };
