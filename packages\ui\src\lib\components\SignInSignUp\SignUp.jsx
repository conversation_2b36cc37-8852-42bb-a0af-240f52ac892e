import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typography,
  Link,
  CircularProgress,
  FormHelperText,
  useMediaQuery,
  Box,
} from '@mui/material';
import { validateUsername, validatePassword } from './Regex';
import { UncontrolledPassword } from './UncontrolledPassword';
import { UncontrolledUsernameInput } from './UncontrolledUsernameInput';
import cambianLogo from './cambian-logo.png';
import { USER_AGREEMENT_PDF_BASE64 } from './NavigatorUserAgreementPdf';
import { PRIVACY_STATEMENT_PDF_BASE64 } from './NavigatorPrivacyStatement';
import { DEFAULT_ERROR_MSG } from './constant';

const getPdfBlob = (base64str) => {
  // decode base64 string, remove space for IE compatibility
  const binary = window.atob(base64str.replace(/\s/g, ''));
  const len = binary.length;
  const buffer = new ArrayBuffer(len);
  const view = new Uint8Array(buffer);
  for (let index = 0; index < len; index++) {
    view[index] = binary.charCodeAt(index);
  }

  const blob = new Blob([view], { type: 'application/pdf' });
  const pdfUrl = URL.createObjectURL(blob);

  return pdfUrl;
};

function SignUp(props) {
  const {
    signUpCallback,
    navigateCallback,
    passwordRestriction: { min = 8, max = 30, requireLowercase, requireUppercase, requireNumber, requireSpecial } = {},
    logoUrl = cambianLogo,
    userAgreementDocumentUrl = getPdfBlob(USER_AGREEMENT_PDF_BASE64),
    privacyDocumentUrl = getPdfBlob(PRIVACY_STATEMENT_PDF_BASE64),
    title = 'Sign up',
    byline = 'Create a free Cambian account in a few seconds',
    buttonText = 'Agree & Create',
    textBelowButton = 'Have a Cambian account?',
    linkBelowButtonText = 'Sign in',
    orgName = 'Cambian',
    usernameType = 'email',
  } = props;

  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));
  const usernameInputRef = React.useRef();
  const passwordInputRef = React.useRef();
  const firstNameInputRef = React.useRef();
  const lastNameInputRef = React.useRef();
  const stackRef = React.useRef();

  const [usernameValidSchema, setUsernameValidSchema] = React.useState({});
  const [passwordValidSchema, setPasswordValidSchema] = React.useState({});
  const [firstNameValidSchema, setFirstNameValidSchema] = React.useState({});
  const [lastNameValidSchema, setLastNameValidSchema] = React.useState({});

  const [serverResponseErrorMsg, setServerResponseErrorMsg] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(false);

  const validateForm = () => {
    const usernameValidationResult = validateUsername({
      min: 1,
      requireEmailFormat: usernameType === 'email',
      value: usernameInputRef.current.value,
    });
    const passwordValidationResult = validatePassword({
      min,
      max,
      requireLowercase,
      requireUppercase,
      requireNumber,
      requireSpecial,
      value: passwordInputRef.current.value,
    });
    const firstNameValidationResult = { isMinLengthValid: firstNameInputRef.current.value.length > 0 };
    const lastNameValidationResult = { isMinLengthValid: lastNameInputRef.current.value.length > 0 };
    setUsernameValidSchema(usernameValidationResult);
    setPasswordValidSchema(passwordValidationResult);
    setFirstNameValidSchema(firstNameValidationResult);
    setLastNameValidSchema(lastNameValidationResult);

    return { usernameValidationResult, passwordValidationResult, firstNameValidationResult, lastNameValidationResult };
  };

  const handleSubmit = async () => {
    const { usernameValidationResult, passwordValidationResult, firstNameValidationResult, lastNameValidationResult } =
      validateForm();

    const isUsernameValid = !Object.values(usernameValidationResult).some((v) => v === false);
    const isPasswordValid = !Object.values(passwordValidationResult).some((v) => v === false);
    const isFirstNameValid = firstNameValidationResult.isMinLengthValid;
    const isLastNameValid = lastNameValidationResult.isMinLengthValid;

    if (!isFirstNameValid) {
      firstNameInputRef.current.focus();
    } else if (!isLastNameValid) {
      lastNameInputRef.current.focus();
    } else if (!isUsernameValid) {
      usernameInputRef.current.focus();
    } else if (!isPasswordValid) {
      passwordInputRef.current.focus();
    }

    if (isUsernameValid && isPasswordValid && isFirstNameValid && isLastNameValid) {
      setIsLoading(true);
      const { success, errorMsg } = await signUpCallback({
        username: usernameInputRef.current.value,
        password: passwordInputRef.current.value,
        firstName: firstNameInputRef.current.value,
        lastName: lastNameInputRef.current.value,
      });
      if (!success) {
        console.error(errorMsg);
        if (errorMsg) {
          setServerResponseErrorMsg(errorMsg);
        } else {
          setServerResponseErrorMsg(DEFAULT_ERROR_MSG);
        }
        setIsLoading(false);
      }
    } else {
      setServerResponseErrorMsg(null);
    }
  };

  const isFirstNameInvalid = firstNameValidSchema.isMinLengthValid === false;
  const isLastNameInvalid = lastNameValidSchema.isMinLengthValid === false;

  const handleLinkButtonClicked = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
    navigateCallback();
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  return (
    <Stack direction="column" spacing={1.5} sx={{ width: { xs: '100%', sm: '40ch' } }}>
      <Box
        component="img"
        sx={{
          marginLeft: '-1%',
          width: { xs: '50%', sm: '55%' },
          minWidth: '100px',
          minHeight: '30px',
          height: { xs: '40%', sm: '45%' },
          marginBottom: -0.5,
        }}
        src={logoUrl}
        alt={`${orgName} full logo`}
      />
      <Typography sx={{ fontSize: { xs: 24, sm: 30 } }} fontWeight={500}>
        {title}
      </Typography>
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{byline}</Typography>
      <TextField
        autoFocus
        id="firstName"
        label="First Name"
        defaultValue=""
        inputProps={{ style: { fontSize: isXs ? 14 : 17 } }}
        InputLabelProps={{ style: { fontSize: isXs ? 13 : 16, marginTop: isXs ? 2 : undefined } }}
        InputProps={{ label: isXs ? 'First Na_' : 'First Name' }}
        size="small"
        error={isFirstNameInvalid}
        inputRef={firstNameInputRef}
        onKeyDown={handleKeyDown}
      />
      {isFirstNameInvalid && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '0.2px !important' }}>
          Please enter your first name
        </FormHelperText>
      )}
      <TextField
        id="lastName"
        label="Last Name"
        defaultValue=""
        inputProps={{ style: { fontSize: isXs ? 14 : 17 } }}
        InputLabelProps={{ style: { fontSize: isXs ? 13 : 16, marginTop: isXs ? 2 : undefined } }}
        InputProps={{ label: isXs ? 'Last Na_' : 'Last Name' }}
        size="small"
        error={isLastNameInvalid}
        inputRef={lastNameInputRef}
        onKeyDown={handleKeyDown}
      />
      {isLastNameInvalid && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '0.2px !important' }}>
          Please enter your last name
        </FormHelperText>
      )}
      <UncontrolledUsernameInput
        ref={usernameInputRef}
        orgName={orgName}
        usernameType={usernameType}
        validSchema={usernameValidSchema}
        onKeyDown={handleKeyDown}
      />
      <UncontrolledPassword
        id="password"
        text="Password"
        type="text"
        min={min}
        max={max}
        requireLowercase={requireLowercase}
        requireUppercase={requireUppercase}
        requireNumber={requireNumber}
        requireSpecial={requireSpecial}
        labelFontSize={{ xs: 14, sm: 16 }}
        inputFontSize={{ xs: 15, sm: 17 }}
        inputBoxSize="small"
        ref={passwordInputRef}
        validSchema={passwordValidSchema}
        onKeyDown={handleKeyDown}
      />
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>
        {`You agree to Cambian's `}
        <Link href={userAgreementDocumentUrl} target="_blank">
          User Agreement
        </Link>
        {` and `}
        <Link href={privacyDocumentUrl} target="_blank">
          Privacy Statement
        </Link>
      </Typography>

      <Button
        variant="contained"
        disabled={isLoading}
        onClick={handleSubmit}
        sx={{ fontSize: { xs: 17, sm: 19 }, fontWeight: 600, padding: '11.5px 12px', lineHeight: 1 }}
      >
        {isLoading ? <CircularProgress size={isXs ? 15 : 19} /> : buttonText}
      </Button>
      {serverResponseErrorMsg && (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
          {serverResponseErrorMsg}
        </Typography>
      )}
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{textBelowButton}</Typography>
      <Link
        component="button"
        onClick={handleLinkButtonClicked}
        sx={{ textAlign: 'left', width: 'fit-content', fontSize: { xs: 11, sm: 12 } }}
      >
        {linkBelowButtonText}
      </Link>
    </Stack>
  );
}

export { SignUp };
