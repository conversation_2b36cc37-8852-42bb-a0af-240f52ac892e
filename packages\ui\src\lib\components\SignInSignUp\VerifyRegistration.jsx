import React, { useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>ield,
  Typography,
  Link,
  CircularProgress,
  FormHelperText,
  useMediaQuery,
  Box,
} from '@mui/material';
import cambianLogo from './cambian-logo.png';
import { EmailResentMessage } from './EmailResentMessage';

const DEFAULT_ERROR_MSG = "You've entered an invalid code. Please try again.";
//otp = One Time Password
function VerifyRegistration(props) {
  const {
    authFlowTimeoutCallback,
    verifyRegistrationCallback,
    navigateCallback,
    resendVerificationCodeCallback,
    navigateToChangeEmailCallback,
    emailAddress = 'your email',
    logoUrl = cambianLogo,
    title = 'Verify your email address',
    byline = (
      <>
        {`Enter the verification code sent to `}
        <Typography component="span" variant="inherit" sx={{ fontWeight: 'bold' }}>
          {emailAddress || 'your email'}
        </Typography>
        {`. If you do not see it, check your spam folder.`}
      </>
    ),
    buttonText = 'Next',
    linkBelowButtonText = 'Back',
    orgName = 'Cambian',
  } = props;

  useEffect(() => {
    if (authFlowTimeoutCallback) authFlowTimeoutCallback();
  }, [authFlowTimeoutCallback]);

  const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));

  const [verificationCode, setVerificationCode] = React.useState('');
  const [serverResponseErrorMsg, setServerResponseErrorMsg] = React.useState(null);
  const [isLoading, setIsLoading] = React.useState(false);

  // const otpInputRef = React.useRef();
  const [otpValidSchema, setOtpValidSchema] = React.useState({});
  const [emailSentMessage, setEmailSentMessage] = React.useState('');
  const [isResending, setIsResending] = React.useState(false);

  const handleSubmit = async () => {
    const isOtpValid = verificationCode.length > 0;

    if (!isOtpValid) {
      setOtpValidSchema({ isMinLengthValid: false });
      return;
    }

    setOtpValidSchema({ isMinLengthValid: true });
    setIsLoading(true);

    const { success, errorMsg } = await verifyRegistrationCallback({
      verificationCode: verificationCode,
    });

    if (!success) {
      console.error(errorMsg);
      setServerResponseErrorMsg(errorMsg || DEFAULT_ERROR_MSG);
      setIsLoading(false);
    }
  };

  const handleResendVerificationCallback = async () => {
    setIsResending(true);
    setEmailSentMessage('');
    const { success, errorMsg } = await resendVerificationCodeCallback();
    if (!success) {
      console.error(errorMsg);
      if (errorMsg) {
        setServerResponseErrorMsg(errorMsg);
      } else {
        setServerResponseErrorMsg(DEFAULT_ERROR_MSG);
      }
    } else {
      setEmailSentMessage('Email Sent');
    }
    setIsResending(false);
  };

  const handleKeyDown = (event) => {
    if (event.key === 'Enter') {
      handleSubmit();
    }
  };

  const isOtpInvalid = otpValidSchema.isMinLengthValid === false;
  return (
    <Stack direction="column" spacing={1.5} sx={{ width: { xs: '100%', sm: '40ch' } }}>
      <Box
        component="img"
        sx={{
          marginLeft: '-1%',
          width: { xs: '50%', sm: '55%' },
          minWidth: '100px',
          minHeight: '30px',
          height: { xs: '40%', sm: '45%' },
          marginBottom: -0.5,
        }}
        src={logoUrl}
        alt={`${orgName} full logo`}
      />
      <Typography sx={{ fontSize: { xs: 24, sm: 30 }, fontWeight: 500 }}>{title}</Typography>
      <Typography sx={{ fontSize: { xs: 11, sm: 12 } }}>{byline}</Typography>
      <TextField
        autoFocus
        size="small"
        id="verificationCode"
        disabled={isResending || isLoading}
        label="Verification Code"
        inputProps={{ style: { fontSize: isXs ? 14 : 17 } }}
        InputLabelProps={{ style: { fontSize: isXs ? 13 : 16, marginTop: isXs ? 2 : undefined } }}
        InputProps={{ label: isXs ? 'Verification C_' : 'Verification Code' }}
        value={verificationCode}
        onChange={(e) => setVerificationCode(e.target.value?.trim())}
        onKeyDown={handleKeyDown}
      />
      {isOtpInvalid && (
        <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '0.2px !important' }}>
          Please enter your verification code
        </FormHelperText>
      )}
      {setServerResponseErrorMsg && (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} color="error">
          {serverResponseErrorMsg}
        </Typography>
      )}
      <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
        <Button
          variant="contained"
          disabled={isLoading || !verificationCode || isResending}
          onClick={handleSubmit}
          sx={{
            fontSize: { xs: 15, sm: 16 },
            fontWeight: 500,
            padding: '8px 16px',
            lineHeight: 1,
          }}
        >
          {isLoading ? <CircularProgress size={isXs ? 15 : 19} /> : buttonText}
        </Button>
      </Box>

      {resendVerificationCodeCallback ? (
        <Typography sx={{ fontSize: { xs: 11, sm: 12 } }} component={'div'}>
          {/* <EmailResentMessage message={emailSentMessage} /> */}
          Did not get the code?{' '}
          <Box sx={{ display: 'inline-flex', alignItems: 'center' }}>
            <Link
              component="button"
              onClick={handleResendVerificationCallback}
              sx={{ display: 'inline-flex', alignItems: 'center', mr: 1 }}
            >
              Resend the code
            </Link>{' '}
            {isResending && <CircularProgress size={isXs ? 15 : 19} sx={{ ml: 0.5 }} />}
          </Box>
          {navigateToChangeEmailCallback && (
            <>
              or{' '}
              <Link component="button" onClick={navigateToChangeEmailCallback}>
                change your email
              </Link>
            </>
          )}
        </Typography>
      ) : (
        <Link
          component="button"
          onClick={navigateCallback}
          sx={{
            textAlign: 'left',
            width: 'fit-content',
            fontSize: { xs: 11, sm: 12 },
          }}
        >
          {linkBelowButtonText}
        </Link>
      )}
    </Stack>
  );
}

export { VerifyRegistration };
