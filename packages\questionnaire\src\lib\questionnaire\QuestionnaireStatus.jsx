import React from 'react';
import LinearProgress from '@mui/material/LinearProgress';
import { Box, Stack } from '@mui/material';
import { CANNOT_LOCATE_STRING } from '../../../Common/constants';
import dataValidation from '../../../Common/Utils/dataValidation';

function QuestionnaireStatus(props) {
  const { progress, description, isStatusEnabled } = props;

  return (
    <Stack direction="column" alignItems="left" spacing={2}>
      {isStatusEnabled && (
        <Box sx={{ width: '100%', py: '5px' }}>
          <LinearProgress variant="determinate" value={progress} />
        </Box>
      )}
      {description !== CANNOT_LOCATE_STRING && !dataValidation.isDataEmpty(description) && (
        <Box sx={{ width: '100%' }}>
          <div dangerouslySetInnerHTML={{ __html: description }} />
        </Box>
      )}
    </Stack>
  );
}

export default QuestionnaireStatus;
