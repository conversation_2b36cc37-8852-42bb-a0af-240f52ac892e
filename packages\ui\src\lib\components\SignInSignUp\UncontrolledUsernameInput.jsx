import React from 'react';
import { <PERSON><PERSON><PERSON>, FormHelperText, useMediaQuery } from '@mui/material';
import { UncontrolledEmail } from './UncontrolledEmail';

const UncontrolledUsernameInput = React.forwardRef(
  ({ validSchema = {}, usernameType, orgName, autoFocus, labelName, ...props }, ref) => {
    const isXs = useMediaQuery((theme) => theme.breakpoints.down('sm'));
    switch (usernameType) {
      case 'email':
        return (
          <UncontrolledEmail
            id="username"
            label="true"
            autoFocus={autoFocus}
            labelName={labelName || 'Email'}
            type="text"
            labelFontSize={{ xs: 14, sm: 16 }}
            inputFontSize={{ xs: 15, sm: 17 }}
            inputBoxSize="small"
            ref={ref}
            validSchema={validSchema}
            {...props}
          />
        );
      default:
        const isInvalid = validSchema.isMinLengthValid === false;
        return (
          <>
            <TextField
              autoFocus={autoFocus}
              id="username"
              label={`${orgName} ID`}
              inputProps={{ style: { fontSize: isXs ? 14 : 17 } }}
              InputLabelProps={{ style: { fontSize: isXs ? 13 : 16, marginTop: isXs ? 2 : undefined } }}
              InputProps={{ label: isXs ? `${orgName}` : `${orgName} ID` }}
              defaultValue=""
              size="small"
              error={isInvalid}
              inputRef={ref}
              {...props}
            />
            {isInvalid && (
              <FormHelperText error sx={{ fontSize: { xs: 11, sm: 12 }, marginTop: '3px !important' }}>
                Please enter an ID
              </FormHelperText>
            )}
          </>
        );
    }
  },
);

export { UncontrolledUsernameInput };
