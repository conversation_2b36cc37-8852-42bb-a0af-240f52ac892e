import React from 'react';
import { Box, Stack, ToggleButtonGroup, ToggleButton } from '@mui/material';
import { QuestionnaireReport } from './QuestionnaireReport';
import { QuestionnairePdfReport } from './QuestionnairePdfReport';

function QuestionnaireReportViewer(props) {
  const { pdf, height, fhirResponse, isWebReportAvailable } = props;

  const getInitialTabValue = (params) => {
    if (!isWebReportAvailable && pdf) {
      return 'pdf-report';
    } else if (!pdf && fhirResponse) {
      return 'html-report';
    }
    return 'html-report';
  };

  const [tabValue, setTabValue] = React.useState(getInitialTabValue);

  React.useEffect(() => {
    setTabValue(getInitialTabValue);
  }, [pdf, isWebReportAvailable]);

  const handleTabChange = (event, newTabValue) => {
    if (newTabValue === null) return;
    setTabValue(newTabValue);
  };

  return (
    <Box sx={{ width: '100%' }}>
      {isWebReportAvailable && pdf && (
        <Stack direction="row" justifyContent="flex-end" sx={{ mb: 1 }}>
          <ToggleButtonGroup color="primary" value={tabValue} exclusive onChange={handleTabChange}>
            <ToggleButton value="html-report">HTML</ToggleButton>
            <ToggleButton value="pdf-report">PDF</ToggleButton>
          </ToggleButtonGroup>
        </Stack>
      )}
      <Box sx={{ display: tabValue === 'html-report' ? 'auto' : 'none' }}>
        {fhirResponse && <QuestionnaireReport {...props} />}
      </Box>
      <Box sx={{ display: tabValue === 'pdf-report' ? 'auto' : 'none' }}>
        {pdf && <QuestionnairePdfReport pdf={pdf} height={height} />}
      </Box>
    </Box>
  );
}

export { QuestionnaireReportViewer };
