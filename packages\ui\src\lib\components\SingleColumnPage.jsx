import React from 'react';
import { Card, CardContent, Grid, Typography } from '@mui/material';

const SingleColumnPage = (props) => {
  const { title, subtitle, content } = props;
  return (
    <>
      <Typography variant="h1">{title}</Typography>
      {subtitle && <Typography>{subtitle}</Typography>}
      <Grid container mt={2} justifyContent="center">
        <Grid item xs={12}>
          <Card elevation={0} sx={{ p: 2, overflow: 'visible' }}>
            <CardContent sx={{ p: 0 }}></CardContent>
            {content}
          </Card>
        </Grid>
      </Grid>
    </>
  );
};

export default SingleColumnPage;
