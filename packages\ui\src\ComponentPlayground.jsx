import { Route, Routes, useNavigate } from 'react-router-dom';
import GeneralTestPage from './pages/GeneralTestPage';
import NoMatch from './NoMatch';
import { Button, Stack } from '@mui/material';
import React from 'react';

function ComponentPlayground(props) {
  let navigate = useNavigate();
  return (
    <div>
      <Stack direction="row" alignItems="left" useFlexGap flexWrap="wrap" sx={{ gap: 1 }}>
        <Button variant="contained" onClick={() => navigate('general')}>
          General
        </Button>
      </Stack>
      <Routes>
        <Route path="/" element={<GeneralTestPage />} />
        <Route exact path="/general" element={<GeneralTestPage />} />
        <Route path="*" element={<NoMatch />} />
      </Routes>
    </div>
  );
}

export default ComponentPlayground;
