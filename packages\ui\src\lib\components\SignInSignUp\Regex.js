// Same regex pattern used in navigator/source/src/main/resources/static/tmpl/security/userIdentities/user-identity-items.html
const EMAIL_REGEX_PATTERN = '([a-zA-Z0-9_\\-\\.]+)@([a-zA-Z0-9_\\-\\.]+)\\.([a-zA-Z]{2,5})';
export const validateUsername = ({ min = 1, requireEmailFormat, value }) => {
  const testEmailFormat = new RegExp(EMAIL_REGEX_PATTERN);

  return {
    isMinLengthValid: value.length >= min,
    ...(requireEmailFormat && { isEmailFormatValid: testEmailFormat.test(value) }),
  };
};

export const validatePassword = ({
  min = 8,
  max = 30,
  requireLowercase,
  requireUppercase,
  requireNumber,
  requireSpecial,
  value,
}) => {
  const testLower = new RegExp('(?=.*[a-z])[a-zA-Z0-9!@#$%^&*]');
  const testUpper = new RegExp('(?=.*[A-Z])[a-zA-Z0-9!@#$%^&*]');
  const testNumber = new RegExp('(?=.*[0-9])[a-zA-Z0-9!@#$%^&*]');
  const testSpecial = new RegExp('(?=.*[!@#$%^&*])[a-zA-Z0-9!@#$%^&*]');

  if (typeof min === 'number' && typeof max === 'number' && max < min) {
    console.error(
      `Max should be bigger than min in the password validation schema\n Provided inputs: Max: ${max}, Min: ${min}`,
    );
  }

  return {
    ...(typeof min === 'number' && { isMinLengthValid: value.length >= min }),
    ...(typeof max === 'number' && { isMaxLengthValid: value.length <= max }),
    ...(requireLowercase && { containsLowercase: testLower.test(value) }),
    ...(requireUppercase && { containsUppercase: testUpper.test(value) }),
    ...(requireNumber && { containsNumber: testNumber.test(value) }),
    ...(requireSpecial && { containsSpecial: testSpecial.test(value) }),
  };
};
