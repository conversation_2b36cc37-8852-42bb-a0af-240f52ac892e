import React from 'react';
import dayjs from 'dayjs';
import { FormControl, MenuItem, Stack, TextField, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material/styles';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';

function DemographicsPanel(props) {
  const { t } = useTranslation();
  const theme = useTheme();
  const {
    userDetail: { birthMonth, birthDay, birthYear, gender, dateOfBirth },
    updateProfileDataCallback,
  } = props;

  // Ensure the birth date is properly initialized as a Date object
  const initialBirthDate = birthMonth && birthYear && birthDay ? new Date(birthYear, birthMonth - 1, birthDay) : null;

  const localDateTimeBirthDate = dateOfBirth ? new Date(dateOfBirth + 'T00:00:00') : initialBirthDate;

  const [birthDate, setBirthDate] = React.useState(localDateTimeBirthDate);
  const [birthDateError, setBirthDateError] = React.useState(false);

  const handleBirthDateChange = (newValue) => {
    if (newValue === null) {
      setBirthDateError(false);
      setBirthDate(null);
      updateProfileDataCallback('property', 'change', 'dateOfBirth', null);
      return;
    }

    const todayDate = new Date();
    todayDate.setHours(0, 0, 0, 0);

    const currentBirthDate = new Date(newValue);

    const isValid = dayjs(newValue).isValid();
    if (!isValid || currentBirthDate.getTime() > todayDate.getTime()) {
      setBirthDateError(true);
    } else {
      setBirthDateError(false);
      setBirthDate(isValid && currentBirthDate.getTime() <= todayDate.getTime() ? newValue : null);
      updateProfileDataCallback('property', 'change', 'dateOfBirth', isValid ? newValue : null);
    }
  };

  const [selectedGender, setSelectedGender] = React.useState(gender ?? '');

  const handleGenderSelection = (event) => {
    setSelectedGender(event.target.value);
    updateProfileDataCallback('property', 'change', 'gender', event.target.value);
  };

  const handleNoneClick = () => {
    setSelectedGender('');
    updateProfileDataCallback('property', 'change', 'gender', '');
  };

  return (
    <Stack
      direction="column"
      alignItems="flex-start"
      spacing={2}
      sx={{
        '& .MuiInputBase-root': {
          height: '40px',
        },
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        sx={{ flexWrap: 'wrap', maxWidth: '500px', width: '100%' }}
      >
        <FormControl fullWidth size="small" error={birthDateError}>
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              maxDate={dayjs()}
              value={birthDate ? dayjs(birthDate).startOf('day') : null}
              onChange={(e) => {
                const formattedDate = e === null ? null : e.startOf('day').format('YYYY-MM-DD');
                handleBirthDateChange(formattedDate);
              }}
              format={'YYYY-MM-DD'}
              label="Date of Birth"
            />
          </LocalizationProvider>
          {birthDateError && (
            <Typography variant="caption" sx={{ color: 'error.main' }}>
              {t('Please enter a valid Date of Birth.')}
            </Typography>
          )}
        </FormControl>
      </Stack>

      <TextField
        id="gender"
        select
        label={t('Gender')}
        value={selectedGender}
        sx={{ maxWidth: '500px', width: '100%' }}
        onChange={handleGenderSelection}
        onClose={handleNoneClick}
      >
        {selectedGender !== '' && (
          <MenuItem value="" onClick={handleNoneClick}>
            <em>None</em>
          </MenuItem>
        )}
        <MenuItem value="FEMALE">{t('Female')}</MenuItem>
        <MenuItem value="MALE">{t('Male')}</MenuItem>
        <MenuItem value="OTHER">{t('Other')}</MenuItem>
      </TextField>

      <Typography
        variant="caption"
        sx={{ color: theme.palette.primary.main, maxWidth: '395px', letterSpacing: '0.00938em' }}
      >
        {t(
          'Your demographic data is optional and is not shared with others ' +
            'without your explicit agreement. Some organizations may ' +
            'request that you provide this information in order to identify ' +
            'you or to perform analysis. You can always remove all information' +
            ' submitted in these categories.',
        )}
      </Typography>
    </Stack>
  );
}

export default DemographicsPanel;
