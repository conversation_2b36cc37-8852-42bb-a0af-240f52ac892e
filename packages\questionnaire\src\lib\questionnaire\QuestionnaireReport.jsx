import React, { useRef } from 'react';
import {
  Box,
  Grid,
  Paper,
  Button,
  Stack,
  Tooltip,
  Menu,
  MenuItem,
  IconButton,
  ListItemIcon,
  ListItemText,
  Typography,
} from '@mui/material';
import Demographics from './Demographics';
import Scores from './Scores';
import { useReactToPrint } from 'react-to-print';
import QuestionnaireUtility, { extractResultPageHtml } from './utility/questionnaireUtility';
import { Download, MoreVert, Print, Save } from '@mui/icons-material';
import { format } from 'date-fns';
import { buildResultWithHtmlReportTemplate } from './utility/htmlReportUtility';
import cambianLogo from '../../assets/cambian_logo_new.png';
import { Question } from './question';
import './styles.css';

function isValidTimeZone(timeZone) {
  try {
    Intl.DateTimeFormat(undefined, { timeZone });
    return timeZone; // Valid timezone
  } catch (e) {
    return ''; // Invalid timezone
  }
}

export const QuestionnaireReport = (props) => {
  const {
    fhirQuestionnaire,
    fhirResponse,
    headerJson,
    headerComponent,
    defaultHeaderComponent,
    resultNextActionCallback,
    isCallbackPrint,
    closePageCallback,
    viewPdfCallback,
    reportPageUrl,
    saveToCambianAccountCallback,
    demographic,
    browserTimezone,
    nextButtonText,
  } = props;

  const timezone = isValidTimeZone(browserTimezone);

  let questionnaireResponse = {};
  if (fhirResponse !== undefined) {
    questionnaireResponse = fhirResponse;
  }

  let hasPdfTemplate = false;
  let templateName = QuestionnaireUtility.extractExtension(fhirQuestionnaire.extension, 'pdftemplate-name');
  if (templateName !== undefined && templateName !== null) {
    hasPdfTemplate = true;
  }

  const codingSystemMap = new Map();

  const [questionList] = React.useState(() =>
    QuestionnaireUtility.buildQuestionList(fhirQuestionnaire.item, questionnaireResponse, codingSystemMap, true),
  );

  const [questionMap] = React.useState(() => QuestionnaireUtility.buildQuestionMap(questionList));

  const scores = QuestionnaireUtility.getCalculatedScores(questionnaireResponse);

  // const isLargeButtonEnabled = QuestionnaireUtility.extractExtension(
  //   fhirQuestionnaire.extension,
  //   '/display-large-buttons'
  // ).valueBoolean;

  const renderHeader = () => {
    if (headerComponent !== undefined) {
      return headerComponent;
    } else if (headerJson !== undefined) {
      return (
        <Grid item xs={12} sx={{ pb: 3 }}>
          <Box sx={{ mb: 2 }} dangerouslySetInnerHTML={{ __html: headerJson.title }} />
          <Box sx={{ mb: 2 }} dangerouslySetInnerHTML={{ __html: headerJson.date }} />
        </Grid>
      );
    } else {
      let headerTitle = extractResultPageHtml(fhirQuestionnaire, 'result-page', 'Label');
      let dateSection = extractResultPageHtml(fhirQuestionnaire, 'result-page', 'Date');

      let titleHtml = '';
      let dateHtml = '';

      if (dateSection && dateSection.htmlText) {
        const formattedDate = format(formatCompletionDate(), QuestionnaireUtility.getReportDateFormat(dateSection));
        let dateString = dateSection.htmlText.replace('&lt;completion date&gt;', ' ' + formattedDate);
        dateHtml = dateString.replace('[COMPLETION_DATE]', ' ' + formattedDate);
      }

      if (headerTitle) {
        titleHtml = headerTitle.displayName ? headerTitle.displayName : '';
      }

      if (titleHtml !== undefined && dateHtml !== undefined) {
        return (
          <Grid item xs={12} sx={{ pb: 3 }}>
            <Box sx={{ mb: 2 }} dangerouslySetInnerHTML={{ __html: titleHtml }} />
            <Box sx={{ mb: 2 }} dangerouslySetInnerHTML={{ __html: dateHtml }} />
          </Grid>
        );
      } else if (defaultHeaderComponent !== undefined) {
        return defaultHeaderComponent;
      } else {
        return <></>;
      }
    }
  };

  const renderDemographics = () => {
    if (demographic === undefined) {
      return <></>;
    }

    const demographicKeysMap = {
      firstName: 'First Name',
      lastName: 'Last Name',
      phn: 'PHN',
      dateOfBirth: 'DOB',
      address: 'Address',
      email: 'Email',
      phone: 'Phone',
      gender: 'Gender',
    };

    return (
      <Grid container>
        {Object.entries(demographic)?.map(([key, value]) =>
          value ? (
            <Grid item key={key} xs={6}>
              <Typography variant="h5" sx={{ fontSize: '1.6rem' }}>
                {demographicKeysMap[key]}: {value}
              </Typography>
            </Grid>
          ) : (
            <></>
          ),
        )}
      </Grid>
    );
  };

  const renderScore = () => {
    if (scores && scores.length) {
      if (QuestionnaireUtility.isResultPagePresent(fhirQuestionnaire)) {
        let section = QuestionnaireUtility.extractResultPageSection(fhirQuestionnaire, 'result-page', 'Variables');
        if (section) {
          return (
            <Grid item xs={12} sx={{ pb: 3 }}>
              <Scores instrumentScores={scores} section={section} />
            </Grid>
          );
        } else {
          return <></>;
        }
      } else {
        return (
          <Grid item xs={12} sx={{ pb: 3 }}>
            <Scores instrumentScores={scores} />
          </Grid>
        );
      }
    } else {
      return <></>;
    }
  };

  const renderQuestions = () => {
    if (QuestionnaireUtility.isDisplayQuestionSection(fhirQuestionnaire)) {
      return (
        <Grid item xs={12}>
          <Box sx={{ height: '100%', pointerEvents: 'none' }}>
            {questionList.map((question, index) => {
              const hideQuestionInReportExtension = QuestionnaireUtility.extractExtension(
                question.extension || question.question.extension,
                'Item/hide-question',
              );
              const hideQuestionInReport = hideQuestionInReportExtension
                ? hideQuestionInReportExtension.valueBoolean
                : false;

              if (
                !hideQuestionInReport &&
                QuestionnaireUtility.isQuestionVisible(
                  question.question ? question.question : question,
                  fhirQuestionnaire,
                  questionMap,
                )
              ) {
                return (
                  <Box
                    sx={{
                      p: 2,
                      border: 1,
                      borderColor: 'divider',
                      pageBreakInside: 'avoid',
                      breakInside: 'avoid',
                    }}
                    key={'question-component-' + index}
                  >
                    <Question
                      question={question}
                      // isLargeButtonEnabled={isLargeButtonEnabled}
                    />
                  </Box>
                );
              }
            })}
          </Box>
        </Grid>
      );
    } else {
      return <></>;
    }
  };
  const formatCompletionDate = () => {
    let date = new Date();
    let completionDateTime = questionnaireResponse.completionDate || questionnaireResponse.authored;
    let createdDateTime = questionnaireResponse.createdDateTime;
    let updatedDateTime = questionnaireResponse.updatedDateTime;

    if (createdDateTime) date = createdDateTime;
    if (completionDateTime) date = completionDateTime;
    if (updatedDateTime) date = updatedDateTime;

    date = new Date(date);
    return date;
  };

  let completionDate = formatCompletionDate();
  let strCompletionDate = QuestionnaireUtility.convertDateToLocaleString(completionDate);

  const componentRef = useRef();

  //Will refactor this
  const createItemsAndResponsesHtmlTable = (questionnaireResponse, styleOptions = {}) => {
    const items = (questionnaireResponse?.item || []).flatMap((topLevelItem) => topLevelItem?.item || []);
    const hasDataGridResponses = (gridItem) => {
      if (!gridItem?.item?.[0]?.item) return false;
      return gridItem.item.some((row) =>
        row?.item?.some(
          (cell) => cell?.answer?.[0] && Object.values(cell.answer[0]).some((value) => value !== null && value !== ''),
        ),
      );
    };
    const hasItemResponses = (item) => {
      if (item?.answer) {
        return item.answer.some((answer) => Object.values(answer).some((value) => value !== null && value !== ''));
      }
      return false;
    };
    const shouldHideItem = (item) => {
      const hideQuestionExtension = item?.extension?.find((ext) => ext?.url === 'Item/hide-question');
      return hideQuestionExtension?.valueBoolean === true;
    };

    const hasAnyResponses = items.some((item) => {
      if (shouldHideItem(item)) return false;

      if (item?.item && item.item[0]?.item) {
        return hasDataGridResponses(item);
      }
      return hasItemResponses(item);
    });

    if (!hasAnyResponses) {
      return '';
    }

    const {
      tableStyle = 'border-collapse: collapse; width: 100%; border: 1px solid #ccc',
      headerStyle = 'text-align: left; border: 1px solid #ccc; padding: 8px; min-width:200px;',
      itemStyle = 'text-align: left; border: 1px solid #ccc; padding: 8px;',
      responseStyle = 'text-align: left; border: 1px solid #ccc; padding: 8px;',
      gridStyle = 'border-collapse: collapse; width: 100%; border: 1px solid #ccc',
      gridHeaderStyle = 'text-align: left; border: 1px solid #ccc; padding: 8px;',
      gridCellStyle = 'border: 1px solid #ddd; padding: 4px;',
    } = styleOptions;

    let html = `
<table style="${tableStyle}">
<thead>
  <tr>
    <th style="${headerStyle}">Item</th>
    <th style="${headerStyle}">Response</th>
  </tr>
</thead>
<tbody>
`;

    // Filter out hidden items in addition to existing filters
    const filteredItems = items.filter((item) => {
      // Skip items that should be hidden
      if (shouldHideItem(item)) return false;

      if (item?.item && item.item[0]?.item) {
        return hasDataGridResponses(item);
      }
      return hasItemResponses(item);
    });

    filteredItems.forEach((item) => {
      const itemText = item.text || '';
      let responseContent = '';

      if (item?.item && item.item[0]?.item) {
        if (hasDataGridResponses(item)) {
          const gridColumns = item.item[0].item.map((col) => col.text || '');
          const gridRows = item.item.filter((row) =>
            row?.item?.some(
              (cell) => cell?.answer?.[0] && Object.values(cell.answer[0]).some((value) => value !== null),
            ),
          );

          // Create a nested table for data grid
          responseContent = `
    <table style="${gridStyle}">
      <thead>
        <tr>
          ${gridColumns.map((col) => `<th style="${gridHeaderStyle}">${col}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
        ${gridRows
          .map(
            (row) => `
            <tr>
              ${(row?.item || [])
                .map((cell) => {
                  const answer = cell?.answer?.[0];
                  const value =
                    answer?.valueString ||
                    answer?.valueInteger ||
                    answer?.valueDecimal ||
                    answer?.valueDate ||
                    answer?.valueDateTime ||
                    '';
                  return `<td style="${gridCellStyle}">${value}</td>`;
                })
                .join('')}
            </tr>
          `,
          )
          .join('')}
      </tbody>
    </table>
  `;
        }
      } else {
        const responses = item?.answer
          ? item.answer
              .map((answer) => {
                if (!answer) return '';
                if (answer.valueString) return answer.valueString;
                if (answer.valueInteger) return answer.valueInteger.toString();
                if (answer.valueDecimal) return answer.valueDecimal.toString();
                if (answer.valueDateTime) return answer.valueDateTime;
                if (answer.valueDate) return answer.valueDate;

                if (answer.valueCoding) {
                  const otherOptionExt = answer.valueCoding?.extension?.find(
                    (ext) => ext?.url === 'Item/AnswerOption/ValueCoding/other-option',
                  );

                  if (otherOptionExt?.valueString) {
                    const match = otherOptionExt.valueString.match(/answer:(.+)$/);
                    return match ? match[1] : answer.valueCoding.display || answer.valueCoding.code;
                  }

                  return answer.valueCoding.display || answer.valueCoding.code;
                }

                return '';
              })
              .filter((response) => response !== '')
              .join(', ')
          : '';

        responseContent = responses;
      }

      if (responseContent) {
        html += `
  <tr>
    <td style="${itemStyle}">${itemText}</td>
    <td style="${responseStyle}">${responseContent}</td>
  </tr>
`;
      }
    });

    html += `
</tbody>
</table>
`;

    return html;
  };

  const renderHtmlReport = () => {
    const reportHtmlWithValues = buildResultWithHtmlReportTemplate(
      fhirQuestionnaire,
      questionnaireResponse,
      questionMap,
      scores,
      demographic,
      timezone,
    );
    const styleMatch = reportHtmlWithValues.match(
      /\{QuestionnaireResponse\.itemsAndResponses(?::(\w+)\(([^)]+)\))?(?::(\w+)\(([^)]+)\))?(?::(\w+)\(([^)]+)\))?(?::(\w+)\(([^)]+)\))?\}/,
    );

    const styleOptions = styleMatch
      ? {
          ...(styleMatch[1] && { [styleMatch[1]]: styleMatch[2] }),
          ...(styleMatch[3] && { [styleMatch[3]]: styleMatch[4] }),
          ...(styleMatch[5] && { [styleMatch[5]]: styleMatch[6] }),
          ...(styleMatch[7] && { [styleMatch[7]]: styleMatch[8] }),
        }
      : {};

    const itemsAndResponsesHtmlTable = createItemsAndResponsesHtmlTable(questionnaireResponse, styleOptions);

    const delimiter = styleMatch ? styleMatch[0] : '{QuestionnaireResponse.itemsAndResponses}';
    const parts = reportHtmlWithValues && reportHtmlWithValues.split(delimiter);
    const htmlReportParts =
      reportHtmlWithValues && reportHtmlWithValues.includes(delimiter)
        ? [parts[0], delimiter, parts[1]]
        : [reportHtmlWithValues];

    return (
      <Box display="block" width="100%" className="clearfix">
        {htmlReportParts &&
          htmlReportParts.map((htmlPart, index) => (
            <Box key={index} sx={{ display: 'block' }}>
              {htmlPart === delimiter ? (
                <Grid id="items-and-responses-wrapper" container sx={{ my: 3 }} className="clearfix">
                  <div dangerouslySetInnerHTML={{ __html: itemsAndResponsesHtmlTable }} />
                </Grid>
              ) : (
                <Box sx={{ width: '100%', my: 2, display: 'block' }}>
                  <div dangerouslySetInnerHTML={{ __html: htmlPart }} />
                </Box>
              )}
            </Box>
          ))}
      </Box>
    );
  };

  const htmlReportActiveExtension = QuestionnaireUtility.extractExtension(
    fhirQuestionnaire.extension,
    'htmltemplate-base64',
  );
  const htmlReportActive = (htmlReportActiveExtension && htmlReportActiveExtension.valueString) || false;

  return (
    <>
      <Grid sx={{ p: 1, pt: 0 }}>
        <Box
          id="QuestionnaireReportComponent"
          sx={{
            p: 4,
            '@page': { margin: '.4in 0in !important' },
            '@media print': {
              '@page': {
                margin: '14mm',
              },
            },
          }}
          ref={componentRef}
        >
          <Grid container>
            {htmlReportActive ? (
              <>
                {/* {demographic && (
                  <Box sx={{ width: '100%', mb: 1, display: 'none', displayPrint: 'block' }}>
                    {renderDemographics()}
                  </Box>
                )} */}
                {renderHtmlReport()}
              </>
            ) : (
              <>
                {renderHeader()}
                {/* {renderDemographics()} */}
                {renderScore()}
                {renderQuestions()}
              </>
            )}
          </Grid>
        </Box>
        {isCallbackPrint ? (
          <></>
        ) : (
          <Grid container>
            <Grid item xs={12} sx={{ pb: 3 }}>
              <Stack spacing={2} justifyContent="center" direction="row">
                {resultNextActionCallback === undefined ? (
                  <></>
                ) : (
                  <Button variant="contained" onClick={() => resultNextActionCallback()}>
                    {nextButtonText || 'Next'}
                  </Button>
                )}
                {hasPdfTemplate && viewPdfCallback !== undefined ? (
                  <Button variant="outlined" onClick={(event) => viewPdfCallback(event)}>
                    View PDF
                  </Button>
                ) : (
                  <></>
                )}

                {closePageCallback !== undefined ? (
                  <Button variant="contained" onClick={(event) => closePageCallback(event)}>
                    Close
                  </Button>
                ) : (
                  <></>
                )}
              </Stack>
            </Grid>
          </Grid>
        )}
      </Grid>
    </>
  );
};

export const getBrowserName = () => {
  if ((navigator.userAgent.indexOf('Opera') || navigator.userAgent.indexOf('OPR')) != -1) {
    return 'Opera';
  } else if (navigator.userAgent.indexOf('Edg') != -1) {
    return 'Edge';
  } else if (navigator.userAgent.indexOf('Chrome') != -1) {
    return 'Chrome';
  } else if (navigator.userAgent.indexOf('Safari') != -1) {
    return 'Safari';
  } else if (navigator.userAgent.indexOf('Firefox') != -1) {
    return 'Firefox';
  } else if (navigator.userAgent.indexOf('MSIE') != -1 || !!document.documentMode == true) {
    //IF IE > 10
    return 'IE';
  } else {
    return 'unknown';
  }
};

export const getPrintDownloadIconsConfiguration = () => {
  const browserName = getBrowserName();
  //default chrome
  let icons = {
    PrintIcon: Print,
    DownloadIcon: Download,
    downloadTooltip: 'Download',
    printTooltip: 'Print',
    order: 'download,print',
  };

  if (browserName === 'Edge') {
    icons.DownloadIcon = Save;
    icons.downloadTooltip = 'Save';
    icons.printTooltip = 'Print';
    icons.order = 'print,download';
  } else if (browserName === 'Firefox') {
    icons.DownloadIcon = Save;
    icons.downloadTooltip = 'Save';
    icons.order = 'print,download';
  }

  return icons;
};

export const ReportActionMenu = (props) => {
  const {
    showSaveIcon,
    saveToCambianAccountCallback,
    showPrintIcon,
    printReportCallback,
    showDownloadIcon,
    downloadReportCallback,
  } = props;
  const [anchorEl, setAnchorEl] = React.useState(null);
  const open = Boolean(anchorEl);
  const iconsAndConfiguration = getPrintDownloadIconsConfiguration();
  const { DownloadIcon, PrintIcon } = iconsAndConfiguration;

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const reportMenuItems = [
    {
      condition: showSaveIcon,
      onClick: () => saveToCambianAccountCallback(),
      icon: <img src={cambianLogo} alt="cambian_logo" width="23" height="23" />,
      tooltip: 'Save to your Cambian account',
      text: 'Save',
    },
    {
      condition: showPrintIcon,
      onClick: () => printReportCallback(),
      icon: <PrintIcon color="primary" />,
      tooltip: 'Print report',
      text: 'Print',
    },
    {
      condition: showDownloadIcon,
      onClick: () => downloadReportCallback(),
      icon: <DownloadIcon color="primary" />,
      tooltip: 'Download report PDF',
      text: 'Download',
    },
  ];

  return (
    <>
      {(showSaveIcon || showPrintIcon || showDownloadIcon) && (
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <IconButton
            id="basic-button"
            aria-controls={open ? 'basic-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            onClick={handleClick}
          >
            <MoreVert />
          </IconButton>
          <Menu
            id="basic-menu"
            anchorEl={anchorEl}
            open={open}
            onClose={handleClose}
            MenuListProps={{
              'aria-labelledby': 'basic-button',
            }}
          >
            {reportMenuItems.map(
              ({ condition, onClick, icon, tooltip, text }, index) =>
                condition && (
                  <MenuItem
                    key={index}
                    onClick={() => {
                      onClick();
                      handleClose();
                    }}
                  >
                    <ListItemIcon>{icon}</ListItemIcon>
                    <ListItemText>
                      <Tooltip title={tooltip}>{text}</Tooltip>
                    </ListItemText>
                  </MenuItem>
                ),
            )}
          </Menu>
        </Box>
      )}
    </>
  );
};
