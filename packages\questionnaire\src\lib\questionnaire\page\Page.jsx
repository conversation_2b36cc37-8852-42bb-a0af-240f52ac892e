import React from 'react';
import { <PERSON>, <PERSON><PERSON>, Divider, Stack } from '@mui/material';
import { SaveLater } from './SaveLater';
import * as QuestionnaireUtility from '../utility/questionnaireUtility';
import * as QuestionUtility from '../utility/questionUtility';
import { Question } from '../question';

function Page(props) {
  const {
    questionList: questionListProp,
    pageNumber,
    maxPages,
    isCancelConfirmationActive,
    questionnairePageAnsweredCallback,
    isSaveForLaterActive,
    handleProgress,
    // isLargeButtonEnabled,
    handleBackNavigation,
    previousButtonLabel,
    nextButtonLabel,
    submitButtonLabel,
    isNextPageVisible,
    questionnaireTitleRef,
    handleSaveForLaterRef,
  } = props;
  const [isComplete, setIsComplete] = React.useState(false);
  const [isValid, setIsValid] = React.useState(true);
  const [noVisibleComponent, setNoVisibleComponent] = React.useState(false);

  const [questionList, setQuestionList] = React.useState(structuredClone(questionListProp));
  const [questionMap] = React.useState(() => QuestionnaireUtility.buildQuestionMap(questionListProp, false));

  React.useEffect(() => {
    setQuestionList(structuredClone(questionListProp));
    setIsValid(isQuestionnaireValid());
    setIsPageComplete(checkQuestionnairePageComplete(questionList));
  }, [questionListProp]);

  const checkQuestionnairePageComplete = (questions) => {
    let isComplete = true;
    questions.forEach((question) => {
      if (
        QuestionnaireUtility.isRequired(question) &&
        question.isVisible &&
        !QuestionnaireUtility.isQuestionComplete(question)
      ) {
        isComplete = false;
      }
    });
    return isComplete;
  };

  React.useEffect(() => {
    if (handleSaveForLaterRef) {
      handleSaveForLaterRef.current = handleSaveForLater;
    }
  }, []);

  const isQuestionnaireValid = () => {
    let isValidQuestionnaire = true;
    questionList.forEach((question) => {
      if (question.type === 'complex') {
        question.item.forEach((subQuestion) => {
          if (!QuestionUtility.isQuestionValid(subQuestion)) {
            isValidQuestionnaire = false;
          }
        });
      } else if (!QuestionUtility.isQuestionValid(question)) {
        isValidQuestionnaire = false;
      }
    });
    return isValidQuestionnaire;
  };

  const [isPageComplete, setIsPageComplete] = React.useState(() => checkQuestionnairePageComplete(questionList));

  const handleQuestionResponse = (selectedAnswer) => {
    let questionId = selectedAnswer.question ? selectedAnswer.question.id : selectedAnswer.id;
    if (questionId !== undefined) {
      questionMap.set(questionId, selectedAnswer);
      if (selectedAnswer?.type === 'complex') {
        selectedAnswer?.item?.forEach((subQuestion) => {
          // use linkId for grid columns
          questionMap.set(subQuestion?.linkId, subQuestion);
        });
      }

      //handleProgress(questionMap);
      questionList.forEach((question) => {
        let questionEnableWhen = question.type === 'complex' ? question.enableWhen : question.question.enableWhen;

        if (questionEnableWhen && questionEnableWhen.length) {
          //reload page with updated data
          handleQuestionnairePageFinished({ pageNumber, questionMap, isFinished: false });
        }
      });
    }
    handleProgress(questionMap);
    setIsValid(isQuestionnaireValid());
    setIsPageComplete(checkQuestionnairePageComplete(questionList));
  };

  const handleQuestionnairePageFinished = ({
    pageNumber,
    questionMap,
    isFinished,
    isCancelled,
    isSavedForLater,
    isPageChanged,
  }) => {
    let errorMessage = questionnairePageAnsweredCallback(
      pageNumber,
      questionMap,
      isFinished,
      isCancelled,
      isSavedForLater,
      setNoVisibleComponent,
      isPageChanged,
    );
    let isError = errorMessage !== undefined;

    if (!isError && isFinished) {
      setIsComplete(true);
    } else if (isError) {
      console.log('packages/questionnaire/src/lib/questionnaire/page/Page.jsx', errorMessage);
    }
  };

  const handleSaveForLater = () => {
    console.log('handleSaveForLater');
    let errorMessage = questionnairePageAnsweredCallback(pageNumber, questionMap, false, false, true);
    let isError = errorMessage !== undefined;

    if (isError) {
      console.log('packages/questionnaire/src/lib/questionnaire/page/Page.jsx', errorMessage);
    }
  };

  const handleCancelled = () => {
    console.log('handleCancelled');
    questionnairePageAnsweredCallback(pageNumber, questionMap, false, true, false);
  };

  let isPageStatusComplete = false;
  isPageStatusComplete = checkQuestionnairePageComplete(questionList);
  if (isPageStatusComplete !== isPageComplete) {
    setIsPageComplete(isPageStatusComplete);
  }

  let lastVisibleIndex = 0;
  questionList.forEach((question, index) => {
    if (question.isVisible) {
      lastVisibleIndex = index;
    }
  });

  const scrollToTop = (isQuestionnaireFinished) => {
    setTimeout(() => {
      if (questionnaireTitleRef) {
        questionnaireTitleRef?.current?.scrollIntoView({ behavior: 'smooth' });
        window?.parent?.postMessage('ScrollIntoView', '*'); // post message to parent pages in iframe for scroll to top, ps: its not a callback
      }
    }, 1);
  };

  const renderQuestions = (question, index) => {
    if (question.type === 'complex') {
      return (
        <Box key={'question-container-' + question.id} sx={{ mt: 1.5 }}>
          <Box key={'question-component-' + question.id}>
            <Question
              question={question}
              handleQuestionResponse={handleQuestionResponse}
              // isLargeButtonEnabled={isLargeButtonEnabled}
            />
          </Box>
          {lastVisibleIndex !== index ? <Divider sx={{ mt: 1.5 }} /> : ''}
        </Box>
      );
    } else {
      return (
        <Box key={'question-container-' + question.question.id} sx={{ mt: index === 0 ? 0 : 1 }}>
          <Box key={'question-component-' + question.question.id}>
            <Question
              question={question}
              handleQuestionResponse={handleQuestionResponse}
              // isLargeButtonEnabled={isLargeButtonEnabled}
            />
          </Box>
          {lastVisibleIndex !== index ? <Divider sx={{ mt: 1.5 }} /> : ''}
        </Box>
      );
    }
  };

  return (
    <>
      <Box key="saveForLaterSection">
        <SaveLater
          isActive={isSaveForLaterActive}
          disabled={!isValid || isComplete}
          isCancelConfirmationActive={isCancelConfirmationActive}
          handleSaveForLater={() => handleSaveForLater()}
          handleCancelled={() => handleCancelled()}
        />
      </Box>
      <Box key="questionSection" sx={{ flexGrow: 1, p: 2, pt: isSaveForLaterActive ? 0 : 1, pb: 1 }}>
        {questionList.map((question, index) => {
          return question.isVisible ? renderQuestions(question, index) : null;
        })}
      </Box>
      <Box key="actionsSection" /* sx={{ minHeight: '50px' }} */>
        <Stack direction="row" alignItems="left" spacing={2} sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
          {pageNumber === 1 ? (
            handleBackNavigation ? (
              <Button variant="outlined" onClick={handleBackNavigation}>
                {previousButtonLabel}
              </Button>
            ) : (
              <></>
            )
          ) : (
            <Button
              disabled={isComplete || !isValid}
              variant="outlined"
              onClick={() => {
                handleQuestionnairePageFinished({
                  pageNumber: pageNumber - 1,
                  questionMap,
                  isFinished: false,
                  isPageChanged: true,
                });
                scrollToTop();
              }}
            >
              {previousButtonLabel}
            </Button>
          )}

          <Button
            disabled={!isValid || isComplete || !isPageComplete || noVisibleComponent}
            variant="contained"
            onClick={() => {
              const isQuestionnaireFinished = pageNumber === maxPages;
              handleQuestionnairePageFinished({
                pageNumber: pageNumber + 1,
                questionMap,
                isFinished: isQuestionnaireFinished,
                isPageChanged: true,
              });
              scrollToTop(isQuestionnaireFinished);
            }}
          >
            {pageNumber === maxPages || noVisibleComponent || !isNextPageVisible ? submitButtonLabel : nextButtonLabel}
          </Button>
        </Stack>
      </Box>
    </>
  );
}

export { Page };
