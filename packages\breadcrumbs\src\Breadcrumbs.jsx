'use client';
import React, { createContext, useContext, useState, useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

const BreadcrumbsContext = createContext();

export const useBreadcrumb = () => useContext(BreadcrumbsContext);

function addBreadcrumb(breadcrumbs, configMap, currentConfig, params, queryParams) {
  if (currentConfig.parent) {
    const parentConfig = configMap.get(currentConfig.parent);
    if (parentConfig) {
      const parentPath = resolvePath(parentConfig.path, params, queryParams);
      if (!breadcrumbs.some((b) => b.path === parentPath)) {
        addBreadcrumb(breadcrumbs, configMap, parentConfig, params, queryParams);
      }
    }
  }

  const resolvedPath = resolvePath(currentConfig.path, params, queryParams);
  if (!breadcrumbs.some((b) => b.path === resolvedPath)) {
    breadcrumbs.push({ path: resolvedPath, name: currentConfig.title });
  }
}

function resolvePath(template, params, queryParams) {
  let [basePath, queryString] = template.split('?');

  // Resolve path parameters
  basePath = basePath.replace(/:[^\s/]+/g, (match, index) => {
    const paramName = match.slice(1);
    if (paramName.startsWith('uuid')) {
      return params[paramName] || '';
    }
    return params[paramName] || '';
  });

  // Resolve query parameters
  if (queryString) {
    const queryParts = queryString.split('&');
    const resolvedQueryParts = queryParts.map((part) => {
      const [key, value] = part.split('=');
      if (value.startsWith(':')) {
        const paramName = value.slice(1);
        return `${key}=${params[paramName] || queryParams.get(key) || ''}`;
      }
      return part;
    });
    return `${basePath}?${resolvedQueryParts.join('&')}`;
  }

  return basePath;
}

export const BreadcrumbsProvider = ({ breadcrumbConfig, children }) => {
  const [breadcrumbList, setBreadcrumbList] = useState([]);
  const pathname = usePathname();
  const searchParams = useSearchParams();

  useEffect(() => {
    const generateBreadcrumbs = () => {
      const breadcrumbs = [];
      const configMap = new Map(breadcrumbConfig.map((config) => [config.path, config]));

      let bestMatch = null;
      let bestMatchScore = 0;

      for (const config of breadcrumbConfig) {
        let match;
        let params = {};
        let matchScore = 0;

        const [basePath, queryString] = config.path.split('?');
        const basePathParts = basePath.split('/');
        const pathnameParts = pathname.split('/');

        if (basePathParts.length === pathnameParts.length) {
          match = true;
          basePathParts.forEach((part, index) => {
            if (part.startsWith(':')) {
              const paramName = part.slice(1);
              params[paramName] = pathnameParts[index];
              matchScore++;
            } else if (part === pathnameParts[index]) {
              matchScore++;
            } else {
              match = false;
            }
          });

          if (match && queryString) {
            const queryParts = queryString.split('&');
            match = queryParts.every((part) => {
              const [key, value] = part.split('=');
              if (value.startsWith(':')) {
                const paramName = value.slice(1);
                params[paramName] = searchParams.get(key);
                return searchParams.has(key);
              }
              return searchParams.get(key) === value;
            });
            if (match) matchScore += queryParts.length;
          }
        }

        if (match && matchScore > bestMatchScore) {
          bestMatch = { config, params };
          bestMatchScore = matchScore;
        }
      }

      if (bestMatch) {
        addBreadcrumb(breadcrumbs, configMap, bestMatch.config, bestMatch.params, searchParams);
      }

      return breadcrumbs;
    };

    setBreadcrumbList(generateBreadcrumbs());
  }, [pathname, searchParams, breadcrumbConfig]);

  return <BreadcrumbsContext.Provider value={{ breadcrumbs: breadcrumbList }}>{children}</BreadcrumbsContext.Provider>;
};
