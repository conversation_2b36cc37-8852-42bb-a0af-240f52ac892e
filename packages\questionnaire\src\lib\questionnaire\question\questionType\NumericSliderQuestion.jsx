import React from 'react';
import { FormControl, <PERSON>rid, <PERSON>lider, TextField } from '@mui/material';
import { QuestionText } from '../QuestionText';
import { Explanation } from '../Explanation';
import * as QuestionnaireUtility from '../../utility/questionnaireUtility';
import { isNumberValue } from '../../utility/questionUtility';

function NumericSliderQuestion(props) {
  const { question, handleQuestionResponse } = props;
  const [isReadOnly, setIsReadOnly] = React.useState(() => handleQuestionResponse === undefined);

  const extractExistingAnswer = () => {
    if (typeof question.answer === 'object' && !Array.isArray(question.answer) && question.answer !== null) {
      const answer = question.answer.valueInteger;
      return answer;
    }
    return '';
  };

  const [value, setValue] = React.useState(() => extractExistingAnswer());

  const min = QuestionnaireUtility.extractExtension(question.question.extension, 'Item/slider-min-value').valueDecimal;
  const max = QuestionnaireUtility.extractExtension(question.question.extension, 'Item/slider-max-value').valueDecimal;
  const minLabel = QuestionnaireUtility.extractExtension(
    question.question.extension,
    'Item/slider-min-label',
  ).valueString;
  const maxLabel = QuestionnaireUtility.extractExtension(
    question.question.extension,
    'Item/slider-max-label',
  ).valueString;
  const markSize =
    QuestionnaireUtility.extractExtension(question.question.extension, 'Item/slider-steps')?.valueDecimal || 10;
  const step = 1;

  const marks = [];
  var last;
  if (max - min > markSize) {
    for (var i = min; i <= max; i = i + markSize) {
      let mark = { value: i, label: i.toString() };
      marks.push(mark);
      last = i;
    }
    if (last !== max) {
      let mark = { value: max, label: max.toString() };
      marks.push(mark);
    }
  } else {
    marks.push({ value: min, label: min.toString() });
    marks.push({ value: max, label: max.toString() });
  }

  const handleSliderChange = (event) => {
    if (!isReadOnly) {
      let sliderValue = event.target.value === '' ? Number(min) : Number(event.target.value);
      setValue(sliderValue);
      question.answer = {
        valueInteger: sliderValue,
      };
      handleQuestionResponse(question);
    }
  };

  const handleInputChange = (event) => {
    const newValue = event.target.value;
    if (!isReadOnly) {
      if (newValue === '' || newValue === '-' || newValue === '+') {
        setValue(newValue);
        question.answer = {
          valueInteger: null,
        };
        handleQuestionResponse(question);
        return;
      }

      if (!isNaN(Number(newValue)) && Number(newValue) >= Number(min) && Number(newValue) <= Number(max)) {
        setValue(Number(newValue));
        question.answer = {
          valueInteger: Number(newValue),
        };
        handleQuestionResponse(question);
      }
    }
  };

  const handleBlur = () => {
    if (value === '' || value === null) {
      return;
    }

    if (value < min) {
      setValue(min);
      question.answer = {
        valueInteger: min,
      };
    } else if (value > max) {
      setValue(max);
      question.answer = {
        valueInteger: max,
      };
    }
  };

  return (
    <>
      <QuestionText
        isRequired={question.question.required}
        question={question.question.text}
        extension={question.question.extension}
      />

      <Grid container>
        <Grid item xs={6}>
          <div dangerouslySetInnerHTML={{ __html: minLabel }} />
        </Grid>
        <Grid item xs={6} justifyContent="flex-end" display="flex">
          <div dangerouslySetInnerHTML={{ __html: maxLabel }} />
        </Grid>
        <Grid item xs={12} sx={{ px: 1 }}>
          <Slider
            size="small"
            aria-label="Numeric Slider"
            value={value !== '' ? value : min}
            step={step}
            marks={marks}
            min={min}
            max={max}
            onChange={handleSliderChange}
          />
        </Grid>
        <Grid item xs={12} sm={7}>
          <FormControl variant="outlined" fullWidth>
            <TextField
              id="outlined-adornment-integer"
              value={value}
              autoComplete="off"
              onChange={handleInputChange}
              onBlur={handleBlur}
              size="small"
              inputProps={{
                step: step,
                min: min,
                max: max,
                'aria-labelledby': 'input-slider',
              }}
            />
          </FormControl>
        </Grid>
      </Grid>
      <Explanation question={question} />
    </>
  );
}

export default NumericSliderQuestion;
